# Rozana E-commerce App

A Flutter e-commerce application built with clean architecture principles, BLoC state management, and secure environment-based configuration.

## Project Overview

Rozana is a modern e-commerce application built with Flutter. The app follows clean architecture principles to ensure maintainability, testability, and scalability. It features a comprehensive service layer, domain-driven design, BLoC state management, and secure environment-based configuration system.

## 🚀 Quick Start

### Prerequisites
- Flutter SDK: 3.5.2 (stable channel)
- Dart SDK: 3.5.2
- Android Studio / VS Code

### Installation & Running

1. **Clone the repository**
```bash
git clone <repository-url>
cd b2c-app
```

2. **Install dependencies**
```bash
flutter pub get
```

3. **Run the app**
```bash
# Development (default)
flutter run

# Staging
flutter run --dart-define=ENVIRONMENT=staging

# Production
flutter run --dart-define=ENVIRONMENT=production --release
```

## 🏗️ Architecture

The project follows a clean architecture approach with BLoC state management:

### 1. Presentation Layer
- **Screens**: User interface components with BLoC integration
- **Widgets**: Reusable UI components
- **BLoCs**: State management using flutter_bloc
- **Events & States**: Immutable state management with Freezed

### 2. Domain Layer
- **Entities**: Core business objects
- **Use Cases**: Application-specific business rules
- **Repositories (Interfaces)**: Abstract data source access

### 3. Data Layer
- **Repositories (Implementations)**: Concrete implementations
- **Models**: Data transfer objects with JSON serialization
- **Services**: API and data services

### 4. Core Layer
- **Configuration**: Environment-based secure configuration
- **Network**: API client with interceptors
- **Utils**: Utilities, constants, and helpers
- **Dependency Injection**: GetIt-based DI container

## 🔧 Environment Configuration

The app supports multiple environments with secure configuration management:

### Current Setup
All environments currently use the same configuration values:

| Environment | API URL | Purpose |
|-------------|---------|---------|
| **Development** | `https://oms-dev.rozana.tech/` | Daily development |
| **Staging** | `https://oms-dev.rozana.tech/` | Testing & QA |
| **Production** | `https://oms-dev.rozana.tech/` | Live app |

### Environment Variables
- **API_BASE_URL**: API endpoint URL
- **TYPESENSE_API_KEY**: Search service API key
- **FIREBASE_API_KEY**: Firebase configuration
- **AMPLITUDE_API_KEY**: Analytics API key
- **ENVIRONMENT**: Current environment (development/staging/production)

## 📁 Project Structure

```
lib/
├── main.dart                    # Application entry point
├── App/                         # App-level configuration
│   ├── app.dart                 # Main app widget
│   ├── app_config.dart          # App configuration
│   └── bloc/                    # App-level BLoC
├── core/                        # Core functionality
│   ├── config/                  # Environment configuration
│   │   └── environment_config.dart  # Secure environment setup
│   ├── dependency_injection/    # Dependency injection
│   │   ├── di_container.dart    # GetIt container
│   │   └── di_setup.dart        # DI setup
│   ├── network/                 # Network layer
│   │   ├── api_client.dart      # HTTP client
│   │   ├── api_endpoints.dart   # API endpoints
│   │   └── dio_interceptors.dart # Request/response interceptors
│   ├── services/                # Core services
│   │   ├── app_preferences_service.dart  # Local storage
│   │   ├── bloc_cleanup_service.dart     # Memory management
│   │   └── app_verification_service.dart # Auth verification
│   ├── theme/                   # App theming
│   │   ├── app_theme.dart       # Theme configuration
│   │   └── color_scheme.dart    # Color definitions
│   └── utils/                   # Utilities
│       ├── constants.dart       # App constants
│       └── logger.dart          # Logging utility
├── data/                        # Data layer
│   └── models/                  # Data models
├── features/                    # Feature modules
│   ├── auth/                    # Authentication
│   │   ├── bloc/                # Auth BLoCs
│   │   └── presentation/        # Auth screens
│   ├── cart/                    # Shopping cart
│   │   ├── bloc/                # Cart BLoC
│   │   └── presentation/        # Cart screens
│   ├── home/                    # Home feature
│   ├── products/                # Product catalog
│   ├── search/                  # Search functionality
│   │   └── services/            # Typesense integration
│   └── profile/                 # User profile
├── l10n/                        # Localization
│   ├── app_en.arb              # English translations
│   └── app_hi.arb              # Hindi translations
├── routes/                      # Navigation
│   └── app_router.dart          # GoRouter configuration
└── shared/                      # Shared components
    └── widgets/                 # Reusable widgets
```

## 🚀 Running the App

### Development Environment
```bash
# Default development run
flutter run

# With explicit environment
flutter run --dart-define=ENVIRONMENT=development

# With custom logging
flutter run \
  --dart-define=ENVIRONMENT=development \
  --dart-define=ENABLE_LOGGING=true

# Using build script
chmod +x scripts/build_dev.sh
./scripts/build_dev.sh
```

### Staging Environment
```bash
# Run in staging mode
flutter run --dart-define=ENVIRONMENT=staging

# Build staging APK
flutter build apk --dart-define=ENVIRONMENT=staging --release

# Using build script
chmod +x scripts/build_staging.sh
./scripts/build_staging.sh
```

### Production Environment
```bash
# Build production APK
flutter build apk \
  --dart-define=ENVIRONMENT=production \
  --release \
  --obfuscate \
  --split-debug-info=build/debug-info

# Using build script
chmod +x scripts/build_production.sh
./scripts/build_production.sh

# Install production build
adb install build/app/outputs/flutter-apk/app-release.apk
```

### Custom Configuration Override
```bash
# Override specific settings
flutter run \
  --dart-define=ENVIRONMENT=development \
  --dart-define=API_BASE_URL=https://localhost:3000 \
  --dart-define=APP_NAME="Rozana Local"
```

## 🔧 Build Scripts

Pre-configured build scripts are available in the `scripts/` directory:

- **`build_dev.sh`**: Development build with debugging enabled
- **`build_staging.sh`**: Staging build for testing
- **`build_production.sh`**: Production build with obfuscation

Make scripts executable:
```bash
chmod +x scripts/*.sh
```

## 🏗️ Key Features

### State Management
- **BLoC Pattern**: Using flutter_bloc for predictable state management
- **Freezed**: Immutable state and event classes
- **Memory Management**: Proper BLoC lifecycle and cleanup

### Security
- **Environment Configuration**: Secure API key management
- **No Hardcoded Secrets**: All sensitive data via environment variables
- **Obfuscated Production Builds**: Code protection for releases

### Localization
- **Multi-language Support**: English and Hindi
- **ARB Files**: Standard localization format
- **Dynamic Language Switching**: Runtime language changes

### Search & Discovery
- **Typesense Integration**: Fast, typo-tolerant search
- **Product Search**: Search across products, categories
- **Auto-suggestions**: Real-time search suggestions

### User Experience
- **Responsive Design**: Adaptive layouts for different screen sizes
- **Theme Support**: Light/dark mode with custom color schemes
- **Smooth Navigation**: GoRouter-based navigation
- **Loading States**: Shimmer effects and loading indicators

## 📱 Core Services

### Authentication
- **Firebase Auth**: Secure user authentication
- **OTP Verification**: SMS-based verification
- **Session Management**: Persistent login state
- **Auto-logout**: Security-based session timeout

### Data Management
- **Local Storage**: SharedPreferences for user data
- **Cart Persistence**: Local cart storage
- **Address Management**: User address CRUD operations
- **Order History**: Order tracking and history

### Network Layer
- **Dio HTTP Client**: Robust network communication
- **Interceptors**: Request/response logging and auth headers
- **Error Handling**: Comprehensive error management
- **Retry Logic**: Automatic retry for failed requests

## 🧪 Testing

### Current Test Structure
```bash
test/
├── widget_test.dart              # Basic widget tests
├── bloc_memory_management_test.dart  # Memory management tests
└── ...                          # Additional test files
```

### Running Tests
```bash
# Run all tests
flutter test

# Run specific test file
flutter test test/bloc_memory_management_test.dart

# Run tests with coverage
flutter test --coverage
```

## 📦 Dependencies

### Core Dependencies
```yaml
dependencies:
  flutter: sdk: flutter
  flutter_bloc: ^8.1.6           # State management
  freezed_annotation: ^2.4.4     # Code generation
  get_it: ^8.0.0                 # Dependency injection
  go_router: ^14.6.1             # Navigation
  dio: ^5.7.0                    # HTTP client
  shared_preferences: ^2.3.2     # Local storage
  typesense: ^0.5.0              # Search service
  firebase_core: ^3.6.0          # Firebase integration
  amplitude_flutter: ^3.16.2     # Analytics
```

### Development Dependencies
```yaml
dev_dependencies:
  flutter_test: sdk: flutter
  build_runner: ^2.4.13          # Code generation
  freezed: ^2.5.7                # Immutable classes
  json_annotation: ^4.9.0        # JSON serialization
  flutter_lints: ^5.0.0          # Linting rules
```

## 🔒 Security Features

### Environment Management
- **Compile-time Configuration**: Secure environment variable injection
- **No Runtime Secrets**: All secrets loaded at build time
- **Environment Validation**: Automatic validation of required configurations

### Memory Management
- **BLoC Cleanup**: Proper disposal of resources
- **Memory Leak Prevention**: Singleton BLoC reset functionality
- **User Data Isolation**: Clean separation between user sessions

### Build Security
- **Code Obfuscation**: Production builds are obfuscated
- **Debug Info Separation**: Debug symbols stored separately
- **Secure Signing**: Environment-based Android signing

## 🚀 Deployment

### Development Deployment
```bash
# Quick development build
flutter run

# Development APK
flutter build apk --dart-define=ENVIRONMENT=development --debug
```

### Staging Deployment
```bash
# Staging build for testing
./scripts/build_staging.sh

# Install on test devices
adb install build/app/outputs/flutter-apk/app-release.apk
```

### Production Deployment
```bash
# Production build
./scripts/build_production.sh

# Verify build
flutter analyze
flutter test

# Deploy to app stores
# (Follow platform-specific deployment guides)
```

## 📋 Environment Differences

| Feature | Development | Staging | Production |
|---------|-------------|---------|------------|
| **Logging** | ✅ Enabled | ❌ Disabled | ❌ Disabled |
| **Debug Mode** | ✅ Enabled | ❌ Disabled | ❌ Disabled |
| **Branch Logging** | ✅ Enabled | ❌ Disabled | ❌ Disabled |
| **Code Obfuscation** | ❌ Disabled | ✅ Enabled | ✅ Enabled |
| **API Configuration** | Same across all environments |
| **Firebase Project** | Same across all environments |
| **Analytics** | Same across all environments |

## 🔧 Development Setup

### IDE Configuration
1. **VS Code Extensions**:
   - Flutter
   - Dart
   - Bloc
   - GitLens

2. **Android Studio Plugins**:
   - Flutter
   - Dart
   - Bloc

### Code Generation
```bash
# Generate code for Freezed, JSON serialization
flutter packages pub run build_runner build

# Watch for changes
flutter packages pub run build_runner watch
```

### Debugging
```bash
# Run with debugging
flutter run --dart-define=ENVIRONMENT=development --dart-define=ENABLE_LOGGING=true

# Profile performance
flutter run --profile

# Analyze performance
flutter run --trace-startup --profile
```

## 📚 Documentation

- **[Security Guide](docs/SECURITY.md)**: Security configuration and best practices
- **[Memory Management](docs/MEMORY_MANAGEMENT.md)**: BLoC memory management improvements
- **[API Documentation](docs/API.md)**: API endpoints and integration
- **[Deployment Guide](docs/DEPLOYMENT.md)**: Deployment procedures

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Commit changes**: `git commit -m 'Add amazing feature'`
4. **Push to branch**: `git push origin feature/amazing-feature`
5. **Open a Pull Request**

### Code Standards
- Follow Flutter/Dart style guidelines
- Use BLoC pattern for state management
- Write tests for new features
- Update documentation as needed
- Use conventional commit messages

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation in the `docs/` directory

---

**Built with ❤️ using Flutter**
