export 'home_event.dart';
export 'home_state.dart';

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_branch_sdk/flutter_branch_sdk.dart';
import 'package:rozana/core/utils/logger.dart';
import '../../../../domain/entities/banner_entity.dart';
import '../../../../domain/entities/category_entity.dart';
import '../../../../domain/entities/product_entity.dart';
import '../../../../domain/usecases/get_banners_usecase.dart';
import '../../../../domain/usecases/get_categories_usecase.dart';
import '../../../../domain/usecases/get_products_usecase.dart';

import '../../../../routes/app_router.dart';
import 'home_event.dart';
import 'home_state.dart';

class HomeBloc extends Bloc<HomeEvent, HomeState> {
  final GetCategoriesUseCase _getCategoriesUseCase;
  final GetProductsUseCase _getProductsUseCase;
  final GetBannersUseCase _getBannersUseCase;

  ScrollController? scrollController;

  HomeBloc({
    required GetCategoriesUseCase getCategoriesUseCase,
    required GetProductsUseCase getProductsUseCase,
    required GetBannersUseCase getBannersUseCase,
  })  : _getCategoriesUseCase = getCategoriesUseCase,
        _getProductsUseCase = getProductsUseCase,
        _getBannersUseCase = getBannersUseCase,
        super(const HomeState.initial()) {
    scrollController = ScrollController();
    // Initial state is now a const factory constructor
    on<InitHome>(_onInitHome);
    on<UpdateScroll>(_onUpdateScroll);
    on<LoadHomeData>(_onLoadHomeData);
    on<UpdateHomeList>(_onUpdateHomeList);
    on<LoadDeepLink>(_onDeepLinkFound);
  }

  void _onInitHome(InitHome event, Emitter<HomeState> emit) async {
    try {
      _handleDeepLink();
      scrollController?.addListener(() {
        final scrolled = (scrollController?.offset ?? 0) > 60;
        if (scrolled != state.isScrolled) {
          add(UpdateScroll(scrolled));
        }
      });
      add(HomeEvent.loadHomeData());
    } catch (_) {}
  }

  void _onDeepLinkFound(LoadDeepLink event, Emitter<HomeState> emit) async {
    try {
      emit(HomeState.deepLink(
          isScrolled: state.isScrolled, route: event.route, args: event.args));
    } catch (_) {}
  }

  void _onUpdateScroll(UpdateScroll event, Emitter<HomeState> emit) {
    emit(state.copyWith(isScrolled: event.scroll));
  }

  Future<void> _onLoadHomeData(
    LoadHomeData event, // LoadHomeData is now a class, not just a type
    Emitter<HomeState> emit,
  ) async {
    try {
      // Start all futures without awaiting
      final categoriesFuture = _getCategoriesUseCase.execute();
      final trendingFuture =
          _getProductsUseCase.execute(query: 'all'); // change query later
      final mostPopularFuture =
          _getProductsUseCase.execute(query: 'popular'); // change query later
      final mostBoughtFuture =
          _getProductsUseCase.execute(query: ''); // change query later
      final bannersFuture = _getBannersUseCase.execute();

      // Keep current progressive data
      List<CategoryEntity>? categories;
      List<ProductEntity>? previouslyBought;
      List<ProductEntity>? mostPopular;
      List<ProductEntity>? mostBought;
      List<BannerEntity>? banners;

      categoriesFuture.then((value) {
        categories = value;
        add(HomeEvent.updateLoadedList(categories: categories ?? []));
      });

      trendingFuture.then((value) {
        previouslyBought = value;
        add(HomeEvent.updateLoadedList(
            previouslyBought: previouslyBought ?? []));
      });

      mostPopularFuture.then((value) {
        mostPopular = value;
        add(HomeEvent.updateLoadedList(mostPopular: mostPopular ?? []));
      });

      mostBoughtFuture.then((value) {
        mostBought = value;
        add(HomeEvent.updateLoadedList(mostBought: mostBought ?? []));
      });

      bannersFuture.then((value) {
        banners = value;
        add(HomeEvent.updateLoadedList(banners: banners ?? []));
      });
    } catch (e) {
      emit(HomeState.error(
          message: 'Failed to load home data: $e',
          isScrolled: state.isScrolled)); // Emit const factory constructor
    }
  }

  void _onUpdateHomeList(UpdateHomeList event, Emitter<HomeState> emit) {
    if (state is HomeLoaded) {
      HomeLoaded loadedState = state as HomeLoaded;
      emit(loadedState.copyWith(
        categories: event.categories ?? loadedState.categories,
        previouslyBought:
            event.previouslyBought ?? loadedState.previouslyBought,
        mostPopular: event.mostPopular ?? loadedState.mostPopular,
        mostBought: event.mostBought ?? loadedState.mostBought,
        banners: event.banners ?? loadedState.banners,
      ));
    } else {
      emit(HomeState.loaded(
          categories: event.categories,
          previouslyBought: event.previouslyBought,
          mostPopular: event.mostPopular,
          mostBought: event.mostBought,
          banners: event.banners,
          isScrolled: state.isScrolled));
    }
  }

  void _handleDeepLink() {
    FlutterBranchSdk.listSession().listen(
      (data) {
        if (data.isNotEmpty && (data['+clicked_branch_link'] == true)) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            switch (data['screen']) {
              case RouteNames.productDetail:
                Map<String, dynamic> arguments = {
                  'product': jsonDecode(data['product'])
                };
                add(HomeEvent.deepLinkFound(
                    RouteNames.productDetail, arguments));
                break;
            }
          });
        }
      },
      onError: (error) {
        if (error is PlatformException) {
          LogMessage.p('Branch SDK Error: ${error.code} - ${error.message}');
        } else {
          LogMessage.p('Unknown Branch SDK Error: $error');
        }
      },
    );
  }

  @override
  Future<void> close() {
    scrollController?.dispose();
    return super.close();
  }
}
