import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/features/cart/bloc/cart_bloc.dart';
import 'package:rozana/features/cart/bloc/cart_state.dart';
import 'package:rozana/features/cart/presentation/widgets/floating_cart_wrapper.dart';

import '../../../../core/themes/color_schemes.dart';
import '../../../../routes/app_router.dart';
import '../../bloc/home bloc/home_bloc.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key, required this.child});

  final Widget child;

  int _locationToIndex(String location) {
    if (location.startsWith(RouteNames.home)) return 0;
    if (location.startsWith(RouteNames.categories)) return 1;
    if (location.startsWith(RouteNames.cart)) return 2;
    if (location.startsWith(RouteNames.profile)) return 3;
    return 0;
  }

  void pushNewRoute(BuildContext context, int prev, String route) {
    if (prev == 0) {
      context.push(route);
    } else {
      context.replace(route);
    }
  }

  @override
  Widget build(BuildContext context) {
    String location = GoRouterState.of(context).uri.toString();
    // Check if current screen is cart screen to exclude floating cart button
    bool isCartScreen = location.startsWith(RouteNames.cart);

    return BlocListener<HomeBloc, HomeState>(
      listenWhen: (previous, current) => (current is HomeDeepLink),
      listener: (context, state) {
        state.mapOrNull(
          deepLink: (value) {
            context.push(value.route, extra: value.args);
          },
        );
      },
      child: Scaffold(
        body: FloatingCartWrapper(
          excludeFloatingCart: isCartScreen,
          bottomPadding: 0,
          child: child,
        ),
        // bottomNavigationBar: bottomNavigationBar(context),
        bottomNavigationBar: Container(
          decoration: BoxDecoration(boxShadow: [
            BoxShadow(
              offset: Offset(0, -4),
              blurRadius: 30,
              spreadRadius: 0,
              color: AppColors.shadowGrey,
            )
          ]),
          child: BottomNavigationBar(
            currentIndex: _locationToIndex(location),
            onTap: (index) {
              // Add haptic feedback
              HapticFeedback.lightImpact();

              int prevLocation = _locationToIndex(location);

              switch (index) {
                case 0:
                  context.go(RouteNames.home);
                  break;
                case 1:
                  pushNewRoute(context, prevLocation, RouteNames.categories);
                  break;
                case 2:
                  pushNewRoute(context, prevLocation, RouteNames.cart);
                  break;
                case 3:
                  pushNewRoute(context, prevLocation, RouteNames.profile);
                  break;
              }
            },
            type: BottomNavigationBarType.fixed,
            backgroundColor: AppColors.surface,
            elevation: 10,
            selectedItemColor: AppColors.primary,
            unselectedItemColor: Colors.grey,
            selectedLabelStyle:
                const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
            unselectedLabelStyle:
                const TextStyle(fontSize: 12, fontWeight: FontWeight.w400),
            items: [
              BottomNavigationBarItem(
                icon: BNIcon(path: 'assets/icons/home_icon.png'),
                activeIcon:
                    BNIcon(isActive: true, path: 'assets/icons/home_icon.png'),
                label: 'Home',
              ),
              BottomNavigationBarItem(
                icon: BNIcon(path: 'assets/icons/categorie_icon.png'),
                activeIcon: BNIcon(
                    isActive: true, path: 'assets/icons/categorie_icon.png'),
                label: 'Categories',
              ),
              BottomNavigationBarItem(
                icon: BNIcon(path: 'assets/icons/order_icon.png'),
                activeIcon:
                    BNIcon(isActive: true, path: 'assets/icons/order_icon.png'),
                label: 'Order Again',
              ),
              BottomNavigationBarItem(
                icon: BNIcon(path: 'assets/icons/profile_icon.png'),
                activeIcon: BNIcon(
                    isActive: true, path: 'assets/icons/profile_icon.png'),
                label: 'Profile',
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class BNIcon extends StatelessWidget {
  const BNIcon({super.key, this.isActive = false, required this.path});
  final bool isActive;
  final String path;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 12, bottom: 8),
      child: Center(
        child: Image.asset(path,
            width: 24,
            color: isActive ? AppColors.primary : AppColors.textGrey),
      ),
    );
  }
}

class CartBNButton extends StatelessWidget {
  const CartBNButton({super.key, required this.isActive});

  final bool isActive;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CartBloc, CartState>(
      builder: (context, state) {
        final itemCount = state.cart.totalItems;

        return Stack(
          clipBehavior: Clip.none,
          children: [
            Icon(
              isActive ? Icons.shopping_cart : Icons.shopping_cart_outlined,
            ),
            if (itemCount > 0)
              Positioned(
                top: -5,
                right: -5,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 1),
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 16,
                    minHeight: 16,
                  ),
                  child: Text(
                    itemCount > 9 ? '9+' : itemCount.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 8,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}
