import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/app/bloc/app_bloc.dart';
import 'package:rozana/core/services/user_profile_service.dart';
import 'package:rozana/core/utils/app_dimensions.dart';
import 'package:rozana/features/profile/presentation/widgets/profile_setup_modal.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:rozana/widgets/custom_text.dart';
import 'package:sms_autofill/sms_autofill.dart';

import '../../../../core/themes/color_schemes.dart';
import '../../../../widgets/custom_textfield.dart';
import '../../bloc/login_bloc/login_bloc.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen>
    with
        SingleTickerProviderStateMixin,
        AutomaticKeepAliveClientMixin<LoginScreen> {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  String? _returnRoute;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn,
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutCubic,
      ),
    );

    _animationController.forward();

    // Extract return route from navigation arguments and listen for auth changes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final Object? extra = GoRouterState.of(context).extra;
      if (extra != null && extra is Map<String, dynamic>) {
        _returnRoute = extra['returnRoute'] as String?;
      }

      final appBloc = context.read<AppBloc>();
      appBloc.stream.listen((state) {
        state.maybeMap(
            loaded: (loaded) {
              if (loaded.isAuthenticated && mounted) {
                // Check if profile setup is needed
                _checkAndShowProfileSetup();
              }
            },
            orElse: () {});
      });
    });
  }

  Future<void> _checkAndShowProfileSetup() async {
    try {
      final userProfileService = UserProfileService();
      final isProfileComplete = await userProfileService.isProfileComplete();

      if (!isProfileComplete && mounted) {
        // Show modal first while context is still mounted
        try {
          await showProfileSetupModal(context);

          // Navigate to destination regardless of modal result
          if (mounted) {
            _navigateToDestination();
          }
        } catch (modalError) {
          // Even if modal fails, still navigate to destination
          if (mounted) {
            _navigateToDestination();
          }
        }
      } else if (mounted) {
        // Profile is complete, navigate normally
        _navigateToDestination();
      }
    } catch (e) {
      // Error checking profile, navigate to destination anyway
      if (mounted) {
        _navigateToDestination();
      }
    }
  }

  void _navigateToDestination() {
    if (_returnRoute != null) {
      // Replace login screen with destination to avoid back to login
      context.pushReplacement(_returnRoute!);
    } else if (context.canPop()) {
      // Go back if possible
      context.pop();
    } else {
      // Default to home screen
      context.go(RouteNames.home);
    }
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        context.read<LoginBloc>().state.map(
          initial: (_) {
            if (context.canPop()) {
              context.pop();
            } else {
              context.go(RouteNames.home);
            }
          },
          otp: (_) {
            context.read<LoginBloc>().add(LoginEvent.loginFailed());
          },
        );
      },
      child: Scaffold(
        backgroundColor: theme.colorScheme.surface,
        body: SafeArea(
          child: SingleChildScrollView(
            child: Column(
              children: [
                SizedBox(
                  height: MediaQuery.of(context).size.height -
                      ((MediaQuery.of(context).viewPadding.top) +
                          (MediaQuery.of(context).viewPadding.bottom)),
                  child: Column(
                    children: [
                      SizedBox(
                        height: MediaQuery.of(context).size.height * 0.3,
                        child: Center(
                          child: Container(
                              height: 120,
                              width: 120,
                              decoration: BoxDecoration(
                                  color: theme.colorScheme.surface,
                                  borderRadius: BorderRadius.circular(24),
                                  boxShadow: [
                                    BoxShadow(
                                      color: AppColors.shadowGrey,
                                      blurRadius: 30,
                                    )
                                  ]),
                              child: Center(
                                child: Image.asset(
                                  'assets/images/Rozana_logo.webp',
                                ),
                              )),
                        ),
                      ),
                      SizedBox(
                        height: (MediaQuery.of(context).size.height * 0.7) -
                            (((MediaQuery.of(context).viewPadding.top) +
                                (MediaQuery.of(context).viewPadding.bottom))),
                        child: BlocBuilder<LoginBloc, LoginState>(
                          builder: (context, state) {
                            return Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: AppDimensions.screenHzPadding),
                              child: Column(
                                children: [
                                  const SizedBox(height: 32),
                                  FadeTransition(
                                    opacity: _fadeAnimation,
                                    child: SlideTransition(
                                      position: _slideAnimation,
                                      child: CustomText(
                                        state.map(
                                            initial: (_) => 'Welcome to Rozana',
                                            otp: (_) => 'Verify OTP'),
                                        fontSize: 24,
                                        fontWeight: FontWeight.w700,
                                        textHeight: 1,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(height: 10),
                                  FadeTransition(
                                    opacity: _fadeAnimation,
                                    child: SlideTransition(
                                      position: _slideAnimation,
                                      child: CustomText(
                                        state.map(
                                            initial: (_) =>
                                                'Groceries delivered in minutes',
                                            otp: (value) =>
                                                'Enter the verification code sent to +91 ${value.mobile}'),
                                        color: AppColors.textGrey,
                                      ),
                                    ),
                                  ),
                                  //
                                  Spacer(),
                                  SizedBox(height: 20),
                                  FadeTransition(
                                    opacity: _fadeAnimation,
                                    child: SlideTransition(
                                      position: _slideAnimation,
                                      child: CustomField(state: state),
                                    ),
                                  ),
                                  FadeTransition(
                                    opacity: _fadeAnimation,
                                    child: SlideTransition(
                                      position: _slideAnimation,
                                      child: SizedBox(
                                        width: double.infinity,
                                        height: 48,
                                        child: ElevatedButton(
                                          onPressed: () {
                                            if (state.isLoading) return;

                                            state.map(initial: (_) {
                                              // _sendOtp
                                              context.read<LoginBloc>().add(
                                                  LoginEvent.submitLogin());
                                            }, otp: (_) {
                                              // _verifyOtp
                                              context
                                                  .read<LoginBloc>()
                                                  .add(LoginEvent.submitOTP());
                                            });
                                          },
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor:
                                                AppColors.primaryAverage,
                                            foregroundColor:
                                                AppColors.primaryAverage,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            elevation: 0,
                                            padding: EdgeInsets.all(8),
                                          ),
                                          child: state.isLoading
                                              ? SizedBox(
                                                  width: 32,
                                                  child:
                                                      CircularProgressIndicator(
                                                    color: AppColors.surface,
                                                  ),
                                                )
                                              : CustomText(
                                                  state.map(
                                                      initial: (_) =>
                                                          'Send OTP',
                                                      otp: (value) =>
                                                          'Verify & Proceed'),
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.bold,
                                                  color: AppColors.surface,
                                                ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  state.maybeMap(
                                    otp: (otpState) {
                                      return Column(children: [
                                        const SizedBox(height: 24),
                                        FadeTransition(
                                          opacity: _fadeAnimation,
                                          child: Center(
                                            child: TextButton(
                                              onPressed: () {
                                                state.mapOrNull(
                                                  otp: (value) {
                                                    if (value.canResend) {
                                                      context
                                                          .read<LoginBloc>()
                                                          .add(LoginEvent
                                                              .resendOTP());
                                                    }
                                                  },
                                                );
                                              },
                                              child: Text(
                                                state.mapOrNull(
                                                        otp: (value) => value
                                                                .canResend
                                                            ? 'Resend OTP'
                                                            : 'Resend OTP in ${value.resendSeconds} seconds') ??
                                                    '',
                                                style: TextStyle(
                                                  color: state.mapOrNull(
                                                          otp: (value) => value.canResend
                                                              ? theme
                                                                  .colorScheme
                                                                  .primary
                                                              : theme
                                                                  .colorScheme
                                                                  .onSurface
                                                                  .withValues(
                                                                      alpha:
                                                                          0.5)) ??
                                                      theme.colorScheme.primary,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ]);
                                    },
                                    orElse: () => SizedBox(),
                                  ),
                                  Spacer(),
                                  Spacer(),
                                  FadeTransition(
                                    opacity: _fadeAnimation,
                                    child: Center(
                                      child: Text(
                                        'By continuing, you agree to our',
                                        style:
                                            theme.textTheme.bodySmall?.copyWith(
                                          color: theme.colorScheme.onSurface
                                              .withValues(alpha: 0.6),
                                        ),
                                      ),
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  FadeTransition(
                                    opacity: _fadeAnimation,
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        TextButton(
                                          onPressed: () {},
                                          style: TextButton.styleFrom(
                                            padding: EdgeInsets.zero,
                                            minimumSize: const Size(0, 0),
                                            tapTargetSize: MaterialTapTargetSize
                                                .shrinkWrap,
                                          ),
                                          child: Text(
                                            'Terms of Service',
                                            style: theme.textTheme.bodySmall
                                                ?.copyWith(
                                              color: theme.colorScheme.primary,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                        Text(
                                          ' and ',
                                          style: theme.textTheme.bodySmall
                                              ?.copyWith(
                                            color: theme.colorScheme.onSurface
                                                .withValues(alpha: 0.6),
                                          ),
                                        ),
                                        TextButton(
                                          onPressed: () {},
                                          style: TextButton.styleFrom(
                                            padding: EdgeInsets.zero,
                                            minimumSize: const Size(0, 0),
                                            tapTargetSize: MaterialTapTargetSize
                                                .shrinkWrap,
                                          ),
                                          child: Text(
                                            'Privacy Policy',
                                            style: theme.textTheme.bodySmall
                                                ?.copyWith(
                                              color: theme.colorScheme.primary,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  SizedBox(height: 24),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class CustomField extends StatelessWidget {
  const CustomField({super.key, required this.state});

  final LoginState state;

  @override
  Widget build(BuildContext context) {
    return state.map(
        initial: (_) {
          return !kIsWeb
              ? GestureDetector(
                  onTap: () async {
                    try {
                      FocusManager.instance.primaryFocus?.unfocus();
                      await Future<void>.delayed(
                          const Duration(milliseconds: 100));
                      final phoneNumber = await SmsAutoFill().hint;
                      if (phoneNumber != null && phoneNumber.isNotEmpty) {
                        final digits =
                            phoneNumber.replaceAll(RegExp(r'\D'), '');
                        final last10Digits = digits.length > 10
                            ? digits.substring(digits.length - 10)
                            : digits;
                        // setState(() {
                        LoginBloc.mobileNumberManager?.text = last10Digits;
                        // });
                      }
                    } catch (e) {
                      debugPrint('Error getting phone hint: $e');
                    }
                  },
                  child: AbsorbPointer(
                    absorbing: false,
                    child: RegularPhoneField(),
                  ),
                )
              : RegularPhoneField();
        },
        otp: (_) => !kIsWeb
            ? OTPField(
                autoFill: true,
              )
            : OTPField());
  }
}

class OTPField extends StatelessWidget {
  const OTPField({super.key, this.autoFill = false});
  final bool autoFill;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
        valueListenable: LoginBloc.otpManager?.errorText ?? ValueNotifier(''),
        builder: (context, error, _) {
          return Titledfield(
            title: 'Verification Code',
            field: CustomTextField(
              controller: LoginBloc.otpManager?.controller,
              focusNode: LoginBloc.otpManager?.focusNode,
              keyboardType: TextInputType.number,
              maxLength: 6,
              hintText: '6-digit OTP',
              textInputAction: TextInputAction.done,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              decoration: InputDecoration(
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 18,
                ),
                errorText: error.isNotEmpty ? error : null,
              ),
              onChanged: autoFill
                  ? (value) {
                      if (value.length == 6) {
                        Future<void>.delayed(const Duration(milliseconds: 500),
                            () {
                          // ignore: use_build_context_synchronously
                          context.read<LoginBloc>().add(LoginEvent.submitOTP());
                        });
                      }
                    }
                  : null,
            ),
          );
        });
  }
}

class RegularPhoneField extends StatelessWidget {
  const RegularPhoneField({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return ValueListenableBuilder(
        valueListenable:
            LoginBloc.mobileNumberManager?.errorText ?? ValueNotifier(''),
        builder: (context, error, _) {
          return Titledfield(
            title: 'Phone Number',
            field: CustomTextField(
              controller: LoginBloc.mobileNumberManager?.controller,
              focusNode: LoginBloc.mobileNumberManager?.focusNode,
              keyboardType: TextInputType.phone,
              maxLength: 10,
              hintText: 'Enter your phone number',
              textInputAction: TextInputAction.done,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              decoration: InputDecoration(
                prefixIcon: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CustomText(
                        '+91',
                        fontWeight: FontWeight.w600,
                      ),
                      const SizedBox(width: 8),
                      SizedBox(
                        height: 24,
                        child: VerticalDivider(
                          width: 10,
                          thickness: 1,
                          color: theme.colorScheme.onSurface
                              .withValues(alpha: 0.2),
                        ),
                      ),
                    ],
                  ),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 18,
                ),
                errorText: error.isNotEmpty ? error : null,
              ),
            ),
          );
        });
  }
}
