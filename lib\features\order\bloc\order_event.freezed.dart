// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$OrderEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(String customerId, String status, int page,
            int pageSize, bool refresh)
        loadOrderHistory,
    required TResult Function() loadMoreOrders,
    required TResult Function(String orderId) loadOrderDetails,
    required TResult Function(String status) refreshOrderHistory,
    required TResult Function(String status) filterOrdersByStatus,
    required TResult Function(String orderId) cancelOrder,
    required TResult Function() clearOrderDetails,
    required TResult Function(String query) searchOrders,
    required TResult Function() reset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(String customerId, String status, int page, int pageSize,
            bool refresh)?
        loadOrderHistory,
    TResult? Function()? loadMoreOrders,
    TResult? Function(String orderId)? loadOrderDetails,
    TResult? Function(String status)? refreshOrderHistory,
    TResult? Function(String status)? filterOrdersByStatus,
    TResult? Function(String orderId)? cancelOrder,
    TResult? Function()? clearOrderDetails,
    TResult? Function(String query)? searchOrders,
    TResult? Function()? reset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(String customerId, String status, int page, int pageSize,
            bool refresh)?
        loadOrderHistory,
    TResult Function()? loadMoreOrders,
    TResult Function(String orderId)? loadOrderDetails,
    TResult Function(String status)? refreshOrderHistory,
    TResult Function(String status)? filterOrdersByStatus,
    TResult Function(String orderId)? cancelOrder,
    TResult Function()? clearOrderDetails,
    TResult Function(String query)? searchOrders,
    TResult Function()? reset,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(OrderInit value) init,
    required TResult Function(LoadOrderHistory value) loadOrderHistory,
    required TResult Function(LoadMoreOrders value) loadMoreOrders,
    required TResult Function(LoadOrderDetails value) loadOrderDetails,
    required TResult Function(RefreshOrderHistory value) refreshOrderHistory,
    required TResult Function(FilterOrdersByStatus value) filterOrdersByStatus,
    required TResult Function(CancelOrder value) cancelOrder,
    required TResult Function(ClearOrderDetails value) clearOrderDetails,
    required TResult Function(SearchOrders value) searchOrders,
    required TResult Function(ResetOrder value) reset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(OrderInit value)? init,
    TResult? Function(LoadOrderHistory value)? loadOrderHistory,
    TResult? Function(LoadMoreOrders value)? loadMoreOrders,
    TResult? Function(LoadOrderDetails value)? loadOrderDetails,
    TResult? Function(RefreshOrderHistory value)? refreshOrderHistory,
    TResult? Function(FilterOrdersByStatus value)? filterOrdersByStatus,
    TResult? Function(CancelOrder value)? cancelOrder,
    TResult? Function(ClearOrderDetails value)? clearOrderDetails,
    TResult? Function(SearchOrders value)? searchOrders,
    TResult? Function(ResetOrder value)? reset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(OrderInit value)? init,
    TResult Function(LoadOrderHistory value)? loadOrderHistory,
    TResult Function(LoadMoreOrders value)? loadMoreOrders,
    TResult Function(LoadOrderDetails value)? loadOrderDetails,
    TResult Function(RefreshOrderHistory value)? refreshOrderHistory,
    TResult Function(FilterOrdersByStatus value)? filterOrdersByStatus,
    TResult Function(CancelOrder value)? cancelOrder,
    TResult Function(ClearOrderDetails value)? clearOrderDetails,
    TResult Function(SearchOrders value)? searchOrders,
    TResult Function(ResetOrder value)? reset,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderEventCopyWith<$Res> {
  factory $OrderEventCopyWith(
          OrderEvent value, $Res Function(OrderEvent) then) =
      _$OrderEventCopyWithImpl<$Res, OrderEvent>;
}

/// @nodoc
class _$OrderEventCopyWithImpl<$Res, $Val extends OrderEvent>
    implements $OrderEventCopyWith<$Res> {
  _$OrderEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$OrderInitImplCopyWith<$Res> {
  factory _$$OrderInitImplCopyWith(
          _$OrderInitImpl value, $Res Function(_$OrderInitImpl) then) =
      __$$OrderInitImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$OrderInitImplCopyWithImpl<$Res>
    extends _$OrderEventCopyWithImpl<$Res, _$OrderInitImpl>
    implements _$$OrderInitImplCopyWith<$Res> {
  __$$OrderInitImplCopyWithImpl(
      _$OrderInitImpl _value, $Res Function(_$OrderInitImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$OrderInitImpl implements OrderInit {
  const _$OrderInitImpl();

  @override
  String toString() {
    return 'OrderEvent.init()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$OrderInitImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(String customerId, String status, int page,
            int pageSize, bool refresh)
        loadOrderHistory,
    required TResult Function() loadMoreOrders,
    required TResult Function(String orderId) loadOrderDetails,
    required TResult Function(String status) refreshOrderHistory,
    required TResult Function(String status) filterOrdersByStatus,
    required TResult Function(String orderId) cancelOrder,
    required TResult Function() clearOrderDetails,
    required TResult Function(String query) searchOrders,
    required TResult Function() reset,
  }) {
    return init();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(String customerId, String status, int page, int pageSize,
            bool refresh)?
        loadOrderHistory,
    TResult? Function()? loadMoreOrders,
    TResult? Function(String orderId)? loadOrderDetails,
    TResult? Function(String status)? refreshOrderHistory,
    TResult? Function(String status)? filterOrdersByStatus,
    TResult? Function(String orderId)? cancelOrder,
    TResult? Function()? clearOrderDetails,
    TResult? Function(String query)? searchOrders,
    TResult? Function()? reset,
  }) {
    return init?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(String customerId, String status, int page, int pageSize,
            bool refresh)?
        loadOrderHistory,
    TResult Function()? loadMoreOrders,
    TResult Function(String orderId)? loadOrderDetails,
    TResult Function(String status)? refreshOrderHistory,
    TResult Function(String status)? filterOrdersByStatus,
    TResult Function(String orderId)? cancelOrder,
    TResult Function()? clearOrderDetails,
    TResult Function(String query)? searchOrders,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (init != null) {
      return init();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(OrderInit value) init,
    required TResult Function(LoadOrderHistory value) loadOrderHistory,
    required TResult Function(LoadMoreOrders value) loadMoreOrders,
    required TResult Function(LoadOrderDetails value) loadOrderDetails,
    required TResult Function(RefreshOrderHistory value) refreshOrderHistory,
    required TResult Function(FilterOrdersByStatus value) filterOrdersByStatus,
    required TResult Function(CancelOrder value) cancelOrder,
    required TResult Function(ClearOrderDetails value) clearOrderDetails,
    required TResult Function(SearchOrders value) searchOrders,
    required TResult Function(ResetOrder value) reset,
  }) {
    return init(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(OrderInit value)? init,
    TResult? Function(LoadOrderHistory value)? loadOrderHistory,
    TResult? Function(LoadMoreOrders value)? loadMoreOrders,
    TResult? Function(LoadOrderDetails value)? loadOrderDetails,
    TResult? Function(RefreshOrderHistory value)? refreshOrderHistory,
    TResult? Function(FilterOrdersByStatus value)? filterOrdersByStatus,
    TResult? Function(CancelOrder value)? cancelOrder,
    TResult? Function(ClearOrderDetails value)? clearOrderDetails,
    TResult? Function(SearchOrders value)? searchOrders,
    TResult? Function(ResetOrder value)? reset,
  }) {
    return init?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(OrderInit value)? init,
    TResult Function(LoadOrderHistory value)? loadOrderHistory,
    TResult Function(LoadMoreOrders value)? loadMoreOrders,
    TResult Function(LoadOrderDetails value)? loadOrderDetails,
    TResult Function(RefreshOrderHistory value)? refreshOrderHistory,
    TResult Function(FilterOrdersByStatus value)? filterOrdersByStatus,
    TResult Function(CancelOrder value)? cancelOrder,
    TResult Function(ClearOrderDetails value)? clearOrderDetails,
    TResult Function(SearchOrders value)? searchOrders,
    TResult Function(ResetOrder value)? reset,
    required TResult orElse(),
  }) {
    if (init != null) {
      return init(this);
    }
    return orElse();
  }
}

abstract class OrderInit implements OrderEvent {
  const factory OrderInit() = _$OrderInitImpl;
}

/// @nodoc
abstract class _$$LoadOrderHistoryImplCopyWith<$Res> {
  factory _$$LoadOrderHistoryImplCopyWith(_$LoadOrderHistoryImpl value,
          $Res Function(_$LoadOrderHistoryImpl) then) =
      __$$LoadOrderHistoryImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {String customerId, String status, int page, int pageSize, bool refresh});
}

/// @nodoc
class __$$LoadOrderHistoryImplCopyWithImpl<$Res>
    extends _$OrderEventCopyWithImpl<$Res, _$LoadOrderHistoryImpl>
    implements _$$LoadOrderHistoryImplCopyWith<$Res> {
  __$$LoadOrderHistoryImplCopyWithImpl(_$LoadOrderHistoryImpl _value,
      $Res Function(_$LoadOrderHistoryImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? customerId = null,
    Object? status = null,
    Object? page = null,
    Object? pageSize = null,
    Object? refresh = null,
  }) {
    return _then(_$LoadOrderHistoryImpl(
      customerId: null == customerId
          ? _value.customerId
          : customerId // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      refresh: null == refresh
          ? _value.refresh
          : refresh // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$LoadOrderHistoryImpl implements LoadOrderHistory {
  const _$LoadOrderHistoryImpl(
      {this.customerId = 'customer_123',
      this.status = '',
      this.page = 0,
      this.pageSize = 10,
      this.refresh = false});

  @override
  @JsonKey()
  final String customerId;
  @override
  @JsonKey()
  final String status;
  @override
  @JsonKey()
  final int page;
  @override
  @JsonKey()
  final int pageSize;
  @override
  @JsonKey()
  final bool refresh;

  @override
  String toString() {
    return 'OrderEvent.loadOrderHistory(customerId: $customerId, status: $status, page: $page, pageSize: $pageSize, refresh: $refresh)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadOrderHistoryImpl &&
            (identical(other.customerId, customerId) ||
                other.customerId == customerId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.refresh, refresh) || other.refresh == refresh));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, customerId, status, page, pageSize, refresh);

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadOrderHistoryImplCopyWith<_$LoadOrderHistoryImpl> get copyWith =>
      __$$LoadOrderHistoryImplCopyWithImpl<_$LoadOrderHistoryImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(String customerId, String status, int page,
            int pageSize, bool refresh)
        loadOrderHistory,
    required TResult Function() loadMoreOrders,
    required TResult Function(String orderId) loadOrderDetails,
    required TResult Function(String status) refreshOrderHistory,
    required TResult Function(String status) filterOrdersByStatus,
    required TResult Function(String orderId) cancelOrder,
    required TResult Function() clearOrderDetails,
    required TResult Function(String query) searchOrders,
    required TResult Function() reset,
  }) {
    return loadOrderHistory(customerId, status, page, pageSize, refresh);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(String customerId, String status, int page, int pageSize,
            bool refresh)?
        loadOrderHistory,
    TResult? Function()? loadMoreOrders,
    TResult? Function(String orderId)? loadOrderDetails,
    TResult? Function(String status)? refreshOrderHistory,
    TResult? Function(String status)? filterOrdersByStatus,
    TResult? Function(String orderId)? cancelOrder,
    TResult? Function()? clearOrderDetails,
    TResult? Function(String query)? searchOrders,
    TResult? Function()? reset,
  }) {
    return loadOrderHistory?.call(customerId, status, page, pageSize, refresh);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(String customerId, String status, int page, int pageSize,
            bool refresh)?
        loadOrderHistory,
    TResult Function()? loadMoreOrders,
    TResult Function(String orderId)? loadOrderDetails,
    TResult Function(String status)? refreshOrderHistory,
    TResult Function(String status)? filterOrdersByStatus,
    TResult Function(String orderId)? cancelOrder,
    TResult Function()? clearOrderDetails,
    TResult Function(String query)? searchOrders,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (loadOrderHistory != null) {
      return loadOrderHistory(customerId, status, page, pageSize, refresh);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(OrderInit value) init,
    required TResult Function(LoadOrderHistory value) loadOrderHistory,
    required TResult Function(LoadMoreOrders value) loadMoreOrders,
    required TResult Function(LoadOrderDetails value) loadOrderDetails,
    required TResult Function(RefreshOrderHistory value) refreshOrderHistory,
    required TResult Function(FilterOrdersByStatus value) filterOrdersByStatus,
    required TResult Function(CancelOrder value) cancelOrder,
    required TResult Function(ClearOrderDetails value) clearOrderDetails,
    required TResult Function(SearchOrders value) searchOrders,
    required TResult Function(ResetOrder value) reset,
  }) {
    return loadOrderHistory(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(OrderInit value)? init,
    TResult? Function(LoadOrderHistory value)? loadOrderHistory,
    TResult? Function(LoadMoreOrders value)? loadMoreOrders,
    TResult? Function(LoadOrderDetails value)? loadOrderDetails,
    TResult? Function(RefreshOrderHistory value)? refreshOrderHistory,
    TResult? Function(FilterOrdersByStatus value)? filterOrdersByStatus,
    TResult? Function(CancelOrder value)? cancelOrder,
    TResult? Function(ClearOrderDetails value)? clearOrderDetails,
    TResult? Function(SearchOrders value)? searchOrders,
    TResult? Function(ResetOrder value)? reset,
  }) {
    return loadOrderHistory?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(OrderInit value)? init,
    TResult Function(LoadOrderHistory value)? loadOrderHistory,
    TResult Function(LoadMoreOrders value)? loadMoreOrders,
    TResult Function(LoadOrderDetails value)? loadOrderDetails,
    TResult Function(RefreshOrderHistory value)? refreshOrderHistory,
    TResult Function(FilterOrdersByStatus value)? filterOrdersByStatus,
    TResult Function(CancelOrder value)? cancelOrder,
    TResult Function(ClearOrderDetails value)? clearOrderDetails,
    TResult Function(SearchOrders value)? searchOrders,
    TResult Function(ResetOrder value)? reset,
    required TResult orElse(),
  }) {
    if (loadOrderHistory != null) {
      return loadOrderHistory(this);
    }
    return orElse();
  }
}

abstract class LoadOrderHistory implements OrderEvent {
  const factory LoadOrderHistory(
      {final String customerId,
      final String status,
      final int page,
      final int pageSize,
      final bool refresh}) = _$LoadOrderHistoryImpl;

  String get customerId;
  String get status;
  int get page;
  int get pageSize;
  bool get refresh;

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadOrderHistoryImplCopyWith<_$LoadOrderHistoryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LoadMoreOrdersImplCopyWith<$Res> {
  factory _$$LoadMoreOrdersImplCopyWith(_$LoadMoreOrdersImpl value,
          $Res Function(_$LoadMoreOrdersImpl) then) =
      __$$LoadMoreOrdersImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadMoreOrdersImplCopyWithImpl<$Res>
    extends _$OrderEventCopyWithImpl<$Res, _$LoadMoreOrdersImpl>
    implements _$$LoadMoreOrdersImplCopyWith<$Res> {
  __$$LoadMoreOrdersImplCopyWithImpl(
      _$LoadMoreOrdersImpl _value, $Res Function(_$LoadMoreOrdersImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadMoreOrdersImpl implements LoadMoreOrders {
  const _$LoadMoreOrdersImpl();

  @override
  String toString() {
    return 'OrderEvent.loadMoreOrders()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadMoreOrdersImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(String customerId, String status, int page,
            int pageSize, bool refresh)
        loadOrderHistory,
    required TResult Function() loadMoreOrders,
    required TResult Function(String orderId) loadOrderDetails,
    required TResult Function(String status) refreshOrderHistory,
    required TResult Function(String status) filterOrdersByStatus,
    required TResult Function(String orderId) cancelOrder,
    required TResult Function() clearOrderDetails,
    required TResult Function(String query) searchOrders,
    required TResult Function() reset,
  }) {
    return loadMoreOrders();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(String customerId, String status, int page, int pageSize,
            bool refresh)?
        loadOrderHistory,
    TResult? Function()? loadMoreOrders,
    TResult? Function(String orderId)? loadOrderDetails,
    TResult? Function(String status)? refreshOrderHistory,
    TResult? Function(String status)? filterOrdersByStatus,
    TResult? Function(String orderId)? cancelOrder,
    TResult? Function()? clearOrderDetails,
    TResult? Function(String query)? searchOrders,
    TResult? Function()? reset,
  }) {
    return loadMoreOrders?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(String customerId, String status, int page, int pageSize,
            bool refresh)?
        loadOrderHistory,
    TResult Function()? loadMoreOrders,
    TResult Function(String orderId)? loadOrderDetails,
    TResult Function(String status)? refreshOrderHistory,
    TResult Function(String status)? filterOrdersByStatus,
    TResult Function(String orderId)? cancelOrder,
    TResult Function()? clearOrderDetails,
    TResult Function(String query)? searchOrders,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (loadMoreOrders != null) {
      return loadMoreOrders();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(OrderInit value) init,
    required TResult Function(LoadOrderHistory value) loadOrderHistory,
    required TResult Function(LoadMoreOrders value) loadMoreOrders,
    required TResult Function(LoadOrderDetails value) loadOrderDetails,
    required TResult Function(RefreshOrderHistory value) refreshOrderHistory,
    required TResult Function(FilterOrdersByStatus value) filterOrdersByStatus,
    required TResult Function(CancelOrder value) cancelOrder,
    required TResult Function(ClearOrderDetails value) clearOrderDetails,
    required TResult Function(SearchOrders value) searchOrders,
    required TResult Function(ResetOrder value) reset,
  }) {
    return loadMoreOrders(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(OrderInit value)? init,
    TResult? Function(LoadOrderHistory value)? loadOrderHistory,
    TResult? Function(LoadMoreOrders value)? loadMoreOrders,
    TResult? Function(LoadOrderDetails value)? loadOrderDetails,
    TResult? Function(RefreshOrderHistory value)? refreshOrderHistory,
    TResult? Function(FilterOrdersByStatus value)? filterOrdersByStatus,
    TResult? Function(CancelOrder value)? cancelOrder,
    TResult? Function(ClearOrderDetails value)? clearOrderDetails,
    TResult? Function(SearchOrders value)? searchOrders,
    TResult? Function(ResetOrder value)? reset,
  }) {
    return loadMoreOrders?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(OrderInit value)? init,
    TResult Function(LoadOrderHistory value)? loadOrderHistory,
    TResult Function(LoadMoreOrders value)? loadMoreOrders,
    TResult Function(LoadOrderDetails value)? loadOrderDetails,
    TResult Function(RefreshOrderHistory value)? refreshOrderHistory,
    TResult Function(FilterOrdersByStatus value)? filterOrdersByStatus,
    TResult Function(CancelOrder value)? cancelOrder,
    TResult Function(ClearOrderDetails value)? clearOrderDetails,
    TResult Function(SearchOrders value)? searchOrders,
    TResult Function(ResetOrder value)? reset,
    required TResult orElse(),
  }) {
    if (loadMoreOrders != null) {
      return loadMoreOrders(this);
    }
    return orElse();
  }
}

abstract class LoadMoreOrders implements OrderEvent {
  const factory LoadMoreOrders() = _$LoadMoreOrdersImpl;
}

/// @nodoc
abstract class _$$LoadOrderDetailsImplCopyWith<$Res> {
  factory _$$LoadOrderDetailsImplCopyWith(_$LoadOrderDetailsImpl value,
          $Res Function(_$LoadOrderDetailsImpl) then) =
      __$$LoadOrderDetailsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String orderId});
}

/// @nodoc
class __$$LoadOrderDetailsImplCopyWithImpl<$Res>
    extends _$OrderEventCopyWithImpl<$Res, _$LoadOrderDetailsImpl>
    implements _$$LoadOrderDetailsImplCopyWith<$Res> {
  __$$LoadOrderDetailsImplCopyWithImpl(_$LoadOrderDetailsImpl _value,
      $Res Function(_$LoadOrderDetailsImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderId = null,
  }) {
    return _then(_$LoadOrderDetailsImpl(
      null == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$LoadOrderDetailsImpl implements LoadOrderDetails {
  const _$LoadOrderDetailsImpl(this.orderId);

  @override
  final String orderId;

  @override
  String toString() {
    return 'OrderEvent.loadOrderDetails(orderId: $orderId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadOrderDetailsImpl &&
            (identical(other.orderId, orderId) || other.orderId == orderId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, orderId);

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadOrderDetailsImplCopyWith<_$LoadOrderDetailsImpl> get copyWith =>
      __$$LoadOrderDetailsImplCopyWithImpl<_$LoadOrderDetailsImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(String customerId, String status, int page,
            int pageSize, bool refresh)
        loadOrderHistory,
    required TResult Function() loadMoreOrders,
    required TResult Function(String orderId) loadOrderDetails,
    required TResult Function(String status) refreshOrderHistory,
    required TResult Function(String status) filterOrdersByStatus,
    required TResult Function(String orderId) cancelOrder,
    required TResult Function() clearOrderDetails,
    required TResult Function(String query) searchOrders,
    required TResult Function() reset,
  }) {
    return loadOrderDetails(orderId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(String customerId, String status, int page, int pageSize,
            bool refresh)?
        loadOrderHistory,
    TResult? Function()? loadMoreOrders,
    TResult? Function(String orderId)? loadOrderDetails,
    TResult? Function(String status)? refreshOrderHistory,
    TResult? Function(String status)? filterOrdersByStatus,
    TResult? Function(String orderId)? cancelOrder,
    TResult? Function()? clearOrderDetails,
    TResult? Function(String query)? searchOrders,
    TResult? Function()? reset,
  }) {
    return loadOrderDetails?.call(orderId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(String customerId, String status, int page, int pageSize,
            bool refresh)?
        loadOrderHistory,
    TResult Function()? loadMoreOrders,
    TResult Function(String orderId)? loadOrderDetails,
    TResult Function(String status)? refreshOrderHistory,
    TResult Function(String status)? filterOrdersByStatus,
    TResult Function(String orderId)? cancelOrder,
    TResult Function()? clearOrderDetails,
    TResult Function(String query)? searchOrders,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (loadOrderDetails != null) {
      return loadOrderDetails(orderId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(OrderInit value) init,
    required TResult Function(LoadOrderHistory value) loadOrderHistory,
    required TResult Function(LoadMoreOrders value) loadMoreOrders,
    required TResult Function(LoadOrderDetails value) loadOrderDetails,
    required TResult Function(RefreshOrderHistory value) refreshOrderHistory,
    required TResult Function(FilterOrdersByStatus value) filterOrdersByStatus,
    required TResult Function(CancelOrder value) cancelOrder,
    required TResult Function(ClearOrderDetails value) clearOrderDetails,
    required TResult Function(SearchOrders value) searchOrders,
    required TResult Function(ResetOrder value) reset,
  }) {
    return loadOrderDetails(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(OrderInit value)? init,
    TResult? Function(LoadOrderHistory value)? loadOrderHistory,
    TResult? Function(LoadMoreOrders value)? loadMoreOrders,
    TResult? Function(LoadOrderDetails value)? loadOrderDetails,
    TResult? Function(RefreshOrderHistory value)? refreshOrderHistory,
    TResult? Function(FilterOrdersByStatus value)? filterOrdersByStatus,
    TResult? Function(CancelOrder value)? cancelOrder,
    TResult? Function(ClearOrderDetails value)? clearOrderDetails,
    TResult? Function(SearchOrders value)? searchOrders,
    TResult? Function(ResetOrder value)? reset,
  }) {
    return loadOrderDetails?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(OrderInit value)? init,
    TResult Function(LoadOrderHistory value)? loadOrderHistory,
    TResult Function(LoadMoreOrders value)? loadMoreOrders,
    TResult Function(LoadOrderDetails value)? loadOrderDetails,
    TResult Function(RefreshOrderHistory value)? refreshOrderHistory,
    TResult Function(FilterOrdersByStatus value)? filterOrdersByStatus,
    TResult Function(CancelOrder value)? cancelOrder,
    TResult Function(ClearOrderDetails value)? clearOrderDetails,
    TResult Function(SearchOrders value)? searchOrders,
    TResult Function(ResetOrder value)? reset,
    required TResult orElse(),
  }) {
    if (loadOrderDetails != null) {
      return loadOrderDetails(this);
    }
    return orElse();
  }
}

abstract class LoadOrderDetails implements OrderEvent {
  const factory LoadOrderDetails(final String orderId) = _$LoadOrderDetailsImpl;

  String get orderId;

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadOrderDetailsImplCopyWith<_$LoadOrderDetailsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RefreshOrderHistoryImplCopyWith<$Res> {
  factory _$$RefreshOrderHistoryImplCopyWith(_$RefreshOrderHistoryImpl value,
          $Res Function(_$RefreshOrderHistoryImpl) then) =
      __$$RefreshOrderHistoryImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String status});
}

/// @nodoc
class __$$RefreshOrderHistoryImplCopyWithImpl<$Res>
    extends _$OrderEventCopyWithImpl<$Res, _$RefreshOrderHistoryImpl>
    implements _$$RefreshOrderHistoryImplCopyWith<$Res> {
  __$$RefreshOrderHistoryImplCopyWithImpl(_$RefreshOrderHistoryImpl _value,
      $Res Function(_$RefreshOrderHistoryImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
  }) {
    return _then(_$RefreshOrderHistoryImpl(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$RefreshOrderHistoryImpl implements RefreshOrderHistory {
  const _$RefreshOrderHistoryImpl({this.status = ''});

  @override
  @JsonKey()
  final String status;

  @override
  String toString() {
    return 'OrderEvent.refreshOrderHistory(status: $status)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RefreshOrderHistoryImpl &&
            (identical(other.status, status) || other.status == status));
  }

  @override
  int get hashCode => Object.hash(runtimeType, status);

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RefreshOrderHistoryImplCopyWith<_$RefreshOrderHistoryImpl> get copyWith =>
      __$$RefreshOrderHistoryImplCopyWithImpl<_$RefreshOrderHistoryImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(String customerId, String status, int page,
            int pageSize, bool refresh)
        loadOrderHistory,
    required TResult Function() loadMoreOrders,
    required TResult Function(String orderId) loadOrderDetails,
    required TResult Function(String status) refreshOrderHistory,
    required TResult Function(String status) filterOrdersByStatus,
    required TResult Function(String orderId) cancelOrder,
    required TResult Function() clearOrderDetails,
    required TResult Function(String query) searchOrders,
    required TResult Function() reset,
  }) {
    return refreshOrderHistory(status);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(String customerId, String status, int page, int pageSize,
            bool refresh)?
        loadOrderHistory,
    TResult? Function()? loadMoreOrders,
    TResult? Function(String orderId)? loadOrderDetails,
    TResult? Function(String status)? refreshOrderHistory,
    TResult? Function(String status)? filterOrdersByStatus,
    TResult? Function(String orderId)? cancelOrder,
    TResult? Function()? clearOrderDetails,
    TResult? Function(String query)? searchOrders,
    TResult? Function()? reset,
  }) {
    return refreshOrderHistory?.call(status);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(String customerId, String status, int page, int pageSize,
            bool refresh)?
        loadOrderHistory,
    TResult Function()? loadMoreOrders,
    TResult Function(String orderId)? loadOrderDetails,
    TResult Function(String status)? refreshOrderHistory,
    TResult Function(String status)? filterOrdersByStatus,
    TResult Function(String orderId)? cancelOrder,
    TResult Function()? clearOrderDetails,
    TResult Function(String query)? searchOrders,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (refreshOrderHistory != null) {
      return refreshOrderHistory(status);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(OrderInit value) init,
    required TResult Function(LoadOrderHistory value) loadOrderHistory,
    required TResult Function(LoadMoreOrders value) loadMoreOrders,
    required TResult Function(LoadOrderDetails value) loadOrderDetails,
    required TResult Function(RefreshOrderHistory value) refreshOrderHistory,
    required TResult Function(FilterOrdersByStatus value) filterOrdersByStatus,
    required TResult Function(CancelOrder value) cancelOrder,
    required TResult Function(ClearOrderDetails value) clearOrderDetails,
    required TResult Function(SearchOrders value) searchOrders,
    required TResult Function(ResetOrder value) reset,
  }) {
    return refreshOrderHistory(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(OrderInit value)? init,
    TResult? Function(LoadOrderHistory value)? loadOrderHistory,
    TResult? Function(LoadMoreOrders value)? loadMoreOrders,
    TResult? Function(LoadOrderDetails value)? loadOrderDetails,
    TResult? Function(RefreshOrderHistory value)? refreshOrderHistory,
    TResult? Function(FilterOrdersByStatus value)? filterOrdersByStatus,
    TResult? Function(CancelOrder value)? cancelOrder,
    TResult? Function(ClearOrderDetails value)? clearOrderDetails,
    TResult? Function(SearchOrders value)? searchOrders,
    TResult? Function(ResetOrder value)? reset,
  }) {
    return refreshOrderHistory?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(OrderInit value)? init,
    TResult Function(LoadOrderHistory value)? loadOrderHistory,
    TResult Function(LoadMoreOrders value)? loadMoreOrders,
    TResult Function(LoadOrderDetails value)? loadOrderDetails,
    TResult Function(RefreshOrderHistory value)? refreshOrderHistory,
    TResult Function(FilterOrdersByStatus value)? filterOrdersByStatus,
    TResult Function(CancelOrder value)? cancelOrder,
    TResult Function(ClearOrderDetails value)? clearOrderDetails,
    TResult Function(SearchOrders value)? searchOrders,
    TResult Function(ResetOrder value)? reset,
    required TResult orElse(),
  }) {
    if (refreshOrderHistory != null) {
      return refreshOrderHistory(this);
    }
    return orElse();
  }
}

abstract class RefreshOrderHistory implements OrderEvent {
  const factory RefreshOrderHistory({final String status}) =
      _$RefreshOrderHistoryImpl;

  String get status;

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RefreshOrderHistoryImplCopyWith<_$RefreshOrderHistoryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FilterOrdersByStatusImplCopyWith<$Res> {
  factory _$$FilterOrdersByStatusImplCopyWith(_$FilterOrdersByStatusImpl value,
          $Res Function(_$FilterOrdersByStatusImpl) then) =
      __$$FilterOrdersByStatusImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String status});
}

/// @nodoc
class __$$FilterOrdersByStatusImplCopyWithImpl<$Res>
    extends _$OrderEventCopyWithImpl<$Res, _$FilterOrdersByStatusImpl>
    implements _$$FilterOrdersByStatusImplCopyWith<$Res> {
  __$$FilterOrdersByStatusImplCopyWithImpl(_$FilterOrdersByStatusImpl _value,
      $Res Function(_$FilterOrdersByStatusImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
  }) {
    return _then(_$FilterOrdersByStatusImpl(
      null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$FilterOrdersByStatusImpl implements FilterOrdersByStatus {
  const _$FilterOrdersByStatusImpl(this.status);

  @override
  final String status;

  @override
  String toString() {
    return 'OrderEvent.filterOrdersByStatus(status: $status)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FilterOrdersByStatusImpl &&
            (identical(other.status, status) || other.status == status));
  }

  @override
  int get hashCode => Object.hash(runtimeType, status);

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FilterOrdersByStatusImplCopyWith<_$FilterOrdersByStatusImpl>
      get copyWith =>
          __$$FilterOrdersByStatusImplCopyWithImpl<_$FilterOrdersByStatusImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(String customerId, String status, int page,
            int pageSize, bool refresh)
        loadOrderHistory,
    required TResult Function() loadMoreOrders,
    required TResult Function(String orderId) loadOrderDetails,
    required TResult Function(String status) refreshOrderHistory,
    required TResult Function(String status) filterOrdersByStatus,
    required TResult Function(String orderId) cancelOrder,
    required TResult Function() clearOrderDetails,
    required TResult Function(String query) searchOrders,
    required TResult Function() reset,
  }) {
    return filterOrdersByStatus(status);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(String customerId, String status, int page, int pageSize,
            bool refresh)?
        loadOrderHistory,
    TResult? Function()? loadMoreOrders,
    TResult? Function(String orderId)? loadOrderDetails,
    TResult? Function(String status)? refreshOrderHistory,
    TResult? Function(String status)? filterOrdersByStatus,
    TResult? Function(String orderId)? cancelOrder,
    TResult? Function()? clearOrderDetails,
    TResult? Function(String query)? searchOrders,
    TResult? Function()? reset,
  }) {
    return filterOrdersByStatus?.call(status);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(String customerId, String status, int page, int pageSize,
            bool refresh)?
        loadOrderHistory,
    TResult Function()? loadMoreOrders,
    TResult Function(String orderId)? loadOrderDetails,
    TResult Function(String status)? refreshOrderHistory,
    TResult Function(String status)? filterOrdersByStatus,
    TResult Function(String orderId)? cancelOrder,
    TResult Function()? clearOrderDetails,
    TResult Function(String query)? searchOrders,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (filterOrdersByStatus != null) {
      return filterOrdersByStatus(status);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(OrderInit value) init,
    required TResult Function(LoadOrderHistory value) loadOrderHistory,
    required TResult Function(LoadMoreOrders value) loadMoreOrders,
    required TResult Function(LoadOrderDetails value) loadOrderDetails,
    required TResult Function(RefreshOrderHistory value) refreshOrderHistory,
    required TResult Function(FilterOrdersByStatus value) filterOrdersByStatus,
    required TResult Function(CancelOrder value) cancelOrder,
    required TResult Function(ClearOrderDetails value) clearOrderDetails,
    required TResult Function(SearchOrders value) searchOrders,
    required TResult Function(ResetOrder value) reset,
  }) {
    return filterOrdersByStatus(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(OrderInit value)? init,
    TResult? Function(LoadOrderHistory value)? loadOrderHistory,
    TResult? Function(LoadMoreOrders value)? loadMoreOrders,
    TResult? Function(LoadOrderDetails value)? loadOrderDetails,
    TResult? Function(RefreshOrderHistory value)? refreshOrderHistory,
    TResult? Function(FilterOrdersByStatus value)? filterOrdersByStatus,
    TResult? Function(CancelOrder value)? cancelOrder,
    TResult? Function(ClearOrderDetails value)? clearOrderDetails,
    TResult? Function(SearchOrders value)? searchOrders,
    TResult? Function(ResetOrder value)? reset,
  }) {
    return filterOrdersByStatus?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(OrderInit value)? init,
    TResult Function(LoadOrderHistory value)? loadOrderHistory,
    TResult Function(LoadMoreOrders value)? loadMoreOrders,
    TResult Function(LoadOrderDetails value)? loadOrderDetails,
    TResult Function(RefreshOrderHistory value)? refreshOrderHistory,
    TResult Function(FilterOrdersByStatus value)? filterOrdersByStatus,
    TResult Function(CancelOrder value)? cancelOrder,
    TResult Function(ClearOrderDetails value)? clearOrderDetails,
    TResult Function(SearchOrders value)? searchOrders,
    TResult Function(ResetOrder value)? reset,
    required TResult orElse(),
  }) {
    if (filterOrdersByStatus != null) {
      return filterOrdersByStatus(this);
    }
    return orElse();
  }
}

abstract class FilterOrdersByStatus implements OrderEvent {
  const factory FilterOrdersByStatus(final String status) =
      _$FilterOrdersByStatusImpl;

  String get status;

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FilterOrdersByStatusImplCopyWith<_$FilterOrdersByStatusImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CancelOrderImplCopyWith<$Res> {
  factory _$$CancelOrderImplCopyWith(
          _$CancelOrderImpl value, $Res Function(_$CancelOrderImpl) then) =
      __$$CancelOrderImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String orderId});
}

/// @nodoc
class __$$CancelOrderImplCopyWithImpl<$Res>
    extends _$OrderEventCopyWithImpl<$Res, _$CancelOrderImpl>
    implements _$$CancelOrderImplCopyWith<$Res> {
  __$$CancelOrderImplCopyWithImpl(
      _$CancelOrderImpl _value, $Res Function(_$CancelOrderImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderId = null,
  }) {
    return _then(_$CancelOrderImpl(
      null == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$CancelOrderImpl implements CancelOrder {
  const _$CancelOrderImpl(this.orderId);

  @override
  final String orderId;

  @override
  String toString() {
    return 'OrderEvent.cancelOrder(orderId: $orderId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CancelOrderImpl &&
            (identical(other.orderId, orderId) || other.orderId == orderId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, orderId);

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CancelOrderImplCopyWith<_$CancelOrderImpl> get copyWith =>
      __$$CancelOrderImplCopyWithImpl<_$CancelOrderImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(String customerId, String status, int page,
            int pageSize, bool refresh)
        loadOrderHistory,
    required TResult Function() loadMoreOrders,
    required TResult Function(String orderId) loadOrderDetails,
    required TResult Function(String status) refreshOrderHistory,
    required TResult Function(String status) filterOrdersByStatus,
    required TResult Function(String orderId) cancelOrder,
    required TResult Function() clearOrderDetails,
    required TResult Function(String query) searchOrders,
    required TResult Function() reset,
  }) {
    return cancelOrder(orderId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(String customerId, String status, int page, int pageSize,
            bool refresh)?
        loadOrderHistory,
    TResult? Function()? loadMoreOrders,
    TResult? Function(String orderId)? loadOrderDetails,
    TResult? Function(String status)? refreshOrderHistory,
    TResult? Function(String status)? filterOrdersByStatus,
    TResult? Function(String orderId)? cancelOrder,
    TResult? Function()? clearOrderDetails,
    TResult? Function(String query)? searchOrders,
    TResult? Function()? reset,
  }) {
    return cancelOrder?.call(orderId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(String customerId, String status, int page, int pageSize,
            bool refresh)?
        loadOrderHistory,
    TResult Function()? loadMoreOrders,
    TResult Function(String orderId)? loadOrderDetails,
    TResult Function(String status)? refreshOrderHistory,
    TResult Function(String status)? filterOrdersByStatus,
    TResult Function(String orderId)? cancelOrder,
    TResult Function()? clearOrderDetails,
    TResult Function(String query)? searchOrders,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (cancelOrder != null) {
      return cancelOrder(orderId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(OrderInit value) init,
    required TResult Function(LoadOrderHistory value) loadOrderHistory,
    required TResult Function(LoadMoreOrders value) loadMoreOrders,
    required TResult Function(LoadOrderDetails value) loadOrderDetails,
    required TResult Function(RefreshOrderHistory value) refreshOrderHistory,
    required TResult Function(FilterOrdersByStatus value) filterOrdersByStatus,
    required TResult Function(CancelOrder value) cancelOrder,
    required TResult Function(ClearOrderDetails value) clearOrderDetails,
    required TResult Function(SearchOrders value) searchOrders,
    required TResult Function(ResetOrder value) reset,
  }) {
    return cancelOrder(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(OrderInit value)? init,
    TResult? Function(LoadOrderHistory value)? loadOrderHistory,
    TResult? Function(LoadMoreOrders value)? loadMoreOrders,
    TResult? Function(LoadOrderDetails value)? loadOrderDetails,
    TResult? Function(RefreshOrderHistory value)? refreshOrderHistory,
    TResult? Function(FilterOrdersByStatus value)? filterOrdersByStatus,
    TResult? Function(CancelOrder value)? cancelOrder,
    TResult? Function(ClearOrderDetails value)? clearOrderDetails,
    TResult? Function(SearchOrders value)? searchOrders,
    TResult? Function(ResetOrder value)? reset,
  }) {
    return cancelOrder?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(OrderInit value)? init,
    TResult Function(LoadOrderHistory value)? loadOrderHistory,
    TResult Function(LoadMoreOrders value)? loadMoreOrders,
    TResult Function(LoadOrderDetails value)? loadOrderDetails,
    TResult Function(RefreshOrderHistory value)? refreshOrderHistory,
    TResult Function(FilterOrdersByStatus value)? filterOrdersByStatus,
    TResult Function(CancelOrder value)? cancelOrder,
    TResult Function(ClearOrderDetails value)? clearOrderDetails,
    TResult Function(SearchOrders value)? searchOrders,
    TResult Function(ResetOrder value)? reset,
    required TResult orElse(),
  }) {
    if (cancelOrder != null) {
      return cancelOrder(this);
    }
    return orElse();
  }
}

abstract class CancelOrder implements OrderEvent {
  const factory CancelOrder(final String orderId) = _$CancelOrderImpl;

  String get orderId;

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CancelOrderImplCopyWith<_$CancelOrderImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ClearOrderDetailsImplCopyWith<$Res> {
  factory _$$ClearOrderDetailsImplCopyWith(_$ClearOrderDetailsImpl value,
          $Res Function(_$ClearOrderDetailsImpl) then) =
      __$$ClearOrderDetailsImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ClearOrderDetailsImplCopyWithImpl<$Res>
    extends _$OrderEventCopyWithImpl<$Res, _$ClearOrderDetailsImpl>
    implements _$$ClearOrderDetailsImplCopyWith<$Res> {
  __$$ClearOrderDetailsImplCopyWithImpl(_$ClearOrderDetailsImpl _value,
      $Res Function(_$ClearOrderDetailsImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ClearOrderDetailsImpl implements ClearOrderDetails {
  const _$ClearOrderDetailsImpl();

  @override
  String toString() {
    return 'OrderEvent.clearOrderDetails()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ClearOrderDetailsImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(String customerId, String status, int page,
            int pageSize, bool refresh)
        loadOrderHistory,
    required TResult Function() loadMoreOrders,
    required TResult Function(String orderId) loadOrderDetails,
    required TResult Function(String status) refreshOrderHistory,
    required TResult Function(String status) filterOrdersByStatus,
    required TResult Function(String orderId) cancelOrder,
    required TResult Function() clearOrderDetails,
    required TResult Function(String query) searchOrders,
    required TResult Function() reset,
  }) {
    return clearOrderDetails();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(String customerId, String status, int page, int pageSize,
            bool refresh)?
        loadOrderHistory,
    TResult? Function()? loadMoreOrders,
    TResult? Function(String orderId)? loadOrderDetails,
    TResult? Function(String status)? refreshOrderHistory,
    TResult? Function(String status)? filterOrdersByStatus,
    TResult? Function(String orderId)? cancelOrder,
    TResult? Function()? clearOrderDetails,
    TResult? Function(String query)? searchOrders,
    TResult? Function()? reset,
  }) {
    return clearOrderDetails?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(String customerId, String status, int page, int pageSize,
            bool refresh)?
        loadOrderHistory,
    TResult Function()? loadMoreOrders,
    TResult Function(String orderId)? loadOrderDetails,
    TResult Function(String status)? refreshOrderHistory,
    TResult Function(String status)? filterOrdersByStatus,
    TResult Function(String orderId)? cancelOrder,
    TResult Function()? clearOrderDetails,
    TResult Function(String query)? searchOrders,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (clearOrderDetails != null) {
      return clearOrderDetails();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(OrderInit value) init,
    required TResult Function(LoadOrderHistory value) loadOrderHistory,
    required TResult Function(LoadMoreOrders value) loadMoreOrders,
    required TResult Function(LoadOrderDetails value) loadOrderDetails,
    required TResult Function(RefreshOrderHistory value) refreshOrderHistory,
    required TResult Function(FilterOrdersByStatus value) filterOrdersByStatus,
    required TResult Function(CancelOrder value) cancelOrder,
    required TResult Function(ClearOrderDetails value) clearOrderDetails,
    required TResult Function(SearchOrders value) searchOrders,
    required TResult Function(ResetOrder value) reset,
  }) {
    return clearOrderDetails(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(OrderInit value)? init,
    TResult? Function(LoadOrderHistory value)? loadOrderHistory,
    TResult? Function(LoadMoreOrders value)? loadMoreOrders,
    TResult? Function(LoadOrderDetails value)? loadOrderDetails,
    TResult? Function(RefreshOrderHistory value)? refreshOrderHistory,
    TResult? Function(FilterOrdersByStatus value)? filterOrdersByStatus,
    TResult? Function(CancelOrder value)? cancelOrder,
    TResult? Function(ClearOrderDetails value)? clearOrderDetails,
    TResult? Function(SearchOrders value)? searchOrders,
    TResult? Function(ResetOrder value)? reset,
  }) {
    return clearOrderDetails?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(OrderInit value)? init,
    TResult Function(LoadOrderHistory value)? loadOrderHistory,
    TResult Function(LoadMoreOrders value)? loadMoreOrders,
    TResult Function(LoadOrderDetails value)? loadOrderDetails,
    TResult Function(RefreshOrderHistory value)? refreshOrderHistory,
    TResult Function(FilterOrdersByStatus value)? filterOrdersByStatus,
    TResult Function(CancelOrder value)? cancelOrder,
    TResult Function(ClearOrderDetails value)? clearOrderDetails,
    TResult Function(SearchOrders value)? searchOrders,
    TResult Function(ResetOrder value)? reset,
    required TResult orElse(),
  }) {
    if (clearOrderDetails != null) {
      return clearOrderDetails(this);
    }
    return orElse();
  }
}

abstract class ClearOrderDetails implements OrderEvent {
  const factory ClearOrderDetails() = _$ClearOrderDetailsImpl;
}

/// @nodoc
abstract class _$$SearchOrdersImplCopyWith<$Res> {
  factory _$$SearchOrdersImplCopyWith(
          _$SearchOrdersImpl value, $Res Function(_$SearchOrdersImpl) then) =
      __$$SearchOrdersImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String query});
}

/// @nodoc
class __$$SearchOrdersImplCopyWithImpl<$Res>
    extends _$OrderEventCopyWithImpl<$Res, _$SearchOrdersImpl>
    implements _$$SearchOrdersImplCopyWith<$Res> {
  __$$SearchOrdersImplCopyWithImpl(
      _$SearchOrdersImpl _value, $Res Function(_$SearchOrdersImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? query = null,
  }) {
    return _then(_$SearchOrdersImpl(
      null == query
          ? _value.query
          : query // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$SearchOrdersImpl implements SearchOrders {
  const _$SearchOrdersImpl(this.query);

  @override
  final String query;

  @override
  String toString() {
    return 'OrderEvent.searchOrders(query: $query)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchOrdersImpl &&
            (identical(other.query, query) || other.query == query));
  }

  @override
  int get hashCode => Object.hash(runtimeType, query);

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchOrdersImplCopyWith<_$SearchOrdersImpl> get copyWith =>
      __$$SearchOrdersImplCopyWithImpl<_$SearchOrdersImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(String customerId, String status, int page,
            int pageSize, bool refresh)
        loadOrderHistory,
    required TResult Function() loadMoreOrders,
    required TResult Function(String orderId) loadOrderDetails,
    required TResult Function(String status) refreshOrderHistory,
    required TResult Function(String status) filterOrdersByStatus,
    required TResult Function(String orderId) cancelOrder,
    required TResult Function() clearOrderDetails,
    required TResult Function(String query) searchOrders,
    required TResult Function() reset,
  }) {
    return searchOrders(query);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(String customerId, String status, int page, int pageSize,
            bool refresh)?
        loadOrderHistory,
    TResult? Function()? loadMoreOrders,
    TResult? Function(String orderId)? loadOrderDetails,
    TResult? Function(String status)? refreshOrderHistory,
    TResult? Function(String status)? filterOrdersByStatus,
    TResult? Function(String orderId)? cancelOrder,
    TResult? Function()? clearOrderDetails,
    TResult? Function(String query)? searchOrders,
    TResult? Function()? reset,
  }) {
    return searchOrders?.call(query);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(String customerId, String status, int page, int pageSize,
            bool refresh)?
        loadOrderHistory,
    TResult Function()? loadMoreOrders,
    TResult Function(String orderId)? loadOrderDetails,
    TResult Function(String status)? refreshOrderHistory,
    TResult Function(String status)? filterOrdersByStatus,
    TResult Function(String orderId)? cancelOrder,
    TResult Function()? clearOrderDetails,
    TResult Function(String query)? searchOrders,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (searchOrders != null) {
      return searchOrders(query);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(OrderInit value) init,
    required TResult Function(LoadOrderHistory value) loadOrderHistory,
    required TResult Function(LoadMoreOrders value) loadMoreOrders,
    required TResult Function(LoadOrderDetails value) loadOrderDetails,
    required TResult Function(RefreshOrderHistory value) refreshOrderHistory,
    required TResult Function(FilterOrdersByStatus value) filterOrdersByStatus,
    required TResult Function(CancelOrder value) cancelOrder,
    required TResult Function(ClearOrderDetails value) clearOrderDetails,
    required TResult Function(SearchOrders value) searchOrders,
    required TResult Function(ResetOrder value) reset,
  }) {
    return searchOrders(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(OrderInit value)? init,
    TResult? Function(LoadOrderHistory value)? loadOrderHistory,
    TResult? Function(LoadMoreOrders value)? loadMoreOrders,
    TResult? Function(LoadOrderDetails value)? loadOrderDetails,
    TResult? Function(RefreshOrderHistory value)? refreshOrderHistory,
    TResult? Function(FilterOrdersByStatus value)? filterOrdersByStatus,
    TResult? Function(CancelOrder value)? cancelOrder,
    TResult? Function(ClearOrderDetails value)? clearOrderDetails,
    TResult? Function(SearchOrders value)? searchOrders,
    TResult? Function(ResetOrder value)? reset,
  }) {
    return searchOrders?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(OrderInit value)? init,
    TResult Function(LoadOrderHistory value)? loadOrderHistory,
    TResult Function(LoadMoreOrders value)? loadMoreOrders,
    TResult Function(LoadOrderDetails value)? loadOrderDetails,
    TResult Function(RefreshOrderHistory value)? refreshOrderHistory,
    TResult Function(FilterOrdersByStatus value)? filterOrdersByStatus,
    TResult Function(CancelOrder value)? cancelOrder,
    TResult Function(ClearOrderDetails value)? clearOrderDetails,
    TResult Function(SearchOrders value)? searchOrders,
    TResult Function(ResetOrder value)? reset,
    required TResult orElse(),
  }) {
    if (searchOrders != null) {
      return searchOrders(this);
    }
    return orElse();
  }
}

abstract class SearchOrders implements OrderEvent {
  const factory SearchOrders(final String query) = _$SearchOrdersImpl;

  String get query;

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SearchOrdersImplCopyWith<_$SearchOrdersImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ResetOrderImplCopyWith<$Res> {
  factory _$$ResetOrderImplCopyWith(
          _$ResetOrderImpl value, $Res Function(_$ResetOrderImpl) then) =
      __$$ResetOrderImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ResetOrderImplCopyWithImpl<$Res>
    extends _$OrderEventCopyWithImpl<$Res, _$ResetOrderImpl>
    implements _$$ResetOrderImplCopyWith<$Res> {
  __$$ResetOrderImplCopyWithImpl(
      _$ResetOrderImpl _value, $Res Function(_$ResetOrderImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ResetOrderImpl implements ResetOrder {
  const _$ResetOrderImpl();

  @override
  String toString() {
    return 'OrderEvent.reset()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ResetOrderImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(String customerId, String status, int page,
            int pageSize, bool refresh)
        loadOrderHistory,
    required TResult Function() loadMoreOrders,
    required TResult Function(String orderId) loadOrderDetails,
    required TResult Function(String status) refreshOrderHistory,
    required TResult Function(String status) filterOrdersByStatus,
    required TResult Function(String orderId) cancelOrder,
    required TResult Function() clearOrderDetails,
    required TResult Function(String query) searchOrders,
    required TResult Function() reset,
  }) {
    return reset();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(String customerId, String status, int page, int pageSize,
            bool refresh)?
        loadOrderHistory,
    TResult? Function()? loadMoreOrders,
    TResult? Function(String orderId)? loadOrderDetails,
    TResult? Function(String status)? refreshOrderHistory,
    TResult? Function(String status)? filterOrdersByStatus,
    TResult? Function(String orderId)? cancelOrder,
    TResult? Function()? clearOrderDetails,
    TResult? Function(String query)? searchOrders,
    TResult? Function()? reset,
  }) {
    return reset?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(String customerId, String status, int page, int pageSize,
            bool refresh)?
        loadOrderHistory,
    TResult Function()? loadMoreOrders,
    TResult Function(String orderId)? loadOrderDetails,
    TResult Function(String status)? refreshOrderHistory,
    TResult Function(String status)? filterOrdersByStatus,
    TResult Function(String orderId)? cancelOrder,
    TResult Function()? clearOrderDetails,
    TResult Function(String query)? searchOrders,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (reset != null) {
      return reset();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(OrderInit value) init,
    required TResult Function(LoadOrderHistory value) loadOrderHistory,
    required TResult Function(LoadMoreOrders value) loadMoreOrders,
    required TResult Function(LoadOrderDetails value) loadOrderDetails,
    required TResult Function(RefreshOrderHistory value) refreshOrderHistory,
    required TResult Function(FilterOrdersByStatus value) filterOrdersByStatus,
    required TResult Function(CancelOrder value) cancelOrder,
    required TResult Function(ClearOrderDetails value) clearOrderDetails,
    required TResult Function(SearchOrders value) searchOrders,
    required TResult Function(ResetOrder value) reset,
  }) {
    return reset(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(OrderInit value)? init,
    TResult? Function(LoadOrderHistory value)? loadOrderHistory,
    TResult? Function(LoadMoreOrders value)? loadMoreOrders,
    TResult? Function(LoadOrderDetails value)? loadOrderDetails,
    TResult? Function(RefreshOrderHistory value)? refreshOrderHistory,
    TResult? Function(FilterOrdersByStatus value)? filterOrdersByStatus,
    TResult? Function(CancelOrder value)? cancelOrder,
    TResult? Function(ClearOrderDetails value)? clearOrderDetails,
    TResult? Function(SearchOrders value)? searchOrders,
    TResult? Function(ResetOrder value)? reset,
  }) {
    return reset?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(OrderInit value)? init,
    TResult Function(LoadOrderHistory value)? loadOrderHistory,
    TResult Function(LoadMoreOrders value)? loadMoreOrders,
    TResult Function(LoadOrderDetails value)? loadOrderDetails,
    TResult Function(RefreshOrderHistory value)? refreshOrderHistory,
    TResult Function(FilterOrdersByStatus value)? filterOrdersByStatus,
    TResult Function(CancelOrder value)? cancelOrder,
    TResult Function(ClearOrderDetails value)? clearOrderDetails,
    TResult Function(SearchOrders value)? searchOrders,
    TResult Function(ResetOrder value)? reset,
    required TResult orElse(),
  }) {
    if (reset != null) {
      return reset(this);
    }
    return orElse();
  }
}

abstract class ResetOrder implements OrderEvent {
  const factory ResetOrder() = _$ResetOrderImpl;
}
