import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:rozana/core/config/environment_config.dart';

import 'package:rozana/core/services/app_preferences_service.dart';
import 'package:flutter_branch_sdk/flutter_branch_sdk.dart';
import 'package:amplitude_flutter/amplitude.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, TargetPlatform, kIsWeb;
import 'package:rozana/core/utils/logger.dart';
import 'package:rozana/core/services/app_verification_service.dart';

/// Application configuration settings
/// ⚠️ SECURITY: Now uses EnvironmentConfig for secure configuration management
class AppConfig {
  /// Current environment - now loaded from EnvironmentConfig
  static Environment get environment => EnvironmentConfig.environment;

  static Future<void> init() async {
    // Set system overlay style
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );

    // Set preferred orientations
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    await AppPreferences.init();

    // Validate environment configuration before proceeding
    EnvironmentConfig.validateConfiguration();

    //Branch sdk initialization
    await FlutterBranchSdk.init(
        enableLogging: EnvironmentConfig.branchLoggingEnabled,
        branchAttributionLevel: BranchAttributionLevel.FULL);

    // Initialize Amplitude for analytics
    final Amplitude amplitude = Amplitude.getInstance(instanceName: "default");

    amplitude.init(EnvironmentConfig.amplitudeApiKey);

    // Initialize Firebase (will use GoogleService-Info.plist on iOS)
    try {
      // Check if Firebase is already initialized to avoid duplicate app error
      if (Firebase.apps.isEmpty) {
        await Firebase.initializeApp(
          options: kIsWeb || defaultTargetPlatform == TargetPlatform.android
              ? FirebaseOptions(
                  apiKey: EnvironmentConfig.firebaseApiKey,
                  authDomain: EnvironmentConfig.firebaseAuthDomain,
                  projectId: EnvironmentConfig.firebaseProjectId,
                  storageBucket: EnvironmentConfig.firebaseStorageBucket,
                  messagingSenderId:
                      EnvironmentConfig.firebaseMessagingSenderId,
                  appId: EnvironmentConfig.firebaseAppId,
                  measurementId: EnvironmentConfig.firebaseMeasurementId)
              : null,
        );
        LogMessage.p('Firebase initialized successfully', color: Colors.green);

        // Configure Firebase Auth verification settings
        final verificationService = AppVerificationService();
        verificationService.configureFirebaseAuth();
      } else {
        LogMessage.p('Firebase already initialized, skipping initialization',
            color: Colors.orange);
      }
    } catch (e) {
      LogMessage.p('Firebase initialization failed: $e', color: Colors.red);
      // Don't rethrow if it's a duplicate app error, as the app can still function
      if (!e.toString().contains('duplicate-app')) {
        rethrow;
      }
    }
  }

  /// Get the appropriate API base URL based on environment
  /// ⚠️ SECURITY: Now uses EnvironmentConfig for secure URL management
  static String get baseUrl =>
      EnvironmentConfig.omsBaseUrl; // Default to OMS for backward compatibility

  /// Get OMS (Order Management System) base URL
  static String get omsBaseUrl => EnvironmentConfig.omsBaseUrl;

  /// Get IMS (Inventory Management System) base URL
  static String get imsBaseUrl => EnvironmentConfig.imsBaseUrl;

  /// Get base URL for specific service
  /// [isIms] - true for IMS service, false for OMS service
  static String getServiceBaseUrl({required bool isIms}) {
    return EnvironmentConfig.getServiceBaseUrl(isIms: isIms);
  }
}
