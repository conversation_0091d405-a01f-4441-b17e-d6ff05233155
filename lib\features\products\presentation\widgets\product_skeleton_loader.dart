import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class ProductSkeletonLoader extends StatelessWidget {
  final bool useGridView;
  final bool showAsRow;
  final int gridCrossAxisCount;
  final double gridChildAspectRatio;
  final int itemCount;

  const ProductSkeletonLoader({
    super.key,
    this.useGridView = false,
    this.showAsRow = false,
    this.gridCrossAxisCount = 2,
    this.gridChildAspectRatio = 2 / 3,
    this.itemCount = 6,
  });

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: showAsRow
          ? _buildRowSkeleton(context)
          : useGridView
              ? _buildGridSkeleton(context)
              : _buildListSkeleton(context),
    );
  }

  Widget _buildRowSkeleton(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: itemCount,
      itemBuilder: (context, index) => Padding(
        padding: const EdgeInsets.only(bottom: 12),
        child: _buildCard(width: screenWidth - 32, height: 100),
      ),
    );
  }

  Widget _buildGridSkeleton(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final cardWidth = (screenWidth - (gridCrossAxisCount + 1) * 12) / gridCrossAxisCount;
    final cardHeight = cardWidth / gridChildAspectRatio;

    return GridView.builder(
      itemCount: itemCount,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: gridCrossAxisCount,
        childAspectRatio: gridChildAspectRatio,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemBuilder: (context, index) =>
          _buildCard(width: cardWidth, height: cardHeight),
    );
  }

  Widget _buildListSkeleton(BuildContext context) {
    return ListView.builder(
      itemCount: itemCount,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemBuilder: (context, index) => Padding(
        padding: const EdgeInsets.only(bottom: 12),
        child: _buildCard(width: double.infinity, height: 220),
      ),
    );
  }

  Widget _buildCard({required double width, required double height}) {
    return Container(
      width: width,
      height: height,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: height * 0.5,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          const SizedBox(height: 8),
          Container(
            height: 12,
            width: width * 0.6,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(height: 4),
          Container(
            height: 12,
            width: width * 0.4,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ],
      ),
    );
  }
}