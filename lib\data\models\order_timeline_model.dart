class OrderTimelineModel {
  String? status;
  String? timestamp;
  String? title;
  String? description;

  OrderTimelineModel({
    this.status,
    this.timestamp,
    this.title,
    this.description,
  });

  OrderTimelineModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    timestamp = json['timestamp'];
    title = json['title'];
    description = json['description'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['timestamp'] = timestamp;
    data['title'] = title;
    data['description'] = description;
    return data;
  }

  OrderTimelineModel copyWith({
    String? status,
    String? timestamp,
    String? title,
    String? description,
  }) {
    return OrderTimelineModel(
      status: status ?? this.status,
      timestamp: timestamp ?? this.timestamp,
      title: title ?? this.title,
      description: description ?? this.description,
    );
  }

  /// Get DateTime from timestamp string
  DateTime? get dateTime {
    if (timestamp == null) return null;
    try {
      return DateTime.parse(timestamp!);
    } catch (e) {
      return null;
    }
  }
}
