// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'home_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$HomeEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(bool scroll) updateScroll,
    required TResult Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners)
        updateLoadedList,
    required TResult Function() loadHomeData,
    required TResult Function(String route, Map<String, dynamic> args)
        deepLinkFound,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(bool scroll)? updateScroll,
    TResult? Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners)?
        updateLoadedList,
    TResult? Function()? loadHomeData,
    TResult? Function(String route, Map<String, dynamic> args)? deepLinkFound,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(bool scroll)? updateScroll,
    TResult Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners)?
        updateLoadedList,
    TResult Function()? loadHomeData,
    TResult Function(String route, Map<String, dynamic> args)? deepLinkFound,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(InitHome value) init,
    required TResult Function(UpdateScroll value) updateScroll,
    required TResult Function(UpdateHomeList value) updateLoadedList,
    required TResult Function(LoadHomeData value) loadHomeData,
    required TResult Function(LoadDeepLink value) deepLinkFound,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(InitHome value)? init,
    TResult? Function(UpdateScroll value)? updateScroll,
    TResult? Function(UpdateHomeList value)? updateLoadedList,
    TResult? Function(LoadHomeData value)? loadHomeData,
    TResult? Function(LoadDeepLink value)? deepLinkFound,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(InitHome value)? init,
    TResult Function(UpdateScroll value)? updateScroll,
    TResult Function(UpdateHomeList value)? updateLoadedList,
    TResult Function(LoadHomeData value)? loadHomeData,
    TResult Function(LoadDeepLink value)? deepLinkFound,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HomeEventCopyWith<$Res> {
  factory $HomeEventCopyWith(HomeEvent value, $Res Function(HomeEvent) then) =
      _$HomeEventCopyWithImpl<$Res, HomeEvent>;
}

/// @nodoc
class _$HomeEventCopyWithImpl<$Res, $Val extends HomeEvent>
    implements $HomeEventCopyWith<$Res> {
  _$HomeEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HomeEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitHomeImplCopyWith<$Res> {
  factory _$$InitHomeImplCopyWith(
          _$InitHomeImpl value, $Res Function(_$InitHomeImpl) then) =
      __$$InitHomeImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitHomeImplCopyWithImpl<$Res>
    extends _$HomeEventCopyWithImpl<$Res, _$InitHomeImpl>
    implements _$$InitHomeImplCopyWith<$Res> {
  __$$InitHomeImplCopyWithImpl(
      _$InitHomeImpl _value, $Res Function(_$InitHomeImpl) _then)
      : super(_value, _then);

  /// Create a copy of HomeEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitHomeImpl implements InitHome {
  const _$InitHomeImpl();

  @override
  String toString() {
    return 'HomeEvent.init()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitHomeImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(bool scroll) updateScroll,
    required TResult Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners)
        updateLoadedList,
    required TResult Function() loadHomeData,
    required TResult Function(String route, Map<String, dynamic> args)
        deepLinkFound,
  }) {
    return init();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(bool scroll)? updateScroll,
    TResult? Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners)?
        updateLoadedList,
    TResult? Function()? loadHomeData,
    TResult? Function(String route, Map<String, dynamic> args)? deepLinkFound,
  }) {
    return init?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(bool scroll)? updateScroll,
    TResult Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners)?
        updateLoadedList,
    TResult Function()? loadHomeData,
    TResult Function(String route, Map<String, dynamic> args)? deepLinkFound,
    required TResult orElse(),
  }) {
    if (init != null) {
      return init();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(InitHome value) init,
    required TResult Function(UpdateScroll value) updateScroll,
    required TResult Function(UpdateHomeList value) updateLoadedList,
    required TResult Function(LoadHomeData value) loadHomeData,
    required TResult Function(LoadDeepLink value) deepLinkFound,
  }) {
    return init(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(InitHome value)? init,
    TResult? Function(UpdateScroll value)? updateScroll,
    TResult? Function(UpdateHomeList value)? updateLoadedList,
    TResult? Function(LoadHomeData value)? loadHomeData,
    TResult? Function(LoadDeepLink value)? deepLinkFound,
  }) {
    return init?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(InitHome value)? init,
    TResult Function(UpdateScroll value)? updateScroll,
    TResult Function(UpdateHomeList value)? updateLoadedList,
    TResult Function(LoadHomeData value)? loadHomeData,
    TResult Function(LoadDeepLink value)? deepLinkFound,
    required TResult orElse(),
  }) {
    if (init != null) {
      return init(this);
    }
    return orElse();
  }
}

abstract class InitHome implements HomeEvent {
  const factory InitHome() = _$InitHomeImpl;
}

/// @nodoc
abstract class _$$UpdateScrollImplCopyWith<$Res> {
  factory _$$UpdateScrollImplCopyWith(
          _$UpdateScrollImpl value, $Res Function(_$UpdateScrollImpl) then) =
      __$$UpdateScrollImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool scroll});
}

/// @nodoc
class __$$UpdateScrollImplCopyWithImpl<$Res>
    extends _$HomeEventCopyWithImpl<$Res, _$UpdateScrollImpl>
    implements _$$UpdateScrollImplCopyWith<$Res> {
  __$$UpdateScrollImplCopyWithImpl(
      _$UpdateScrollImpl _value, $Res Function(_$UpdateScrollImpl) _then)
      : super(_value, _then);

  /// Create a copy of HomeEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? scroll = null,
  }) {
    return _then(_$UpdateScrollImpl(
      null == scroll
          ? _value.scroll
          : scroll // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$UpdateScrollImpl implements UpdateScroll {
  const _$UpdateScrollImpl(this.scroll);

  @override
  final bool scroll;

  @override
  String toString() {
    return 'HomeEvent.updateScroll(scroll: $scroll)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateScrollImpl &&
            (identical(other.scroll, scroll) || other.scroll == scroll));
  }

  @override
  int get hashCode => Object.hash(runtimeType, scroll);

  /// Create a copy of HomeEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateScrollImplCopyWith<_$UpdateScrollImpl> get copyWith =>
      __$$UpdateScrollImplCopyWithImpl<_$UpdateScrollImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(bool scroll) updateScroll,
    required TResult Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners)
        updateLoadedList,
    required TResult Function() loadHomeData,
    required TResult Function(String route, Map<String, dynamic> args)
        deepLinkFound,
  }) {
    return updateScroll(scroll);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(bool scroll)? updateScroll,
    TResult? Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners)?
        updateLoadedList,
    TResult? Function()? loadHomeData,
    TResult? Function(String route, Map<String, dynamic> args)? deepLinkFound,
  }) {
    return updateScroll?.call(scroll);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(bool scroll)? updateScroll,
    TResult Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners)?
        updateLoadedList,
    TResult Function()? loadHomeData,
    TResult Function(String route, Map<String, dynamic> args)? deepLinkFound,
    required TResult orElse(),
  }) {
    if (updateScroll != null) {
      return updateScroll(scroll);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(InitHome value) init,
    required TResult Function(UpdateScroll value) updateScroll,
    required TResult Function(UpdateHomeList value) updateLoadedList,
    required TResult Function(LoadHomeData value) loadHomeData,
    required TResult Function(LoadDeepLink value) deepLinkFound,
  }) {
    return updateScroll(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(InitHome value)? init,
    TResult? Function(UpdateScroll value)? updateScroll,
    TResult? Function(UpdateHomeList value)? updateLoadedList,
    TResult? Function(LoadHomeData value)? loadHomeData,
    TResult? Function(LoadDeepLink value)? deepLinkFound,
  }) {
    return updateScroll?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(InitHome value)? init,
    TResult Function(UpdateScroll value)? updateScroll,
    TResult Function(UpdateHomeList value)? updateLoadedList,
    TResult Function(LoadHomeData value)? loadHomeData,
    TResult Function(LoadDeepLink value)? deepLinkFound,
    required TResult orElse(),
  }) {
    if (updateScroll != null) {
      return updateScroll(this);
    }
    return orElse();
  }
}

abstract class UpdateScroll implements HomeEvent {
  const factory UpdateScroll(final bool scroll) = _$UpdateScrollImpl;

  bool get scroll;

  /// Create a copy of HomeEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateScrollImplCopyWith<_$UpdateScrollImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateHomeListImplCopyWith<$Res> {
  factory _$$UpdateHomeListImplCopyWith(_$UpdateHomeListImpl value,
          $Res Function(_$UpdateHomeListImpl) then) =
      __$$UpdateHomeListImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {List<CategoryEntity>? categories,
      List<ProductEntity>? previouslyBought,
      List<ProductEntity>? mostPopular,
      List<ProductEntity>? mostBought,
      List<BannerEntity>? banners});
}

/// @nodoc
class __$$UpdateHomeListImplCopyWithImpl<$Res>
    extends _$HomeEventCopyWithImpl<$Res, _$UpdateHomeListImpl>
    implements _$$UpdateHomeListImplCopyWith<$Res> {
  __$$UpdateHomeListImplCopyWithImpl(
      _$UpdateHomeListImpl _value, $Res Function(_$UpdateHomeListImpl) _then)
      : super(_value, _then);

  /// Create a copy of HomeEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? categories = freezed,
    Object? previouslyBought = freezed,
    Object? mostPopular = freezed,
    Object? mostBought = freezed,
    Object? banners = freezed,
  }) {
    return _then(_$UpdateHomeListImpl(
      categories: freezed == categories
          ? _value._categories
          : categories // ignore: cast_nullable_to_non_nullable
              as List<CategoryEntity>?,
      previouslyBought: freezed == previouslyBought
          ? _value._previouslyBought
          : previouslyBought // ignore: cast_nullable_to_non_nullable
              as List<ProductEntity>?,
      mostPopular: freezed == mostPopular
          ? _value._mostPopular
          : mostPopular // ignore: cast_nullable_to_non_nullable
              as List<ProductEntity>?,
      mostBought: freezed == mostBought
          ? _value._mostBought
          : mostBought // ignore: cast_nullable_to_non_nullable
              as List<ProductEntity>?,
      banners: freezed == banners
          ? _value._banners
          : banners // ignore: cast_nullable_to_non_nullable
              as List<BannerEntity>?,
    ));
  }
}

/// @nodoc

class _$UpdateHomeListImpl implements UpdateHomeList {
  const _$UpdateHomeListImpl(
      {final List<CategoryEntity>? categories,
      final List<ProductEntity>? previouslyBought,
      final List<ProductEntity>? mostPopular,
      final List<ProductEntity>? mostBought,
      final List<BannerEntity>? banners})
      : _categories = categories,
        _previouslyBought = previouslyBought,
        _mostPopular = mostPopular,
        _mostBought = mostBought,
        _banners = banners;

  final List<CategoryEntity>? _categories;
  @override
  List<CategoryEntity>? get categories {
    final value = _categories;
    if (value == null) return null;
    if (_categories is EqualUnmodifiableListView) return _categories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ProductEntity>? _previouslyBought;
  @override
  List<ProductEntity>? get previouslyBought {
    final value = _previouslyBought;
    if (value == null) return null;
    if (_previouslyBought is EqualUnmodifiableListView)
      return _previouslyBought;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ProductEntity>? _mostPopular;
  @override
  List<ProductEntity>? get mostPopular {
    final value = _mostPopular;
    if (value == null) return null;
    if (_mostPopular is EqualUnmodifiableListView) return _mostPopular;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ProductEntity>? _mostBought;
  @override
  List<ProductEntity>? get mostBought {
    final value = _mostBought;
    if (value == null) return null;
    if (_mostBought is EqualUnmodifiableListView) return _mostBought;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<BannerEntity>? _banners;
  @override
  List<BannerEntity>? get banners {
    final value = _banners;
    if (value == null) return null;
    if (_banners is EqualUnmodifiableListView) return _banners;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'HomeEvent.updateLoadedList(categories: $categories, previouslyBought: $previouslyBought, mostPopular: $mostPopular, mostBought: $mostBought, banners: $banners)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateHomeListImpl &&
            const DeepCollectionEquality()
                .equals(other._categories, _categories) &&
            const DeepCollectionEquality()
                .equals(other._previouslyBought, _previouslyBought) &&
            const DeepCollectionEquality()
                .equals(other._mostPopular, _mostPopular) &&
            const DeepCollectionEquality()
                .equals(other._mostBought, _mostBought) &&
            const DeepCollectionEquality().equals(other._banners, _banners));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_categories),
      const DeepCollectionEquality().hash(_previouslyBought),
      const DeepCollectionEquality().hash(_mostPopular),
      const DeepCollectionEquality().hash(_mostBought),
      const DeepCollectionEquality().hash(_banners));

  /// Create a copy of HomeEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateHomeListImplCopyWith<_$UpdateHomeListImpl> get copyWith =>
      __$$UpdateHomeListImplCopyWithImpl<_$UpdateHomeListImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(bool scroll) updateScroll,
    required TResult Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners)
        updateLoadedList,
    required TResult Function() loadHomeData,
    required TResult Function(String route, Map<String, dynamic> args)
        deepLinkFound,
  }) {
    return updateLoadedList(
        categories, previouslyBought, mostPopular, mostBought, banners);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(bool scroll)? updateScroll,
    TResult? Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners)?
        updateLoadedList,
    TResult? Function()? loadHomeData,
    TResult? Function(String route, Map<String, dynamic> args)? deepLinkFound,
  }) {
    return updateLoadedList?.call(
        categories, previouslyBought, mostPopular, mostBought, banners);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(bool scroll)? updateScroll,
    TResult Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners)?
        updateLoadedList,
    TResult Function()? loadHomeData,
    TResult Function(String route, Map<String, dynamic> args)? deepLinkFound,
    required TResult orElse(),
  }) {
    if (updateLoadedList != null) {
      return updateLoadedList(
          categories, previouslyBought, mostPopular, mostBought, banners);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(InitHome value) init,
    required TResult Function(UpdateScroll value) updateScroll,
    required TResult Function(UpdateHomeList value) updateLoadedList,
    required TResult Function(LoadHomeData value) loadHomeData,
    required TResult Function(LoadDeepLink value) deepLinkFound,
  }) {
    return updateLoadedList(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(InitHome value)? init,
    TResult? Function(UpdateScroll value)? updateScroll,
    TResult? Function(UpdateHomeList value)? updateLoadedList,
    TResult? Function(LoadHomeData value)? loadHomeData,
    TResult? Function(LoadDeepLink value)? deepLinkFound,
  }) {
    return updateLoadedList?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(InitHome value)? init,
    TResult Function(UpdateScroll value)? updateScroll,
    TResult Function(UpdateHomeList value)? updateLoadedList,
    TResult Function(LoadHomeData value)? loadHomeData,
    TResult Function(LoadDeepLink value)? deepLinkFound,
    required TResult orElse(),
  }) {
    if (updateLoadedList != null) {
      return updateLoadedList(this);
    }
    return orElse();
  }
}

abstract class UpdateHomeList implements HomeEvent {
  const factory UpdateHomeList(
      {final List<CategoryEntity>? categories,
      final List<ProductEntity>? previouslyBought,
      final List<ProductEntity>? mostPopular,
      final List<ProductEntity>? mostBought,
      final List<BannerEntity>? banners}) = _$UpdateHomeListImpl;

  List<CategoryEntity>? get categories;
  List<ProductEntity>? get previouslyBought;
  List<ProductEntity>? get mostPopular;
  List<ProductEntity>? get mostBought;
  List<BannerEntity>? get banners;

  /// Create a copy of HomeEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateHomeListImplCopyWith<_$UpdateHomeListImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LoadHomeDataImplCopyWith<$Res> {
  factory _$$LoadHomeDataImplCopyWith(
          _$LoadHomeDataImpl value, $Res Function(_$LoadHomeDataImpl) then) =
      __$$LoadHomeDataImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadHomeDataImplCopyWithImpl<$Res>
    extends _$HomeEventCopyWithImpl<$Res, _$LoadHomeDataImpl>
    implements _$$LoadHomeDataImplCopyWith<$Res> {
  __$$LoadHomeDataImplCopyWithImpl(
      _$LoadHomeDataImpl _value, $Res Function(_$LoadHomeDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of HomeEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadHomeDataImpl implements LoadHomeData {
  const _$LoadHomeDataImpl();

  @override
  String toString() {
    return 'HomeEvent.loadHomeData()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadHomeDataImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(bool scroll) updateScroll,
    required TResult Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners)
        updateLoadedList,
    required TResult Function() loadHomeData,
    required TResult Function(String route, Map<String, dynamic> args)
        deepLinkFound,
  }) {
    return loadHomeData();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(bool scroll)? updateScroll,
    TResult? Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners)?
        updateLoadedList,
    TResult? Function()? loadHomeData,
    TResult? Function(String route, Map<String, dynamic> args)? deepLinkFound,
  }) {
    return loadHomeData?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(bool scroll)? updateScroll,
    TResult Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners)?
        updateLoadedList,
    TResult Function()? loadHomeData,
    TResult Function(String route, Map<String, dynamic> args)? deepLinkFound,
    required TResult orElse(),
  }) {
    if (loadHomeData != null) {
      return loadHomeData();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(InitHome value) init,
    required TResult Function(UpdateScroll value) updateScroll,
    required TResult Function(UpdateHomeList value) updateLoadedList,
    required TResult Function(LoadHomeData value) loadHomeData,
    required TResult Function(LoadDeepLink value) deepLinkFound,
  }) {
    return loadHomeData(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(InitHome value)? init,
    TResult? Function(UpdateScroll value)? updateScroll,
    TResult? Function(UpdateHomeList value)? updateLoadedList,
    TResult? Function(LoadHomeData value)? loadHomeData,
    TResult? Function(LoadDeepLink value)? deepLinkFound,
  }) {
    return loadHomeData?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(InitHome value)? init,
    TResult Function(UpdateScroll value)? updateScroll,
    TResult Function(UpdateHomeList value)? updateLoadedList,
    TResult Function(LoadHomeData value)? loadHomeData,
    TResult Function(LoadDeepLink value)? deepLinkFound,
    required TResult orElse(),
  }) {
    if (loadHomeData != null) {
      return loadHomeData(this);
    }
    return orElse();
  }
}

abstract class LoadHomeData implements HomeEvent {
  const factory LoadHomeData() = _$LoadHomeDataImpl;
}

/// @nodoc
abstract class _$$LoadDeepLinkImplCopyWith<$Res> {
  factory _$$LoadDeepLinkImplCopyWith(
          _$LoadDeepLinkImpl value, $Res Function(_$LoadDeepLinkImpl) then) =
      __$$LoadDeepLinkImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String route, Map<String, dynamic> args});
}

/// @nodoc
class __$$LoadDeepLinkImplCopyWithImpl<$Res>
    extends _$HomeEventCopyWithImpl<$Res, _$LoadDeepLinkImpl>
    implements _$$LoadDeepLinkImplCopyWith<$Res> {
  __$$LoadDeepLinkImplCopyWithImpl(
      _$LoadDeepLinkImpl _value, $Res Function(_$LoadDeepLinkImpl) _then)
      : super(_value, _then);

  /// Create a copy of HomeEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? route = null,
    Object? args = null,
  }) {
    return _then(_$LoadDeepLinkImpl(
      null == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String,
      null == args
          ? _value._args
          : args // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc

class _$LoadDeepLinkImpl implements LoadDeepLink {
  const _$LoadDeepLinkImpl(this.route, final Map<String, dynamic> args)
      : _args = args;

  @override
  final String route;
  final Map<String, dynamic> _args;
  @override
  Map<String, dynamic> get args {
    if (_args is EqualUnmodifiableMapView) return _args;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_args);
  }

  @override
  String toString() {
    return 'HomeEvent.deepLinkFound(route: $route, args: $args)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadDeepLinkImpl &&
            (identical(other.route, route) || other.route == route) &&
            const DeepCollectionEquality().equals(other._args, _args));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, route, const DeepCollectionEquality().hash(_args));

  /// Create a copy of HomeEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadDeepLinkImplCopyWith<_$LoadDeepLinkImpl> get copyWith =>
      __$$LoadDeepLinkImplCopyWithImpl<_$LoadDeepLinkImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(bool scroll) updateScroll,
    required TResult Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners)
        updateLoadedList,
    required TResult Function() loadHomeData,
    required TResult Function(String route, Map<String, dynamic> args)
        deepLinkFound,
  }) {
    return deepLinkFound(route, args);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(bool scroll)? updateScroll,
    TResult? Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners)?
        updateLoadedList,
    TResult? Function()? loadHomeData,
    TResult? Function(String route, Map<String, dynamic> args)? deepLinkFound,
  }) {
    return deepLinkFound?.call(route, args);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(bool scroll)? updateScroll,
    TResult Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners)?
        updateLoadedList,
    TResult Function()? loadHomeData,
    TResult Function(String route, Map<String, dynamic> args)? deepLinkFound,
    required TResult orElse(),
  }) {
    if (deepLinkFound != null) {
      return deepLinkFound(route, args);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(InitHome value) init,
    required TResult Function(UpdateScroll value) updateScroll,
    required TResult Function(UpdateHomeList value) updateLoadedList,
    required TResult Function(LoadHomeData value) loadHomeData,
    required TResult Function(LoadDeepLink value) deepLinkFound,
  }) {
    return deepLinkFound(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(InitHome value)? init,
    TResult? Function(UpdateScroll value)? updateScroll,
    TResult? Function(UpdateHomeList value)? updateLoadedList,
    TResult? Function(LoadHomeData value)? loadHomeData,
    TResult? Function(LoadDeepLink value)? deepLinkFound,
  }) {
    return deepLinkFound?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(InitHome value)? init,
    TResult Function(UpdateScroll value)? updateScroll,
    TResult Function(UpdateHomeList value)? updateLoadedList,
    TResult Function(LoadHomeData value)? loadHomeData,
    TResult Function(LoadDeepLink value)? deepLinkFound,
    required TResult orElse(),
  }) {
    if (deepLinkFound != null) {
      return deepLinkFound(this);
    }
    return orElse();
  }
}

abstract class LoadDeepLink implements HomeEvent {
  const factory LoadDeepLink(
      final String route, final Map<String, dynamic> args) = _$LoadDeepLinkImpl;

  String get route;
  Map<String, dynamic> get args;

  /// Create a copy of HomeEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadDeepLinkImplCopyWith<_$LoadDeepLinkImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
