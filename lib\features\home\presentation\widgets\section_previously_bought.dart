import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/utils/app_dimensions.dart';
import '../../../../domain/entities/product_entity.dart';
import '../../../../widgets/shimmer_widgets.dart';
import '../../bloc/home bloc/home_bloc.dart';
import 'product_grid.dart';

class PreviouslyBoughtSection extends StatelessWidget {
  const PreviouslyBoughtSection({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      buildWhen: (previous, current) {
        if (previous is! HomeLoaded) {
          return true;
        }
        if (current is HomeLoaded) {
          return previous.previouslyBought != current.previouslyBought;
        }
        return false; // Don’t rebuild for other transitions
      },
      builder: (context, state) {
        List<ProductEntity>? previouslyBought =
            state.mapOrNull(loaded: (value) => value.previouslyBought);
        return Visibility(
          visible: previouslyBought != null,
          replacement: Column(
            children: [
              Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: AppDimensions.screenHzPadding),
                  child: Shi<PERSON><PERSON><PERSON>(height: 80, radius: 20)),
              SizedBox(height: 20),
              ProductGrid(productList: null),
            ],
          ),
          child: Visibility(
              visible: previouslyBought?.isNotEmpty ?? false,
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: AppDimensions.screenHzPadding),
                    child: Image.asset(
                      'assets/images/buy_again.png',
                      width: double.infinity,
                    ),
                  ),
                  SizedBox(height: 20),
                  ProductGrid(productList: previouslyBought ?? []),
                ],
              )),
        );
      },
    );
  }
}
