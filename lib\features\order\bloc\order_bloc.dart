import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/domain/usecases/get_order_history_usecase.dart';
import 'package:rozana/domain/usecases/get_order_details_usecase.dart';
import 'package:rozana/domain/usecases/cancel_order_usecase.dart';
import 'package:rozana/domain/entities/order_entity.dart';

import 'order_event.dart';
import 'order_state.dart';

export 'order_event.dart';
export 'order_state.dart';

class OrderBloc extends Bloc<OrderEvent, OrderState> {
  final GetOrderHistoryUseCase _getOrderHistoryUseCase;
  final GetOrderDetailsUseCase _getOrderDetailsUseCase;
  final CancelOrderUseCase _cancelOrderUseCase;

  // Pagination and filtering state
  String _currentCustomerId = 'customer_123';
  String _currentFilter = '';
  String _currentSearchQuery = '';
  int _currentPage = 0;
  List<OrderEntity> _allOrders = [];
  bool _hasMoreData = true;

  OrderBloc({
    required GetOrderHistoryUseCase getOrderHistoryUseCase,
    required GetOrderDetailsUseCase getOrderDetailsUseCase,
    required CancelOrderUseCase cancelOrderUseCase,
  })  : _getOrderHistoryUseCase = getOrderHistoryUseCase,
        _getOrderDetailsUseCase = getOrderDetailsUseCase,
        _cancelOrderUseCase = cancelOrderUseCase,
        super(const OrderState.initial()) {
    on<OrderInit>(_onInit);
    on<LoadOrderHistory>(_onLoadOrderHistory);
    on<LoadMoreOrders>(_onLoadMoreOrders);
    on<LoadOrderDetails>(_onLoadOrderDetails);
    on<RefreshOrderHistory>(_onRefreshOrderHistory);
    on<FilterOrdersByStatus>(_onFilterOrdersByStatus);
    on<CancelOrder>(_onCancelOrder);
    on<ClearOrderDetails>(_onClearOrderDetails);
    on<SearchOrders>(_onSearchOrders);
    on<ResetOrder>(_onReset);
  }

  Future<void> _onInit(OrderInit event, Emitter<OrderState> emit) async {
    emit(const OrderState.loading());
    add(const OrderEvent.loadOrderHistory());
  }

  Future<void> _onLoadOrderHistory(
    LoadOrderHistory event,
    Emitter<OrderState> emit,
  ) async {
    try {
      if (event.page == 0) {
        emit(const OrderState.loading());
        _allOrders.clear();
        _currentPage = 0;
        _hasMoreData = true;
      }

      _currentCustomerId = event.customerId;
      _currentFilter = event.status;

      final orders = await _getOrderHistoryUseCase.call(
        status: event.status,
        page: event.page,
        pageSize: event.pageSize,
        refresh: event.refresh,
      );

      if (event.page == 0) {
        _allOrders = orders;
      } else {
        _allOrders.addAll(orders);
      }

      _currentPage = event.page;
      _hasMoreData = orders.length >= event.pageSize;

      if (_allOrders.isEmpty) {
        emit(OrderState.empty(
          filter: _currentFilter,
          searchQuery: _currentSearchQuery,
        ));
      } else {
        emit(OrderState.orderHistoryLoaded(
          orders: List.from(_allOrders),
          isLoadingMore: false,
          hasMoreData: _hasMoreData,
          currentPage: _currentPage,
          currentFilter: _currentFilter,
          searchQuery: _currentSearchQuery,
        ));
      }
    } catch (e) {
      emit(OrderState.error(message: e.toString()));
    }
  }

  Future<void> _onLoadMoreOrders(
    LoadMoreOrders event,
    Emitter<OrderState> emit,
  ) async {
    if (!_hasMoreData || state.isLoadingMore) return;

    try {
      // Update state to show loading more
      if (state is OrderHistoryLoaded) {
        final currentState = state as OrderHistoryLoaded;
        emit(currentState.copyWith(isLoadingMore: true));
      }

      final nextPage = _currentPage + 1;
      add(OrderEvent.loadOrderHistory(
        customerId: _currentCustomerId,
        status: _currentFilter,
        page: nextPage,
        pageSize: 10,
        refresh: false,
      ));
    } catch (e) {
      emit(OrderState.error(message: e.toString()));
    }
  }

  Future<void> _onLoadOrderDetails(
    LoadOrderDetails event,
    Emitter<OrderState> emit,
  ) async {
    try {
      emit(const OrderState.loading());

      final order = await _getOrderDetailsUseCase.call(event.orderId);

      if (order == null) {
        emit(OrderState.error(
          message: 'Order not found',
          orderId: event.orderId,
        ));
        return;
      }

      emit(OrderState.orderDetailsLoaded(order: order));
    } catch (e) {
      emit(OrderState.error(
        message: e.toString(),
        orderId: event.orderId,
      ));
    }
  }

  Future<void> _onRefreshOrderHistory(
    RefreshOrderHistory event,
    Emitter<OrderState> emit,
  ) async {
    _currentFilter = event.status;
    add(OrderEvent.loadOrderHistory(
      customerId: _currentCustomerId,
      status: event.status,
      page: 0,
      pageSize: 10,
      refresh: true,
    ));
  }

  Future<void> _onFilterOrdersByStatus(
    FilterOrdersByStatus event,
    Emitter<OrderState> emit,
  ) async {
    _currentFilter = event.status;
    _currentSearchQuery = '';
    add(OrderEvent.loadOrderHistory(
      customerId: _currentCustomerId,
      status: event.status,
      page: 0,
      pageSize: 10,
      refresh: false,
    ));
  }

  Future<void> _onCancelOrder(
    CancelOrder event,
    Emitter<OrderState> emit,
  ) async {
    try {
      final success = await _cancelOrderUseCase.call(event.orderId);

      if (success) {
        emit(OrderState.orderCancelled(
          orderId: event.orderId,
          message: 'Order cancelled successfully',
        ));

        // Refresh the order list
        add(OrderEvent.refreshOrderHistory(status: _currentFilter));
      } else {
        emit(OrderState.error(
          message: 'Failed to cancel order',
          orderId: event.orderId,
        ));
      }
    } catch (e) {
      emit(OrderState.error(
        message: e.toString(),
        orderId: event.orderId,
      ));
    }
  }

  Future<void> _onClearOrderDetails(
    ClearOrderDetails event,
    Emitter<OrderState> emit,
  ) async {
    if (_allOrders.isNotEmpty) {
      emit(OrderState.orderHistoryLoaded(
        orders: List.from(_allOrders),
        isLoadingMore: false,
        hasMoreData: _hasMoreData,
        currentPage: _currentPage,
        currentFilter: _currentFilter,
        searchQuery: _currentSearchQuery,
      ));
    } else {
      emit(const OrderState.initial());
    }
  }

  Future<void> _onSearchOrders(
    SearchOrders event,
    Emitter<OrderState> emit,
  ) async {
    _currentSearchQuery = event.query;

    if (event.query.isEmpty) {
      // If search query is empty, show all orders with current filter
      add(OrderEvent.filterOrdersByStatus(_currentFilter));
      return;
    }

    try {
      // Filter existing orders by search query
      final filteredOrders = _allOrders.where((order) {
        final query = event.query.toLowerCase();
        return order.id.toLowerCase().contains(query) ||
            order.items.any((item) => item.name.toLowerCase().contains(query));
      }).toList();

      if (filteredOrders.isEmpty) {
        emit(OrderState.empty(
          filter: _currentFilter,
          searchQuery: _currentSearchQuery,
        ));
      } else {
        emit(OrderState.orderHistoryLoaded(
          orders: filteredOrders,
          isLoadingMore: false,
          hasMoreData: false, // No pagination for search results
          currentPage: 0,
          currentFilter: _currentFilter,
          searchQuery: _currentSearchQuery,
        ));
      }
    } catch (e) {
      emit(OrderState.error(message: e.toString()));
    }
  }

  Future<void> _onReset(
    ResetOrder event,
    Emitter<OrderState> emit,
  ) async {
    _allOrders.clear();
    _currentPage = 0;
    _currentFilter = '';
    _currentSearchQuery = '';
    _hasMoreData = true;
    emit(const OrderState.initial());
  }
}
