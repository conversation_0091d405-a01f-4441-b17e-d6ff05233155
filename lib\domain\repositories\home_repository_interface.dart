import '../entities/banner_entity.dart';
import '../entities/category_entity.dart';
import '../entities/product_entity.dart';

/// Abstract interface for home repository
/// Defines the contract for home-related data operations
abstract class IHomeRepository {
  /// Get categories with pagination
  Future<List<CategoryEntity>> getCategories({
    int page = 0,
    int pageSize = 10,
    bool refresh = false,
    String query = '',
  });

  /// Get products with pagination
  Future<List<ProductEntity>> getProducts({
    int page = 0,
    int pageSize = 9,
    bool refresh = false,
    String query = '',
  });

  /// Get home banners
  Future<List<BannerEntity>> getBanners();
}
