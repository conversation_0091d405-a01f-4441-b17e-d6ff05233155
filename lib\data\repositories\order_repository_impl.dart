import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:rozana/core/network/service_api_client.dart';
import 'package:rozana/core/network/api_endpoints.dart';
import 'package:rozana/domain/repositories/order_repository_interface.dart';
import 'package:rozana/domain/entities/order_entity.dart';
import 'package:rozana/data/models/order_model.dart';
import 'package:rozana/data/mappers/order_mapper.dart';
import 'package:rozana/core/services/app_preferences_service.dart';

class OrderRepositoryImpl implements OrderRepositoryInterface {
  /// Get current user's customer ID from local storage
  String _getCurrentCustomerId() {
    try {
      // Get user data from SharedPreferences (where login services store it)
      final userDataJson = AppPreferences.getUserdata();
      if (userDataJson != null && userDataJson.isNotEmpty) {
        final userData = jsonDecode(userDataJson);
        final uid = userData['uid'];
        if (uid != null && uid.toString().isNotEmpty) {
          return uid.toString();
        }
      }

      // If no user data found, user is not authenticated
      throw Exception('No authenticated user found');
    } catch (e) {
      throw Exception('Failed to get customer ID: $e');
    }
  }

  @override
  Future<Response?> createOrder({
    required String customerId,
    required String customerName,
    required String facilityId,
    required String facilityName,
    required num totalAmount,
    required List<Map<String, dynamic>> items,
  }) async {
    final orderPayload = {
      'customer_id': customerId,
      'customer_name': customerName,
      'facility_id': facilityId,
      'facility_name': facilityName,
      'status': 'pending',
      'total_amount': totalAmount,
      'items': items
    };

    // Using ServiceApiClient - automatically routes to OMS for order creation
    return await ServiceApiClient.post(
      endUrl: EndUrl.createOrder,
      data: orderPayload,
      tag: 'CreateOrder',
    );
  }

  @override
  Future<List<OrderEntity>> getOrderHistory({
    String customerId = 'customer_123',
    String status = '',
    int page = 0,
    int pageSize = 10,
    bool refresh = false,
  }) async {
    try {
      // Get current user's customer ID from local storage
      final currentCustomerId = _getCurrentCustomerId();

      // Use ServiceApiClient to call OMS API directly
      final response = await ServiceApiClient.getFromOms(
        endUrl: EndUrl.getOrderHistory,
        queryParameters: {
          'customer_id': currentCustomerId,
          if (status.isNotEmpty) 'status': status,
        },
        tag: 'GetOrderHistory',
      );

      if (response?.data == null) {
        throw Exception('No data received from API');
      }

      // The API returns an array of orders directly
      final List<dynamic> ordersData = response!.data as List<dynamic>;

      // Convert to models and then to entities
      final orderModels = ordersData
          .map((json) => OrderModel.fromJson(json as Map<String, dynamic>))
          .toList();

      // Apply client-side filtering if status is provided (since API might not support it)
      List<OrderModel> filteredOrders = orderModels;
      if (status.isNotEmpty) {
        filteredOrders = orderModels
            .where(
                (order) => order.status?.toLowerCase() == status.toLowerCase())
            .toList();
      }

      // Apply client-side pagination since API returns all orders
      final start = page * pageSize;
      final end = start + pageSize;

      if (start >= filteredOrders.length) {
        return [];
      }

      final paginatedOrders = filteredOrders.sublist(
          start, end > filteredOrders.length ? filteredOrders.length : end);

      return OrderMapper.toEntityList(paginatedOrders);
    } catch (e) {
      throw Exception('Failed to fetch order history: $e');
    }
  }

  @override
  Future<OrderEntity?> getOrderDetails(String orderId) async {
    try {
      // URL encode the order ID (replace + with %2B)

      // Use ServiceApiClient to call OMS API directly
      final response = await ServiceApiClient.getFromOms(
        endUrl: EndUrl.getOrderDetails,
        queryParameters: {
          'order_id': orderId,
        },
        tag: 'GetOrderDetails',
      );

      if (response?.data == null) {
        throw Exception('No data received from API');
      }

      // The API returns a single order object
      final orderData = response!.data as Map<String, dynamic>;

      // Convert to model and then to entity
      final orderModel = OrderModel.fromJson(orderData);
      return OrderMapper.toEntity(orderModel);
    } catch (e) {
      throw Exception('Failed to fetch order details: $e');
    }
  }

  @override
  Future<bool> cancelOrder(String orderId) async {
    try {
      // For now, this is a mock implementation since we're using sample data
      // In a real app, this would make an API call to cancel the order

      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 1));

      // For sample data, we'll just return true
      // In real implementation, this would be:
      // final response = await ServiceApiClient.post(
      //   endUrl: 'orders/$orderId/cancel',
      //   tag: 'CancelOrder',
      // );
      // return response?.statusCode == 200;

      return true;
    } catch (e) {
      throw Exception('Failed to cancel order: $e');
    }
  }
}
