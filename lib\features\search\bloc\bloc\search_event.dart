import 'package:freezed_annotation/freezed_annotation.dart';

part 'search_event.freezed.dart';

@freezed
class SearchEvent with _$SearchEvent {
  const factory SearchEvent.init(String initialQuery) = _Init;
  const factory SearchEvent.search(String query,
      {@Default(false) bool submit}) = _Search;
  const factory SearchEvent.loadMore() = _LoadMore;
  const factory SearchEvent.clearSearch() = _ClearSearch;
  const factory SearchEvent.clearRecent() = _ClearRecent;
  const factory SearchEvent.removeRecent(int index) = _RemoveRecent;
  const factory SearchEvent.selectRecent(String query) = _SelectRecent;
  const factory SearchEvent.selectSuggestion(String suggestion) =
      _SelectSuggestion;
  const factory SearchEvent.inputChange(String query) = _InputChange;
  const factory SearchEvent.updateSuggestions(String query) =
      _UpdateSuggestions;
  const factory SearchEvent.clearState() = _ClearState;
}
