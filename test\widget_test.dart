// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:rozana/app/app.dart';
import 'package:rozana/core/dependency_injection/di_setup.dart';

void main() {
  group('App Widget Tests', () {
    setUp(() {
      // Reset GetIt and setup dependencies before each test
      GetIt.instance.reset();
      setupDI();
    });

    tearDown(() {
      // Clean up after each test
      GetIt.instance.reset();
    });

    testWidgets('App should build without errors', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(const MyApp());

      // Wait for the app to settle
      await tester.pumpAndSettle();

      // Verify that the app builds successfully
      expect(find.byType(MyApp), findsOneWidget);
    });
  });
}
