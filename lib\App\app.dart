import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/app/bloc/app_bloc.dart';
import 'package:rozana/core/blocs/language_bloc/language_bloc.dart';
import 'package:rozana/core/themes/app_theme.dart';
import 'package:rozana/core/themes/theme_bloc/theme_bloc.dart';
import 'package:rozana/features/cart/bloc/cart_bloc.dart';
import 'package:rozana/features/cart/bloc/cart_event.dart';
import 'package:rozana/features/location/bloc/location%20bloc/location_bloc.dart';
import 'package:rozana/l10n/app_localizations.dart';
import 'package:rozana/web-view/bloc/bloc/web_view_bloc.dart';
import 'package:rozana/web-view/presentation/screens/web_screen.dart';
import '../core/dependency_injection/di_container.dart';
import '../core/utils/constants.dart';
import '../routes/app_router.dart';

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late GoRouter _router;

  @override
  void initState() {
    super.initState();
    // Initialize GoRouter with the Bloc for redirection logic
    _router = AppRouter.createRouter(context);
    // Initialize BLoCs for mobile view to avoid loading delays
    _initializeMobileBloCs();
  }

  Future<void> _initializeMobileBloCs() async {
    // Initialize BLoCs for mobile view
    final themeBloc = getIt<ThemeBloc>();
    final locationBloc = getIt<LocationBloc>();
    final cartBloc = getIt<CartBloc>();

    themeBloc.add(const ThemeEvent.init());

    cartBloc.add(const CartEvent.init());

    // Initialize location but don't wait for it to complete
    // This prevents the loading state from blocking the UI
    locationBloc.add(const LocationEvent.started());
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: getIt<WebViewBloc>(),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // Check screen width and update bloc state
          final webViewBloc = context.read<WebViewBloc>();
          webViewBloc.checkScreenWidth(constraints.maxWidth);

          return BlocBuilder<WebViewBloc, WebViewState>(
            builder: (context, state) {
              return state.maybeWhen(
                webViewMode: () => MaterialApp(
                  home: const WebScreen(),
                  debugShowCheckedModeBanner: false,
                  title: AppConstants.appName,
                  scaffoldMessengerKey: scaffoldMessengerKey,
                  theme: AppTheme.lightTheme,
                  darkTheme: AppTheme.darkTheme,
                ),
                nativeViewMode: () => CoreAppWidget(router: _router),
                orElse: () {
                  // Default behavior based on screen width for initial state
                  final isWeb = constraints.maxWidth > 600;
                  if (isWeb) {
                    return MaterialApp(
                      home: const WebScreen(),
                      debugShowCheckedModeBanner: false,
                      title: AppConstants.appName,
                      scaffoldMessengerKey: scaffoldMessengerKey,
                      theme: AppTheme.lightTheme,
                      darkTheme: AppTheme.darkTheme,
                    );
                  }
                  return CoreAppWidget(router: _router);
                },
              );
            },
          );
        },
      ),
    );
  }
}

/// Core app widget that contains the main business logic and UI components
/// This widget can be reused in both native mobile app and web view mobile frame
class CoreAppWidget extends StatelessWidget {
  final GoRouter router;
  final bool isWebFrame;

  const CoreAppWidget({
    super.key,
    required this.router,
    this.isWebFrame = false,
  });

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider.value(value: getIt<ThemeBloc>()),
        BlocProvider.value(value: getIt<LanguageBloc>()),
        BlocProvider.value(value: getIt<LocationBloc>()),
        BlocProvider.value(value: getIt<CartBloc>()),
        BlocProvider.value(value: getIt<WebViewBloc>()),
        // Other global BLoCs can be added here
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<AppBloc, AppState>(listenWhen: (previous, current) {
            bool? previousValue =
                (previous.mapOrNull(loaded: (value) => value.isAuthenticated));
            bool? currentValue =
                (current.mapOrNull(loaded: (value) => value.isAuthenticated));

            return ((previousValue != null) && (currentValue != null)) &&
                (previousValue != currentValue);
          }, listener: (context, state) {
            state.mapOrNull(loaded: (value) {
              // Removed automatic profile navigation - let LoginScreen handle navigation
              if (!value.isAuthenticated) {
                if (navigatorKey.currentState?.context != null) {
                  navigatorKey.currentState!.context.go(RouteNames.login);
                }
              }
            });
          })
        ],
        child: BlocBuilder<ThemeBloc, ThemeState>(
          builder: (context, themeState) {
            return BlocBuilder<LanguageBloc, LanguageState>(
              builder: (context, languageState) {
                final locale = languageState.mapOrNull(
                      loaded: (state) => state.locale,
                    ) ??
                    const Locale('en'); // Default to English

                return MaterialApp.router(
                  debugShowCheckedModeBanner: false,
                  title: AppConstants.appName,
                  scaffoldMessengerKey:
                      isWebFrame ? null : scaffoldMessengerKey,
                  theme: AppTheme.lightTheme,
                  darkTheme: AppTheme.darkTheme,
                  themeMode: themeState.themeMode,
                  routerConfig: router,
                  // Localization configuration
                  localizationsDelegates: const [
                    AppLocalizations.delegate,
                    GlobalMaterialLocalizations.delegate,
                    GlobalWidgetsLocalizations.delegate,
                    GlobalCupertinoLocalizations.delegate,
                  ],
                  supportedLocales: const [
                    Locale('en'), // English (Default)
                    Locale('hi'), // Hindi
                  ],
                  locale: locale, // Dynamic locale from LanguageBloc
                );
              },
            );
          },
        ),
      ),
    );
  }
}
