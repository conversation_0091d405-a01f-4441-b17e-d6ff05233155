import 'package:freezed_annotation/freezed_annotation.dart';

part 'search_state.freezed.dart';

@freezed
class SearchState with _$SearchState {
  const factory SearchState({
    required String query,
    required bool isLoading,
    required bool hasMore,
    required int page,
    required List<Map<String, dynamic>> products,
    required List<Map<String, dynamic>> categories,
    required List<Map<String, dynamic>> subcategories,
    required List<String> recentSearches,
    required List<Map<String, dynamic>> suggestions,
    String? error,
  }) = _SearchState;

  factory SearchState.initial() => const SearchState(
        query: '',
        isLoading: false,
        hasMore: true,
        page: 1,
        products: [],
        categories: [],
        subcategories: [],
        recentSearches: [],
        suggestions: [],
      );
}
