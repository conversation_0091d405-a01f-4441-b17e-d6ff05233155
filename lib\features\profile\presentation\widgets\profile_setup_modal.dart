import 'package:flutter/material.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/core/utils/logger.dart';
import 'package:rozana/core/utils/app_validators.dart';
import 'package:rozana/core/services/user_profile_service.dart';
import 'package:rozana/widgets/custom_button.dart';
import 'package:rozana/widgets/custom_textfield.dart';
import 'package:rozana/widgets/custom_text.dart';

class ProfileSetupModal extends StatefulWidget {
  const ProfileSetupModal({super.key});

  @override
  State<ProfileSetupModal> createState() => _ProfileSetupModalState();
}

class _ProfileSetupModalState extends State<ProfileSetupModal> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final UserProfileService _userProfileService = UserProfileService();

  bool _isLoading = false;
  String? _nameError;
  String? _emailError;

  bool get _isFormValid =>
      _nameController.text.trim().isNotEmpty && _nameError == null;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  void _validateName(String value) {
    final validation =
        AppValidator.nameValidator(value, 'Please enter your name');
    setState(() {
      _nameError = validation.valid ? null : validation.message;
    });
  }

  void _validateEmail(String value) {
    // Email is optional, only validate if not empty
    if (value.trim().isEmpty) {
      setState(() {
        _emailError = null;
      });
      return;
    }

    final validation = AppValidator.emailValidator(value);
    setState(() {
      _emailError = validation.valid ? null : validation.message;
    });
  }

  Future<void> _saveProfile() async {
    if (!_isFormValid) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final profile = _userProfileService.createBasicProfile(
        displayName: _nameController.text.trim(),
        email: _emailController.text.trim().isEmpty
            ? null
            : _emailController.text.trim(),
      );

      await _userProfileService.createUserProfile(profile);

      LogMessage.p('Profile setup completed successfully');

      if (mounted) {
        // Close the modal and return success
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      LogMessage.p('Error saving profile: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save profile. Please try again.'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: AppColors.background,
      child: SafeArea(
        child: Scaffold(
          backgroundColor: AppColors.background,
          appBar: AppBar(
            backgroundColor: AppColors.background,
            elevation: 0,
            leading: const SizedBox(), // Remove back button
            title: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.person_add_outlined,
                  color: AppColors.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Complete Your Profile',
                  style: TextStyle(
                    color: AppColors.textPrimary,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            centerTitle: true,
          ),
          body: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Subtitle
                Center(
                  child: CustomText(
                    'Please provide your name and email to continue',
                    fontSize: 14,
                    color: AppColors.textSecondary,
                    textAlign: TextAlign.center,
                  ),
                ),

                const SizedBox(height: 32),

                // Name Field
                Titledfield(
                  title: 'Full Name',
                  errorText: _nameError,
                  field: CustomTextField(
                    controller: _nameController,
                    hintText: 'Enter your full name',
                    keyboardType: TextInputType.name,
                    textInputAction: TextInputAction.next,
                    onChanged: _validateName,
                    decoration: const InputDecoration(
                      prefixIcon: Icon(Icons.person_outline),
                    ),
                  ),
                ),

                // Email Field (Optional)
                Titledfield(
                  title: 'Email Address (Optional)',
                  errorText: _emailError,
                  field: CustomTextField(
                    controller: _emailController,
                    hintText: 'Enter your email address (optional)',
                    keyboardType: TextInputType.emailAddress,
                    textInputAction: TextInputAction.done,
                    onChanged: _validateEmail,
                    onSubmitted: (_) {
                      if (_isFormValid) {
                        _saveProfile();
                      }
                    },
                    decoration: const InputDecoration(
                      prefixIcon: Icon(Icons.email_outlined),
                    ),
                  ),
                ),

                const Spacer(),

                // Buttons
                Column(
                  children: [
                    SizedBox(
                      width: double.infinity,
                      child: AppButton(
                        text: 'Complete Profile',
                        onPressed: _isFormValid ? _saveProfile : null,
                        isLoading: _isLoading,
                        height: 52,
                        backgroundColor: _isFormValid
                            ? AppColors.primary
                            : AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      width: double.infinity,
                      child: TextButton(
                        onPressed: _isLoading
                            ? null
                            : () {
                                Navigator.of(context).pop(false);
                              },
                        child: Text(
                          'Skip for now',
                          style: TextStyle(
                            color: AppColors.textSecondary,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Helper function to show the profile setup modal as full screen
Future<bool?> showProfileSetupModal(BuildContext context) {
  return showDialog<bool>(
    context: context,
    barrierDismissible: false, // Prevent dismissing by tapping outside
    useSafeArea: false, // Allow full screen
    builder: (context) => const ProfileSetupModal(),
  );
}
