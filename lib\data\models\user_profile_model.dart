class UserProfileModel {
  String? uid;
  String? phoneNumber;
  String? displayName;
  String? email;
  bool? isProfileComplete;
  String? createdAt;
  String? updatedAt;

  UserProfileModel({
    this.uid,
    this.phoneNumber,
    this.displayName,
    this.email,
    this.isProfileComplete,
    this.createdAt,
    this.updatedAt,
  });

  UserProfileModel.fromJson(Map<String, dynamic> json) {
    uid = json['uid']?.toString();
    phoneNumber = json['phoneNumber']?.toString();
    displayName = json['displayName']?.toString();
    email = json['email']?.toString();
    isProfileComplete = json['isProfileComplete'] ?? false;
    createdAt = json['createdAt']?.toString();
    updatedAt = json['updatedAt']?.toString();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (uid != null) data['uid'] = uid;
    if (phoneNumber != null) data['phoneNumber'] = phoneNumber;
    if (displayName != null) data['displayName'] = displayName;
    if (email != null) data['email'] = email;
    if (isProfileComplete != null) data['isProfileComplete'] = isProfileComplete;
    if (createdAt != null) data['createdAt'] = createdAt;
    if (updatedAt != null) data['updatedAt'] = updatedAt;
    return data;
  }

  UserProfileModel copyWith({
    String? uid,
    String? phoneNumber,
    String? displayName,
    String? email,
    bool? isProfileComplete,
    String? createdAt,
    String? updatedAt,
  }) {
    return UserProfileModel(
      uid: uid ?? this.uid,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      displayName: displayName ?? this.displayName,
      email: email ?? this.email,
      isProfileComplete: isProfileComplete ?? this.isProfileComplete,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'UserProfileModel(uid: $uid, phoneNumber: $phoneNumber, displayName: $displayName, email: $email, isProfileComplete: $isProfileComplete)';
  }
}
