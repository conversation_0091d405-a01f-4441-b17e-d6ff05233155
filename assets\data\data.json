{"banners": [{"objectID": "banner1", "title": "Fresh Arrivals", "subtitle": "Check out our new products", "imageUrl": "https://images.unsplash.com/photo-1542838132-92c53300491e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8Z3JvY2VyeXxlbnwwfHwwfHw%3D&auto=format&fit=crop&w=1200&q=60", "actionUrl": "/new-arrivals", "objectType": "banner"}, {"objectID": "banner2", "title": "Summer Fruits", "subtitle": "Refreshing summer collection", "imageUrl": "https://images.unsplash.com/photo-1610832958506-aa56368176cf?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8NHx8ZnJ1aXRzfGVufDB8fDB8fA%3D%3D&auto=format&fit=crop&w=1200&q=60", "actionUrl": "/summer-fruits", "objectType": "banner"}, {"objectID": "banner3", "title": "Weekend Special", "subtitle": "Get 20% off on orders above $50", "imageUrl": "https://images.unsplash.com/photo-1506617564039-2f3b650b7010?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8NXx8Z3JvY2VyeXxlbnwwfHwwfHw%3D&auto=format&fit=crop&w=1200&q=60", "actionUrl": "/promotions/weekend", "objectType": "banner"}], "categories": [{"objectID": "1", "name": "Fruits & Vegetables", "imageUrl": "lib/assets/categories/fruit.png", "productCount": 42, "objectType": "category"}, {"objectID": "2", "name": "Dairy, Bread & Eggs", "imageUrl": "lib/assets/categories/dairy.png", "productCount": 38, "objectType": "category"}, {"objectID": "3", "name": "Cold Drinks & Juices", "imageUrl": "lib/assets/categories/beverages.png", "productCount": 35, "objectType": "category"}, {"objectID": "4", "name": "Tea, Coffee & More", "imageUrl": "lib/assets/categories/tea-coffee.png", "productCount": 24, "objectType": "category"}, {"objectID": "5", "name": "Masala, Dry Fruits & More", "imageUrl": "lib/assets/categories/masalas.png", "productCount": 32, "objectType": "category"}, {"objectID": "6", "name": "Snacks & Munchies", "imageUrl": "lib/assets/categories/munchies.png", "productCount": 40, "objectType": "category"}, {"objectID": "7", "name": "Packaged Foods", "imageUrl": "lib/assets/categories/packaged-foods.png", "productCount": 28, "objectType": "category"}, {"objectID": "8", "name": "Meat, Fish & Eggs", "imageUrl": "lib/assets/categories/meat.png", "productCount": 22, "objectType": "category"}, {"objectID": "9", "name": "Health & Personal Care", "imageUrl": "lib/assets/categories/health.png", "productCount": 36, "objectType": "category"}, {"objectID": "10", "name": "Cleaning Essentials", "imageUrl": "lib/assets/categories/clean-essentials.png", "productCount": 25, "objectType": "category"}], "subCategories": [{"objectID": "1", "name": "Fresh Fruits", "imageUrl": "lib/assets/categories/fruit.png", "productCount": 25, "objectType": "subCategory", "categoryID": "1"}, {"objectID": "2", "name": "Fresh Vegetables", "imageUrl": "lib/assets/categories/vegetable.png", "productCount": 30, "objectType": "subCategory", "categoryID": "1"}, {"objectID": "3", "name": "Organic Fruits", "imageUrl": "lib/assets/categories/organic-fruit.png", "productCount": 15, "objectType": "subCategory", "categoryID": "1"}, {"objectID": "4", "name": "Organic Vegetables", "imageUrl": "lib/assets/categories/organic-vegetable.png", "productCount": 18, "objectType": "subCategory", "categoryID": "1"}, {"objectID": "5", "name": "Exotic Fruits & Vegetables", "imageUrl": "lib/assets/categories/exotic-produce.png", "productCount": 12, "objectType": "subCategory", "categoryID": "1"}, {"objectID": "6", "name": "Milk & Yogurt", "imageUrl": "lib/assets/categories/milk.png", "productCount": 15, "objectType": "subCategory", "categoryID": "2"}, {"objectID": "7", "name": "Bread & Buns", "imageUrl": "lib/assets/categories/bread.png", "productCount": 12, "objectType": "subCategory", "categoryID": "2"}, {"objectID": "8", "name": "Eggs", "imageUrl": "lib/assets/categories/eggs.png", "productCount": 8, "objectType": "subCategory", "categoryID": "2"}, {"objectID": "9", "name": "Butter & Cheese", "imageUrl": "lib/assets/categories/cheese.png", "productCount": 14, "objectType": "subCategory", "categoryID": "2"}, {"objectID": "10", "name": "Paneer & Tofu", "imageUrl": "lib/assets/categories/paneer.png", "productCount": 6, "objectType": "subCategory", "categoryID": "2"}, {"objectID": "11", "name": "Soft Drinks", "imageUrl": "lib/assets/categories/soft-drinks.png", "productCount": 18, "objectType": "subCategory", "categoryID": "3"}, {"objectID": "12", "name": "Fruit Juices", "imageUrl": "lib/assets/categories/juices.png", "productCount": 15, "objectType": "subCategory", "categoryID": "3"}, {"objectID": "13", "name": "Energy Drinks", "imageUrl": "lib/assets/categories/energy-drinks.png", "productCount": 8, "objectType": "subCategory", "categoryID": "3"}, {"objectID": "14", "name": "Water & Soda", "imageUrl": "lib/assets/categories/water.png", "productCount": 12, "objectType": "subCategory", "categoryID": "3"}, {"objectID": "15", "name": "Health Drinks", "imageUrl": "lib/assets/categories/health-drinks.png", "productCount": 10, "objectType": "subCategory", "categoryID": "3"}, {"objectID": "16", "name": "Tea Varieties", "imageUrl": "lib/assets/categories/tea.png", "productCount": 14, "objectType": "subCategory", "categoryID": "4"}, {"objectID": "17", "name": "Coffee", "imageUrl": "lib/assets/categories/coffee.png", "productCount": 12, "objectType": "subCategory", "categoryID": "4"}, {"objectID": "18", "name": "Green Tea", "imageUrl": "lib/assets/categories/green-tea.png", "productCount": 8, "objectType": "subCategory", "categoryID": "4"}, {"objectID": "19", "name": "Herbal Tea", "imageUrl": "lib/assets/categories/herbal-tea.png", "productCount": 10, "objectType": "subCategory", "categoryID": "4"}, {"objectID": "20", "name": "Tea & Coffee Accessories", "imageUrl": "lib/assets/categories/tea-accessories.png", "productCount": 6, "objectType": "subCategory", "categoryID": "4"}, {"objectID": "21", "name": "Spices & Masalas", "imageUrl": "lib/assets/categories/spices.png", "productCount": 20, "objectType": "subCategory", "categoryID": "5"}, {"objectID": "22", "name": "Dry Fruits", "imageUrl": "lib/assets/categories/dry-fruits.png", "productCount": 15, "objectType": "subCategory", "categoryID": "5"}, {"objectID": "23", "name": "Nuts & Seeds", "imageUrl": "lib/assets/categories/nuts.png", "productCount": 12, "objectType": "subCategory", "categoryID": "5"}, {"objectID": "24", "name": "Cooking Pastes", "imageUrl": "lib/assets/categories/cooking-pastes.png", "productCount": 8, "objectType": "subCategory", "categoryID": "5"}, {"objectID": "25", "name": "Herbs & Seasonings", "imageUrl": "lib/assets/categories/herbs.png", "productCount": 10, "objectType": "subCategory", "categoryID": "5"}, {"objectID": "26", "name": "Chips & Crisps", "imageUrl": "lib/assets/categories/chips.png", "productCount": 18, "objectType": "subCategory", "categoryID": "6"}, {"objectID": "27", "name": "Namkeen & Mixture", "imageUrl": "lib/assets/categories/namkeen.png", "productCount": 15, "objectType": "subCategory", "categoryID": "6"}, {"objectID": "28", "name": "Popcorn & Corn Snacks", "imageUrl": "lib/assets/categories/popcorn.png", "productCount": 8, "objectType": "subCategory", "categoryID": "6"}, {"objectID": "29", "name": "Cookies & Crackers", "imageUrl": "lib/assets/categories/cookies.png", "productCount": 12, "objectType": "subCategory", "categoryID": "6"}, {"objectID": "30", "name": "Healthy Snacks", "imageUrl": "lib/assets/categories/healthy-snacks.png", "productCount": 10, "objectType": "subCategory", "categoryID": "6"}, {"objectID": "31", "name": "Noodles & Pasta", "imageUrl": "lib/assets/categories/noodles.png", "productCount": 12, "objectType": "subCategory", "categoryID": "7"}, {"objectID": "32", "name": "Ready to Eat", "imageUrl": "lib/assets/categories/ready-to-eat.png", "productCount": 15, "objectType": "subCategory", "categoryID": "7"}, {"objectID": "33", "name": "Breakfast Cereals", "imageUrl": "lib/assets/categories/cereals.png", "productCount": 10, "objectType": "subCategory", "categoryID": "7"}, {"objectID": "34", "name": "Sauces & Ketchup", "imageUrl": "lib/assets/categories/sauces.png", "productCount": 8, "objectType": "subCategory", "categoryID": "7"}, {"objectID": "35", "name": "Jams & Spreads", "imageUrl": "lib/assets/categories/jams.png", "productCount": 6, "objectType": "subCategory", "categoryID": "7"}, {"objectID": "36", "name": "Fresh Chicken", "imageUrl": "lib/assets/categories/chicken.png", "productCount": 8, "objectType": "subCategory", "categoryID": "8"}, {"objectID": "37", "name": "Fresh Fish & Seafood", "imageUrl": "lib/assets/categories/fish.png", "productCount": 10, "objectType": "subCategory", "categoryID": "8"}, {"objectID": "38", "name": "Mutton & Lamb", "imageUrl": "lib/assets/categories/mutton.png", "productCount": 6, "objectType": "subCategory", "categoryID": "8"}, {"objectID": "39", "name": "Marinated Meats", "imageUrl": "lib/assets/categories/marinated-meat.png", "productCount": 8, "objectType": "subCategory", "categoryID": "8"}, {"objectID": "40", "name": "Frozen Meats", "imageUrl": "lib/assets/categories/frozen-meat.png", "productCount": 7, "objectType": "subCategory", "categoryID": "8"}, {"objectID": "41", "name": "Skin Care", "imageUrl": "lib/assets/categories/skin-care.png", "productCount": 15, "objectType": "subCategory", "categoryID": "9"}, {"objectID": "42", "name": "Hair Care", "imageUrl": "lib/assets/categories/hair-care.png", "productCount": 12, "objectType": "subCategory", "categoryID": "9"}, {"objectID": "43", "name": "Oral Care", "imageUrl": "lib/assets/categories/oral-care.png", "productCount": 8, "objectType": "subCategory", "categoryID": "9"}, {"objectID": "44", "name": "Baby Care", "imageUrl": "lib/assets/categories/baby-care.png", "productCount": 10, "objectType": "subCategory", "categoryID": "9"}, {"objectID": "45", "name": "Health Supplements", "imageUrl": "lib/assets/categories/supplements.png", "productCount": 14, "objectType": "subCategory", "categoryID": "9"}, {"objectID": "46", "name": "Detergents", "imageUrl": "lib/assets/categories/detergents.png", "productCount": 10, "objectType": "subCategory", "categoryID": "10"}, {"objectID": "47", "name": "Dishwashing", "imageUrl": "lib/assets/categories/dishwashing.png", "productCount": 8, "objectType": "subCategory", "categoryID": "10"}, {"objectID": "48", "name": "Floor & Surface Cleaners", "imageUrl": "lib/assets/categories/floor-cleaners.png", "productCount": 12, "objectType": "subCategory", "categoryID": "10"}, {"objectID": "49", "name": "Bathroom Cleaners", "imageUrl": "lib/assets/categories/bathroom-cleaners.png", "productCount": 6, "objectType": "subCategory", "categoryID": "10"}, {"objectID": "50", "name": "Cleaning Tools", "imageUrl": "lib/assets/categories/cleaning-tools.png", "productCount": 8, "objectType": "subCategory", "categoryID": "10"}], "products": [{"objectID": "1", "name": "Organic Bananas", "description": "Fresh organic bananas from local farms", "imageUrl": "lib/assets/products/banana.png", "price": 2.99, "originalPrice": 3.99, "rating": 4.5, "reviewCount": 128, "discountPercentage": 25, "isFeatured": true, "objectType": "product", "subCategoryID": "1", "categoryID": "1"}, {"objectID": "2", "name": "Fresh Apples", "description": "Crisp and juicy apples, perfect for snacking", "imageUrl": "lib/assets/products/apple.png", "price": 3.49, "originalPrice": 4.99, "rating": 4.3, "reviewCount": 95, "discountPercentage": 30, "isFeatured": true, "objectType": "product", "subCategoryID": "1", "categoryID": "1"}, {"objectID": "3", "name": "Sweet Oranges", "description": "Juicy and sweet oranges, rich in vitamin C", "imageUrl": "lib/assets/products/orange.png", "price": 4.49, "originalPrice": 5.99, "rating": 4.4, "reviewCount": 87, "discountPercentage": 25, "isFeatured": false, "objectType": "product", "subCategoryID": "1", "categoryID": "1"}, {"objectID": "4", "name": "<PERSON><PERSON>e <PERSON>", "description": "Sweet and aromatic mangoes, perfect ripeness", "imageUrl": "lib/assets/products/mango.png", "price": 5.99, "originalPrice": 7.49, "rating": 4.7, "reviewCount": 112, "discountPercentage": 20, "isFeatured": true, "objectType": "product", "subCategoryID": "1", "categoryID": "1"}, {"objectID": "5", "name": "Fresh Strawberries", "description": "Sweet and juicy strawberries, freshly picked", "imageUrl": "lib/assets/products/strawberry.png", "price": 3.99, "originalPrice": 4.99, "rating": 4.6, "reviewCount": 76, "discountPercentage": 20, "isFeatured": false, "objectType": "product", "subCategoryID": "1", "categoryID": "1"}, {"objectID": "198", "name": "Organic Brown Rice", "description": "Premium organic brown rice, 1kg pack", "imageUrl": "lib/assets/products/organic-brown-rice.png", "price": 3.99, "originalPrice": 4.49, "rating": 4.7, "reviewCount": 128, "discountPercentage": 11, "isFeatured": true, "objectType": "product", "subCategoryID": "1", "categoryID": "1"}, {"objectID": "199", "name": "Wild Rice", "description": "Premium wild rice blend, 500g pack", "imageUrl": "lib/assets/products/wild-rice.png", "price": 4.99, "originalPrice": 5.49, "rating": 4.6, "reviewCount": 112, "discountPercentage": 9, "isFeatured": false, "objectType": "product", "subCategoryID": "1", "categoryID": "1"}, {"objectID": "200", "name": "Red Rice", "description": "Nutritious red rice, 750g pack", "imageUrl": "lib/assets/products/red-rice.png", "price": 3.79, "originalPrice": 4.29, "rating": 4.5, "reviewCount": 98, "discountPercentage": 12, "isFeatured": false, "objectType": "product", "subCategoryID": "1", "categoryID": "1"}, {"objectID": "201", "name": "Black Rice", "description": "Exotic black rice, rich in antioxidants, 500g pack", "imageUrl": "lib/assets/products/black-rice.png", "price": 5.49, "originalPrice": 5.99, "rating": 4.8, "reviewCount": 86, "discountPercentage": 8, "isFeatured": true, "objectType": "product", "subCategoryID": "1", "categoryID": "1"}, {"objectID": "202", "name": "<PERSON><PERSON>", "description": "Premium short-grain sushi rice, 1kg pack", "imageUrl": "lib/assets/products/sushi-rice.png", "price": 4.29, "originalPrice": 4.99, "rating": 4.7, "reviewCount": 104, "discountPercentage": 14, "isFeatured": false, "objectType": "product", "subCategoryID": "1", "categoryID": "1"}, {"objectID": "6", "name": "Fresh Tomatoes", "description": "Juicy red tomatoes, perfect for salads and cooking", "imageUrl": "lib/assets/products/tomato.png", "price": 2.49, "originalPrice": 2.99, "rating": 4.3, "reviewCount": 56, "discountPercentage": 15, "isFeatured": false, "objectType": "product", "subCategoryID": "2", "categoryID": "1"}, {"objectID": "7", "name": "<PERSON>et <PERSON>", "description": "Premium quality potatoes, great for baking and frying", "imageUrl": "lib/assets/products/potato.png", "price": 3.49, "originalPrice": 4.29, "rating": 4.4, "reviewCount": 38, "discountPercentage": 18, "isFeatured": false, "objectType": "product", "subCategoryID": "2", "categoryID": "1"}, {"objectID": "8", "name": "Fresh Onions", "description": "Essential cooking ingredient, medium-sized yellow onions", "imageUrl": "lib/assets/products/onion.png", "price": 1.99, "originalPrice": 2.49, "rating": 4.2, "reviewCount": 45, "discountPercentage": 20, "isFeatured": false, "objectType": "product", "subCategoryID": "2", "categoryID": "1"}, {"objectID": "9", "name": "Green Bell Peppers", "description": "Crisp and flavorful bell peppers, perfect for stir-fries", "imageUrl": "lib/assets/products/bell-pepper.png", "price": 2.79, "originalPrice": 3.29, "rating": 4.3, "reviewCount": 32, "discountPercentage": 15, "isFeatured": false, "objectType": "product", "subCategoryID": "2", "categoryID": "1"}, {"objectID": "10", "name": "Fresh Carrots", "description": "Sweet and crunchy carrots, rich in vitamins", "imageUrl": "lib/assets/products/carrot.png", "price": 1.89, "originalPrice": 2.29, "rating": 4.4, "reviewCount": 41, "discountPercentage": 17, "isFeatured": false, "objectType": "product", "subCategoryID": "2", "categoryID": "1"}, {"objectID": "198", "name": "Bell Peppers", "description": "Mixed red, yellow and green bell peppers, 500g pack", "imageUrl": "lib/assets/products/bell-peppers.png", "price": 3.99, "originalPrice": 4.49, "rating": 4.7, "reviewCount": 89, "discountPercentage": 11, "isFeatured": false, "objectType": "product", "subCategoryID": "2", "categoryID": "1"}, {"objectID": "199", "name": "<PERSON><PERSON><PERSON>ber", "description": "Fresh crisp cucumbers, 3 pack", "imageUrl": "lib/assets/products/cucumber.png", "price": 1.99, "originalPrice": 2.29, "rating": 4.5, "reviewCount": 76, "discountPercentage": 13, "isFeatured": false, "objectType": "product", "subCategoryID": "2", "categoryID": "1"}, {"objectID": "200", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Fresh green zucchini, 500g pack", "imageUrl": "lib/assets/products/zucchini.png", "price": 2.49, "originalPrice": 2.99, "rating": 4.6, "reviewCount": 68, "discountPercentage": 17, "isFeatured": true, "objectType": "product", "subCategoryID": "2", "categoryID": "1"}, {"objectID": "201", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Fresh broccoli florets, 400g pack", "imageUrl": "lib/assets/products/broccoli.png", "price": 2.79, "originalPrice": 3.29, "rating": 4.7, "reviewCount": 92, "discountPercentage": 15, "isFeatured": false, "objectType": "product", "subCategoryID": "2", "categoryID": "1"}, {"objectID": "202", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Fresh cauliflower head, approx. 600g", "imageUrl": "lib/assets/products/cauliflower.png", "price": 2.99, "originalPrice": 3.49, "rating": 4.5, "reviewCount": 71, "discountPercentage": 14, "isFeatured": false, "objectType": "product", "subCategoryID": "2", "categoryID": "1"}, {"objectID": "11", "name": "Organic Apples", "description": "Pesticide-free organic apples, sweet and nutritious", "imageUrl": "lib/assets/products/organic-apple.png", "price": 4.99, "originalPrice": 6.49, "rating": 4.8, "reviewCount": 65, "discountPercentage": 23, "isFeatured": true, "objectType": "product", "subCategoryID": "3", "categoryID": "1"}, {"objectID": "208", "name": "Cherries", "description": "Sweet dark cherries, 250g pack", "imageUrl": "lib/assets/products/cherries.png", "price": 4.49, "originalPrice": 4.99, "rating": 4.7, "reviewCount": 96, "discountPercentage": 10, "isFeatured": false, "objectType": "product", "subCategoryID": "3", "categoryID": "1"}, {"objectID": "209", "name": "Cranberries", "description": "Dried cranberries, 200g pack", "imageUrl": "lib/assets/products/cranberries.png", "price": 2.99, "originalPrice": 3.49, "rating": 4.6, "reviewCount": 78, "discountPercentage": 14, "isFeatured": false, "objectType": "product", "subCategoryID": "3", "categoryID": "1"}, {"objectID": "210", "name": "Grapes", "description": "Sweet seedless grapes, 500g pack", "imageUrl": "lib/assets/products/grapes.png", "price": 3.99, "originalPrice": 4.49, "rating": 4.7, "reviewCount": 108, "discountPercentage": 11, "isFeatured": true, "objectType": "product", "subCategoryID": "3", "categoryID": "1"}, {"objectID": "211", "name": "<PERSON><PERSON>", "description": "Fresh kiwi fruit, pack of 4", "imageUrl": "lib/assets/products/kiwi.png", "price": 2.99, "originalPrice": 3.49, "rating": 4.6, "reviewCount": 92, "discountPercentage": 14, "isFeatured": false, "objectType": "product", "subCategoryID": "3", "categoryID": "1"}, {"objectID": "212", "name": "Peaches", "description": "Juicy ripe peaches, pack of 4", "imageUrl": "lib/assets/products/peaches.png", "price": 3.49, "originalPrice": 3.99, "rating": 4.7, "reviewCount": 86, "discountPercentage": 13, "isFeatured": false, "objectType": "product", "subCategoryID": "3", "categoryID": "1"}, {"objectID": "213", "name": "Plums", "description": "Sweet ripe plums, pack of 6", "imageUrl": "lib/assets/products/plums.png", "price": 2.99, "originalPrice": 3.49, "rating": 4.5, "reviewCount": 78, "discountPercentage": 14, "isFeatured": false, "objectType": "product", "subCategoryID": "3", "categoryID": "1"}, {"objectID": "214", "name": "Apricots", "description": "Fresh apricots, pack of 6", "imageUrl": "lib/assets/products/apricots.png", "price": 3.29, "originalPrice": 3.79, "rating": 4.6, "reviewCount": 82, "discountPercentage": 13, "isFeatured": false, "objectType": "product", "subCategoryID": "3", "categoryID": "1"}, {"objectID": "12", "name": "Organic Strawberries", "description": "Organic strawberries grown without chemicals", "imageUrl": "lib/assets/products/organic-strawberry.png", "price": 5.49, "originalPrice": 6.99, "rating": 4.7, "reviewCount": 52, "discountPercentage": 21, "isFeatured": false, "objectType": "product", "subCategoryID": "3", "categoryID": "1"}, {"objectID": "13", "name": "Organic Blueberries", "description": "Antioxidant-rich organic blueberries, 125g pack", "imageUrl": "lib/assets/products/blueberry.png", "price": 4.99, "originalPrice": 5.99, "rating": 4.6, "reviewCount": 48, "discountPercentage": 17, "isFeatured": false, "objectType": "product", "subCategoryID": "3", "categoryID": "1"}, {"objectID": "14", "name": "Organic Spinach", "description": "Fresh organic spinach, pesticide-free, 200g pack", "imageUrl": "lib/assets/products/spinach.png", "price": 2.99, "originalPrice": 3.49, "rating": 4.5, "reviewCount": 42, "discountPercentage": 14, "isFeatured": false, "objectType": "product", "subCategoryID": "4", "categoryID": "1"}, {"objectID": "15", "name": "Organic Carrots", "description": "Sweet organic carrots grown without pesticides", "imageUrl": "lib/assets/products/organic-carrot.png", "price": 2.79, "originalPrice": 3.29, "rating": 4.4, "reviewCount": 38, "discountPercentage": 15, "isFeatured": false, "objectType": "product", "subCategoryID": "4", "categoryID": "1"}, {"objectID": "16", "name": "Organic Broccoli", "description": "Fresh organic broccoli, rich in nutrients", "imageUrl": "lib/assets/products/broccoli.png", "price": 3.49, "originalPrice": 3.99, "rating": 4.3, "reviewCount": 35, "discountPercentage": 12, "isFeatured": false, "objectType": "product", "subCategoryID": "4", "categoryID": "1"}, {"objectID": "17", "name": "Organic Bell Peppers", "description": "Colorful organic bell peppers, pesticide-free", "imageUrl": "lib/assets/products/organic-bell-pepper.png", "price": 3.99, "originalPrice": 4.79, "rating": 4.5, "reviewCount": 32, "discountPercentage": 17, "isFeatured": false, "objectType": "product", "subCategoryID": "4", "categoryID": "1"}, {"objectID": "215", "name": "Organic Kale", "description": "Fresh organic kale, rich in nutrients, 200g pack", "imageUrl": "lib/assets/products/organic-kale.png", "price": 3.49, "originalPrice": 3.99, "rating": 4.7, "reviewCount": 86, "discountPercentage": 13, "isFeatured": true, "objectType": "product", "subCategoryID": "4", "categoryID": "1"}, {"objectID": "216", "name": "Organic Tomatoes", "description": "Juicy organic tomatoes, 500g pack", "imageUrl": "lib/assets/products/organic-tomatoes.png", "price": 3.99, "originalPrice": 4.49, "rating": 4.8, "reviewCount": 92, "discountPercentage": 11, "isFeatured": false, "objectType": "product", "subCategoryID": "4", "categoryID": "1"}, {"objectID": "217", "name": "Organic Potatoes", "description": "Organic potatoes, 1kg pack", "imageUrl": "lib/assets/products/organic-potatoes.png", "price": 3.79, "originalPrice": 4.29, "rating": 4.6, "reviewCount": 78, "discountPercentage": 12, "isFeatured": false, "objectType": "product", "subCategoryID": "4", "categoryID": "1"}, {"objectID": "218", "name": "Organic Lettuce", "description": "Fresh organic lettuce, pesticide-free", "imageUrl": "lib/assets/products/organic-lettuce.png", "price": 2.99, "originalPrice": 3.49, "rating": 4.7, "reviewCount": 82, "discountPercentage": 14, "isFeatured": true, "objectType": "product", "subCategoryID": "4", "categoryID": "1"}, {"objectID": "219", "name": "Organic Onions", "description": "Organic onions, 500g pack", "imageUrl": "lib/assets/products/organic-onions.png", "price": 2.49, "originalPrice": 2.99, "rating": 4.5, "reviewCount": 74, "discountPercentage": 17, "isFeatured": false, "objectType": "product", "subCategoryID": "4", "categoryID": "1"}, {"objectID": "18", "name": "Organic Cucumber", "description": "Crisp organic cucumbers, perfect for salads", "imageUrl": "lib/assets/products/cucumber.png", "price": 2.49, "originalPrice": 2.99, "rating": 4.2, "reviewCount": 28, "discountPercentage": 17, "isFeatured": false, "objectType": "product", "subCategoryID": "4", "categoryID": "1"}, {"objectID": "19", "name": "Dragon Fruit", "description": "Exotic dragon fruit with vibrant pink flesh", "imageUrl": "lib/assets/products/dragon-fruit.png", "price": 5.99, "originalPrice": 7.49, "rating": 4.7, "reviewCount": 45, "discountPercentage": 20, "isFeatured": true, "objectType": "product", "subCategoryID": "5", "categoryID": "1"}, {"objectID": "20", "name": "Kiwi <PERSON>", "description": "Sweet and tangy kiwi fruits, rich in vitamin C", "imageUrl": "lib/assets/products/kiwi.png", "price": 4.49, "originalPrice": 5.49, "rating": 4.6, "reviewCount": 38, "discountPercentage": 18, "isFeatured": false, "objectType": "product", "subCategoryID": "5", "categoryID": "1"}, {"objectID": "21", "name": "Passion Fruit", "description": "Aromatic passion fruit, perfect for desserts and drinks", "imageUrl": "lib/assets/products/passion-fruit.png", "price": 6.49, "originalPrice": 7.99, "rating": 4.8, "reviewCount": 32, "discountPercentage": 19, "isFeatured": false, "objectType": "product", "subCategoryID": "5", "categoryID": "1"}, {"objectID": "22", "name": "Star Fruit", "description": "Unique star-shaped exotic fruit with sweet-sour taste", "imageUrl": "lib/assets/products/star-fruit.png", "price": 5.49, "originalPrice": 6.79, "rating": 4.5, "reviewCount": 28, "discountPercentage": 19, "isFeatured": false, "objectType": "product", "subCategoryID": "5", "categoryID": "1"}, {"objectID": "23", "name": "<PERSON><PERSON><PERSON>", "description": "Sweet tropical fruit with hairy red skin", "imageUrl": "lib/assets/products/rambutan.png", "price": 7.99, "originalPrice": 9.49, "rating": 4.7, "reviewCount": 24, "discountPercentage": 16, "isFeatured": false, "objectType": "product", "subCategoryID": "5", "categoryID": "1"}, {"objectID": "220", "name": "<PERSON><PERSON><PERSON>", "description": "Sweet and fragrant lychee fruit, 250g pack", "imageUrl": "lib/assets/products/lychee.png", "price": 4.99, "originalPrice": 5.49, "rating": 4.8, "reviewCount": 96, "discountPercentage": 9, "isFeatured": true, "objectType": "product", "subCategoryID": "5", "categoryID": "1"}, {"objectID": "221", "name": "Mangosteen", "description": "Exotic mangosteen with sweet white flesh, 300g pack", "imageUrl": "lib/assets/products/mangosteen.png", "price": 8.99, "originalPrice": 9.99, "rating": 4.9, "reviewCount": 86, "discountPercentage": 10, "isFeatured": false, "objectType": "product", "subCategoryID": "5", "categoryID": "1"}, {"objectID": "222", "name": "<PERSON><PERSON>", "description": "King of fruits with unique aroma and creamy texture", "imageUrl": "lib/assets/products/durian.png", "price": 12.99, "originalPrice": 14.99, "rating": 4.5, "reviewCount": 72, "discountPercentage": 13, "isFeatured": true, "objectType": "product", "subCategoryID": "5", "categoryID": "1"}, {"objectID": "223", "name": "Jackfruit", "description": "Sweet tropical jackfruit, pre-cut, 300g pack", "imageUrl": "lib/assets/products/jackfruit.png", "price": 6.99, "originalPrice": 7.99, "rating": 4.7, "reviewCount": 78, "discountPercentage": 12, "isFeatured": false, "objectType": "product", "subCategoryID": "5", "categoryID": "1"}, {"objectID": "224", "name": "<PERSON><PERSON>", "description": "White-fleshed dragon fruit, rich in antioxidants", "imageUrl": "lib/assets/products/pitaya.png", "price": 5.49, "originalPrice": 6.49, "rating": 4.6, "reviewCount": 68, "discountPercentage": 15, "isFeatured": false, "objectType": "product", "subCategoryID": "5", "categoryID": "1"}, {"objectID": "24", "name": "Full Cream Milk", "description": "Fresh full cream milk, 1 liter", "imageUrl": "lib/assets/products/milk.png", "price": 2.99, "originalPrice": 3.49, "rating": 4.6, "reviewCount": 85, "discountPercentage": 14, "isFeatured": true, "objectType": "product", "subCategoryID": "6", "categoryID": "2"}, {"objectID": "25", "name": "Low Fat Milk", "description": "Healthy low fat milk, 1 liter", "imageUrl": "lib/assets/products/low-fat-milk.png", "price": 3.29, "originalPrice": 3.79, "rating": 4.5, "reviewCount": 72, "discountPercentage": 13, "isFeatured": false, "objectType": "product", "subCategoryID": "6", "categoryID": "2"}, {"objectID": "225", "name": "Skimmed Milk", "description": "Fat-free skimmed milk, 1 liter", "imageUrl": "lib/assets/products/skimmed-milk.png", "price": 3.19, "originalPrice": 3.69, "rating": 4.6, "reviewCount": 78, "discountPercentage": 14, "isFeatured": false, "objectType": "product", "subCategoryID": "6", "categoryID": "2"}, {"objectID": "226", "name": "Almond Milk", "description": "Dairy-free almond milk, 1 liter", "imageUrl": "lib/assets/products/almond-milk.png", "price": 3.99, "originalPrice": 4.49, "rating": 4.8, "reviewCount": 92, "discountPercentage": 11, "isFeatured": true, "objectType": "product", "subCategoryID": "6", "categoryID": "2"}, {"objectID": "227", "name": "Soy Milk", "description": "Plant-based soy milk, 1 liter", "imageUrl": "lib/assets/products/soy-milk.png", "price": 3.79, "originalPrice": 4.29, "rating": 4.5, "reviewCount": 84, "discountPercentage": 12, "isFeatured": false, "objectType": "product", "subCategoryID": "6", "categoryID": "2"}, {"objectID": "228", "name": "Oat Milk", "description": "Creamy plant-based oat milk, 1 liter", "imageUrl": "lib/assets/products/oat-milk.png", "price": 3.89, "originalPrice": 4.39, "rating": 4.7, "reviewCount": 88, "discountPercentage": 11, "isFeatured": false, "objectType": "product", "subCategoryID": "6", "categoryID": "2"}, {"objectID": "229", "name": "Coconut Milk", "description": "Rich and creamy coconut milk, 1 liter", "imageUrl": "lib/assets/products/coconut-milk.png", "price": 3.99, "originalPrice": 4.49, "rating": 4.6, "reviewCount": 76, "discountPercentage": 11, "isFeatured": false, "objectType": "product", "subCategoryID": "6", "categoryID": "2"}, {"objectID": "230", "name": "Chocolate Milk", "description": "Delicious chocolate flavored milk, 1 liter", "imageUrl": "lib/assets/products/chocolate-milk.png", "price": 3.49, "originalPrice": 3.99, "rating": 4.8, "reviewCount": 96, "discountPercentage": 13, "isFeatured": true, "objectType": "product", "subCategoryID": "6", "categoryID": "2"}, {"objectID": "231", "name": "Strawberry Milk", "description": "Sweet strawberry flavored milk, 1 liter", "imageUrl": "lib/assets/products/strawberry-milk.png", "price": 3.49, "originalPrice": 3.99, "rating": 4.7, "reviewCount": 82, "discountPercentage": 13, "isFeatured": false, "objectType": "product", "subCategoryID": "6", "categoryID": "2"}, {"objectID": "232", "name": "Lactose-Free Milk", "description": "Easy to digest lactose-free milk, 1 liter", "imageUrl": "lib/assets/products/lactose-free-milk.png", "price": 3.89, "originalPrice": 4.39, "rating": 4.7, "reviewCount": 74, "discountPercentage": 11, "isFeatured": false, "objectType": "product", "subCategoryID": "6", "categoryID": "2"}, {"objectID": "26", "name": "Whole Wheat Bread", "description": "Freshly baked whole wheat bread, perfect for sandwiches", "imageUrl": "lib/assets/products/whole-wheat-bread.png", "price": 2.99, "originalPrice": 3.49, "rating": 4.7, "reviewCount": 105, "discountPercentage": 14, "isFeatured": true, "objectType": "product", "subCategoryID": "7", "categoryID": "2"}, {"objectID": "27", "name": "Multigrain Bread", "description": "Nutritious multigrain bread with seeds and nuts", "imageUrl": "lib/assets/products/multigrain-bread.png", "price": 3.49, "originalPrice": 3.99, "rating": 4.6, "reviewCount": 87, "discountPercentage": 13, "isFeatured": false, "objectType": "product", "subCategoryID": "7", "categoryID": "2"}, {"objectID": "28", "name": "Burger Buns", "description": "Soft burger buns, pack of 6", "imageUrl": "lib/assets/products/burger-buns.png", "price": 2.49, "originalPrice": 2.99, "rating": 4.4, "reviewCount": 62, "discountPercentage": 17, "isFeatured": false, "objectType": "product", "subCategoryID": "7", "categoryID": "2"}, {"objectID": "29", "name": "<PERSON><PERSON><PERSON>", "description": "Ready-to-bake garlic bread with herbs", "imageUrl": "lib/assets/products/garlic-bread.png", "price": 3.99, "originalPrice": 4.49, "rating": 4.8, "reviewCount": 124, "discountPercentage": 11, "isFeatured": true, "objectType": "product", "subCategoryID": "7", "categoryID": "2"}, {"objectID": "233", "name": "Sour<PERSON>ugh Bread", "description": "Artisanal sourdough bread with tangy flavor", "imageUrl": "lib/assets/products/sourdough-bread.png", "price": 4.49, "originalPrice": 4.99, "rating": 4.8, "reviewCount": 112, "discountPercentage": 10, "isFeatured": true, "objectType": "product", "subCategoryID": "7", "categoryID": "2"}, {"objectID": "234", "name": "Ciabatta Bread", "description": "Italian ciabatta bread with crispy crust", "imageUrl": "lib/assets/products/ciabatta-bread.png", "price": 3.79, "originalPrice": 4.29, "rating": 4.7, "reviewCount": 94, "discountPercentage": 12, "isFeatured": false, "objectType": "product", "subCategoryID": "7", "categoryID": "2"}, {"objectID": "235", "name": "Baguette", "description": "Fresh French baguette with crispy exterior", "imageUrl": "lib/assets/products/baguette.png", "price": 2.99, "originalPrice": 3.49, "rating": 4.8, "reviewCount": 108, "discountPercentage": 14, "isFeatured": true, "objectType": "product", "subCategoryID": "7", "categoryID": "2"}, {"objectID": "236", "name": "Croissants", "description": "Buttery French croissants, pack of 4", "imageUrl": "lib/assets/products/croissants.png", "price": 4.99, "originalPrice": 5.49, "rating": 4.9, "reviewCount": 126, "discountPercentage": 9, "isFeatured": true, "objectType": "product", "subCategoryID": "7", "categoryID": "2"}, {"objectID": "237", "name": "<PERSON><PERSON>", "description": "Hearty rye bread, perfect for sandwiches", "imageUrl": "lib/assets/products/rye-bread.png", "price": 3.89, "originalPrice": 4.39, "rating": 4.6, "reviewCount": 88, "discountPercentage": 11, "isFeatured": false, "objectType": "product", "subCategoryID": "7", "categoryID": "2"}, {"objectID": "238", "name": "Dinner Rolls", "description": "Soft dinner rolls, pack of 12", "imageUrl": "lib/assets/products/dinner-rolls.png", "price": 3.49, "originalPrice": 3.99, "rating": 4.7, "reviewCount": 92, "discountPercentage": 13, "isFeatured": false, "objectType": "product", "subCategoryID": "7", "categoryID": "2"}, {"objectID": "30", "name": "Brown Eggs", "description": "Farm-fresh brown eggs, dozen", "imageUrl": "lib/assets/products/brown-eggs.png", "price": 4.29, "originalPrice": 4.99, "rating": 4.7, "reviewCount": 93, "discountPercentage": 14, "isFeatured": true, "objectType": "product", "subCategoryID": "8", "categoryID": "2"}, {"objectID": "31", "name": "White Eggs", "description": "Farm-fresh white eggs, half dozen", "imageUrl": "lib/assets/products/white-eggs.png", "price": 2.49, "originalPrice": 2.99, "rating": 4.5, "reviewCount": 78, "discountPercentage": 17, "isFeatured": false, "objectType": "product", "subCategoryID": "8", "categoryID": "2"}, {"objectID": "32", "name": "Organic Eggs", "description": "Organic free-range eggs, dozen", "imageUrl": "lib/assets/products/organic-eggs.png", "price": 5.99, "originalPrice": 6.49, "rating": 4.9, "reviewCount": 112, "discountPercentage": 8, "isFeatured": true, "objectType": "product", "subCategoryID": "8", "categoryID": "2"}, {"objectID": "239", "name": "Quail Eggs", "description": "Gourmet quail eggs, pack of 12", "imageUrl": "lib/assets/products/quail-eggs.png", "price": 6.99, "originalPrice": 7.99, "rating": 4.7, "reviewCount": 68, "discountPercentage": 13, "isFeatured": false, "objectType": "product", "subCategoryID": "8", "categoryID": "2"}, {"objectID": "240", "name": "Duck Eggs", "description": "Rich and flavorful duck eggs, half dozen", "imageUrl": "lib/assets/products/duck-eggs.png", "price": 7.49, "originalPrice": 8.49, "rating": 4.8, "reviewCount": 56, "discountPercentage": 12, "isFeatured": false, "objectType": "product", "subCategoryID": "8", "categoryID": "2"}, {"objectID": "241", "name": "Omega-3 Eggs", "description": "Eggs from hens fed with flaxseed for higher omega-3 content", "imageUrl": "lib/assets/products/omega3-eggs.png", "price": 5.49, "originalPrice": 5.99, "rating": 4.7, "reviewCount": 82, "discountPercentage": 8, "isFeatured": true, "objectType": "product", "subCategoryID": "8", "categoryID": "2"}, {"objectID": "242", "name": "Jumbo Eggs", "description": "Extra large jumbo eggs, half dozen", "imageUrl": "lib/assets/products/jumbo-eggs.png", "price": 4.99, "originalPrice": 5.49, "rating": 4.6, "reviewCount": 74, "discountPercentage": 9, "isFeatured": false, "objectType": "product", "subCategoryID": "8", "categoryID": "2"}, {"objectID": "243", "name": "Pastured Eggs", "description": "Eggs from free-roaming pastured hens, dozen", "imageUrl": "lib/assets/products/pastured-eggs.png", "price": 6.49, "originalPrice": 6.99, "rating": 4.9, "reviewCount": 96, "discountPercentage": 7, "isFeatured": true, "objectType": "product", "subCategoryID": "8", "categoryID": "2"}, {"objectID": "244", "name": "Egg Whites", "description": "Pure egg whites in carton, 500ml", "imageUrl": "lib/assets/products/egg-whites.png", "price": 4.29, "originalPrice": 4.79, "rating": 4.5, "reviewCount": 68, "discountPercentage": 10, "isFeatured": false, "objectType": "product", "subCategoryID": "8", "categoryID": "2"}, {"objectID": "245", "name": "Colored Easter Eggs", "description": "Naturally dyed colored eggs, half dozen", "imageUrl": "lib/assets/products/easter-eggs.png", "price": 5.99, "originalPrice": 6.49, "rating": 4.7, "reviewCount": 58, "discountPercentage": 8, "isFeatured": false, "objectType": "product", "subCategoryID": "8", "categoryID": "2"}, {"objectID": "33", "name": "Cheddar Cheese", "description": "Sharp cheddar cheese block, 250g", "imageUrl": "lib/assets/products/cheddar-cheese.png", "price": 4.99, "originalPrice": 5.49, "rating": 4.6, "reviewCount": 86, "discountPercentage": 9, "isFeatured": false, "objectType": "product", "subCategoryID": "9", "categoryID": "2"}, {"objectID": "34", "name": "<PERSON><PERSON><PERSON>", "description": "Fresh mozzarella cheese, 200g", "imageUrl": "lib/assets/products/mozzarella.png", "price": 3.99, "originalPrice": 4.49, "rating": 4.7, "reviewCount": 94, "discountPercentage": 11, "isFeatured": true, "objectType": "product", "subCategoryID": "9", "categoryID": "2"}, {"objectID": "35", "name": "Salted Butter", "description": "Premium salted butter, 250g", "imageUrl": "lib/assets/products/salted-butter.png", "price": 3.49, "originalPrice": 3.99, "rating": 4.5, "reviewCount": 76, "discountPercentage": 13, "isFeatured": false, "objectType": "product", "subCategoryID": "9", "categoryID": "2"}, {"objectID": "36", "name": "White Sandwich Bread", "description": "Soft white sandwich bread, 400g", "imageUrl": "lib/assets/products/white-bread.png", "price": 2.29, "originalPrice": 2.79, "rating": 4.3, "reviewCount": 65, "discountPercentage": 18, "isFeatured": false, "objectType": "product", "subCategoryID": "10", "categoryID": "3"}, {"objectID": "37", "name": "Fruit Bread", "description": "Sweet bread with dried fruits and nuts", "imageUrl": "lib/assets/products/fruit-bread.png", "price": 3.99, "originalPrice": 4.49, "rating": 4.6, "reviewCount": 82, "discountPercentage": 11, "isFeatured": true, "objectType": "product", "subCategoryID": "10", "categoryID": "3"}, {"objectID": "38", "name": "Brioche Bread", "description": "Rich and buttery brioche bread", "imageUrl": "lib/assets/products/brioche.png", "price": 4.49, "originalPrice": 4.99, "rating": 4.8, "reviewCount": 94, "discountPercentage": 10, "isFeatured": true, "objectType": "product", "subCategoryID": "10", "categoryID": "3"}, {"objectID": "39", "name": "Oats Cereal", "description": "Whole grain oats cereal, 500g", "imageUrl": "lib/assets/products/oats-cereal.png", "price": 3.99, "originalPrice": 4.49, "rating": 4.7, "reviewCount": 108, "discountPercentage": 11, "isFeatured": true, "objectType": "product", "subCategoryID": "11", "categoryID": "3"}, {"objectID": "40", "name": "<PERSON><PERSON>", "description": "Crispy corn flakes cereal, 400g", "imageUrl": "lib/assets/products/corn-flakes.png", "price": 3.49, "originalPrice": 3.99, "rating": 4.5, "reviewCount": 87, "discountPercentage": 13, "isFeatured": false, "objectType": "product", "subCategoryID": "11", "categoryID": "3"}, {"objectID": "41", "name": "Granola", "description": "Crunchy granola with nuts and dried fruits, 350g", "imageUrl": "lib/assets/products/granola.png", "price": 4.99, "originalPrice": 5.49, "rating": 4.8, "reviewCount": 124, "discountPercentage": 9, "isFeatured": true, "objectType": "product", "subCategoryID": "11", "categoryID": "3"}, {"objectID": "42", "name": "Basmati Rice", "description": "Premium long grain basmati rice, 1kg", "imageUrl": "lib/assets/products/basmati-rice.png", "price": 5.99, "originalPrice": 6.49, "rating": 4.7, "reviewCount": 116, "discountPercentage": 8, "isFeatured": true, "objectType": "product", "subCategoryID": "12", "categoryID": "3"}, {"objectID": "43", "name": "<PERSON>", "description": "Nutritious whole grain brown rice, 1kg", "imageUrl": "lib/assets/products/brown-rice.png", "price": 4.99, "originalPrice": 5.49, "rating": 4.6, "reviewCount": 92, "discountPercentage": 9, "isFeatured": false, "objectType": "product", "subCategoryID": "12", "categoryID": "3"}, {"objectID": "44", "name": "<PERSON> Rice", "description": "Fragrant jasmine rice, 1kg", "imageUrl": "lib/assets/products/jasmine-rice.png", "price": 6.49, "originalPrice": 6.99, "rating": 4.8, "reviewCount": 105, "discountPercentage": 7, "isFeatured": true, "objectType": "product", "subCategoryID": "12", "categoryID": "3"}, {"objectID": "45", "name": "Arborio Rice", "description": "Italian arborio rice for risotto, 500g", "imageUrl": "lib/assets/products/arborio-rice.png", "price": 4.49, "originalPrice": 4.99, "rating": 4.7, "reviewCount": 78, "discountPercentage": 10, "isFeatured": false, "objectType": "product", "subCategoryID": "12", "categoryID": "3"}, {"objectID": "46", "name": "Spaghetti", "description": "Premium Italian spaghetti pasta, 500g", "imageUrl": "lib/assets/products/spaghetti.png", "price": 1.99, "originalPrice": 2.49, "rating": 4.6, "reviewCount": 98, "discountPercentage": 20, "isFeatured": true, "objectType": "product", "subCategoryID": "13", "categoryID": "3"}, {"objectID": "47", "name": "Penne Pasta", "description": "Durum wheat penne pasta, 500g", "imageUrl": "lib/assets/products/penne.png", "price": 1.89, "originalPrice": 2.29, "rating": 4.5, "reviewCount": 85, "discountPercentage": 17, "isFeatured": false, "objectType": "product", "subCategoryID": "13", "categoryID": "3"}, {"objectID": "48", "name": "Egg Noodles", "description": "Fresh egg noodles, 300g", "imageUrl": "lib/assets/products/egg-noodles.png", "price": 2.49, "originalPrice": 2.99, "rating": 4.7, "reviewCount": 92, "discountPercentage": 17, "isFeatured": true, "objectType": "product", "subCategoryID": "13", "categoryID": "3"}, {"objectID": "49", "name": "Instant Ramen", "description": "Quick cooking ramen noodles, pack of 5", "imageUrl": "lib/assets/products/instant-ramen.png", "price": 3.99, "originalPrice": 4.49, "rating": 4.4, "reviewCount": 76, "discountPercentage": 11, "isFeatured": false, "objectType": "product", "subCategoryID": "13", "categoryID": "3"}, {"objectID": "50", "name": "Olive Oil", "description": "Extra virgin olive oil, 500ml", "imageUrl": "lib/assets/products/olive-oil.png", "price": 7.99, "originalPrice": 8.99, "rating": 4.8, "reviewCount": 124, "discountPercentage": 11, "isFeatured": true, "objectType": "product", "subCategoryID": "14", "categoryID": "3"}, {"objectID": "51", "name": "Sunflower Oil", "description": "Pure sunflower cooking oil, 1L", "imageUrl": "lib/assets/products/sunflower-oil.png", "price": 4.99, "originalPrice": 5.49, "rating": 4.5, "reviewCount": 87, "discountPercentage": 9, "isFeatured": false, "objectType": "product", "subCategoryID": "14", "categoryID": "3"}, {"objectID": "52", "name": "Coconut Oil", "description": "Organic virgin coconut oil, 400ml", "imageUrl": "lib/assets/products/coconut-oil.png", "price": 6.99, "originalPrice": 7.99, "rating": 4.7, "reviewCount": 108, "discountPercentage": 13, "isFeatured": true, "objectType": "product", "subCategoryID": "14", "categoryID": "3"}, {"objectID": "53", "name": "Sesame Oil", "description": "Toasted sesame oil for Asian cooking, 250ml", "imageUrl": "lib/assets/products/sesame-oil.png", "price": 5.49, "originalPrice": 5.99, "rating": 4.6, "reviewCount": 92, "discountPercentage": 8, "isFeatured": false, "objectType": "product", "subCategoryID": "14", "categoryID": "3"}, {"objectID": "54", "name": "Black Pepper", "description": "Freshly ground black pepper, 100g", "imageUrl": "lib/assets/products/black-pepper.png", "price": 3.49, "originalPrice": 3.99, "rating": 4.7, "reviewCount": 86, "discountPercentage": 13, "isFeatured": false, "objectType": "product", "subCategoryID": "15", "categoryID": "3"}, {"objectID": "55", "name": "Mixed Herbs", "description": "Dried mixed herbs for cooking, 50g", "imageUrl": "lib/assets/products/mixed-herbs.png", "price": 2.99, "originalPrice": 3.49, "rating": 4.6, "reviewCount": 78, "discountPercentage": 14, "isFeatured": true, "objectType": "product", "subCategoryID": "15", "categoryID": "3"}, {"objectID": "56", "name": "Cinnamon Sticks", "description": "Premium cinnamon sticks, 50g", "imageUrl": "lib/assets/products/cinnamon.png", "price": 3.99, "originalPrice": 4.49, "rating": 4.8, "reviewCount": 94, "discountPercentage": 11, "isFeatured": true, "objectType": "product", "subCategoryID": "15", "categoryID": "3"}, {"objectID": "57", "name": "<PERSON> Powder", "description": "Authentic curry powder blend, 80g", "imageUrl": "lib/assets/products/curry-powder.png", "price": 3.79, "originalPrice": 4.29, "rating": 4.7, "reviewCount": 89, "discountPercentage": 12, "isFeatured": false, "objectType": "product", "subCategoryID": "15", "categoryID": "3"}, {"objectID": "58", "name": "Canned Tuna", "description": "Tuna chunks in water, 185g", "imageUrl": "lib/assets/products/canned-tuna.png", "price": 2.49, "originalPrice": 2.99, "rating": 4.5, "reviewCount": 82, "discountPercentage": 17, "isFeatured": true, "objectType": "product", "subCategoryID": "16", "categoryID": "4"}, {"objectID": "59", "name": "Baked Beans", "description": "Baked beans in tomato sauce, 400g", "imageUrl": "lib/assets/products/baked-beans.png", "price": 1.29, "originalPrice": 1.59, "rating": 4.4, "reviewCount": 76, "discountPercentage": 19, "isFeatured": false, "objectType": "product", "subCategoryID": "16", "categoryID": "4"}, {"objectID": "60", "name": "Canned Corn", "description": "Sweet corn kernels, 330g", "imageUrl": "lib/assets/products/canned-corn.png", "price": 1.49, "originalPrice": 1.79, "rating": 4.6, "reviewCount": 85, "discountPercentage": 17, "isFeatured": true, "objectType": "product", "subCategoryID": "16", "categoryID": "4"}, {"objectID": "61", "name": "Canned Soup", "description": "Creamy tomato soup, 400g", "imageUrl": "lib/assets/products/tomato-soup.png", "price": 1.99, "originalPrice": 2.29, "rating": 4.5, "reviewCount": 79, "discountPercentage": 13, "isFeatured": false, "objectType": "product", "subCategoryID": "16", "categoryID": "4"}, {"objectID": "62", "name": "<PERSON><PERSON>", "description": "Classic tomato ketchup, 500ml", "imageUrl": "lib/assets/products/ketchup.png", "price": 2.49, "originalPrice": 2.99, "rating": 4.6, "reviewCount": 92, "discountPercentage": 17, "isFeatured": true, "objectType": "product", "subCategoryID": "17", "categoryID": "4"}, {"objectID": "63", "name": "Mayonnaise", "description": "Creamy mayonnaise, 400ml", "imageUrl": "lib/assets/products/mayonnaise.png", "price": 2.99, "originalPrice": 3.49, "rating": 4.5, "reviewCount": 84, "discountPercentage": 14, "isFeatured": false, "objectType": "product", "subCategoryID": "17", "categoryID": "4"}, {"objectID": "64", "name": "Soy Sauce", "description": "Traditional soy sauce, 250ml", "imageUrl": "lib/assets/products/soy-sauce.png", "price": 2.79, "originalPrice": 3.29, "rating": 4.7, "reviewCount": 96, "discountPercentage": 15, "isFeatured": true, "objectType": "product", "subCategoryID": "17", "categoryID": "4"}, {"objectID": "65", "name": "Hot Sauce", "description": "Spicy hot sauce, 150ml", "imageUrl": "lib/assets/products/hot-sauce.png", "price": 3.49, "originalPrice": 3.99, "rating": 4.8, "reviewCount": 105, "discountPercentage": 13, "isFeatured": true, "objectType": "product", "subCategoryID": "17", "categoryID": "4"}, {"objectID": "66", "name": "Black Tea", "description": "Premium black tea bags, pack of 50", "imageUrl": "lib/assets/products/black-tea.png", "price": 3.99, "originalPrice": 4.49, "rating": 4.7, "reviewCount": 112, "discountPercentage": 11, "isFeatured": true, "objectType": "product", "subCategoryID": "18", "categoryID": "4"}, {"objectID": "67", "name": "Green Tea", "description": "Organic green tea bags, pack of 40", "imageUrl": "lib/assets/products/green-tea.png", "price": 4.49, "originalPrice": 4.99, "rating": 4.8, "reviewCount": 98, "discountPercentage": 10, "isFeatured": true, "objectType": "product", "subCategoryID": "18", "categoryID": "4"}, {"objectID": "68", "name": "Ground Coffee", "description": "Medium roast ground coffee, 250g", "imageUrl": "lib/assets/products/ground-coffee.png", "price": 5.99, "originalPrice": 6.49, "rating": 4.9, "reviewCount": 124, "discountPercentage": 8, "isFeatured": true, "objectType": "product", "subCategoryID": "18", "categoryID": "4"}, {"objectID": "69", "name": "Herbal Tea", "description": "Relaxing chamomile tea bags, pack of 20", "imageUrl": "lib/assets/products/herbal-tea.png", "price": 3.79, "originalPrice": 4.29, "rating": 4.6, "reviewCount": 87, "discountPercentage": 12, "isFeatured": false, "objectType": "product", "subCategoryID": "18", "categoryID": "4"}, {"objectID": "70", "name": "Coffee Mug", "description": "Ceramic coffee mug, 350ml", "imageUrl": "lib/assets/products/coffee-mug.png", "price": 6.99, "originalPrice": 8.99, "rating": 4.7, "reviewCount": 108, "discountPercentage": 22, "isFeatured": true, "objectType": "product", "subCategoryID": "19", "categoryID": "4"}, {"objectID": "71", "name": "Tea Infuser", "description": "Stainless steel mesh tea infuser", "imageUrl": "lib/assets/products/tea-infuser.png", "price": 4.99, "originalPrice": 5.99, "rating": 4.6, "reviewCount": 87, "discountPercentage": 17, "isFeatured": false, "objectType": "product", "subCategoryID": "19", "categoryID": "4"}, {"objectID": "72", "name": "Coffee Filters", "description": "Paper coffee filters, pack of 100", "imageUrl": "lib/assets/products/coffee-filters.png", "price": 2.99, "originalPrice": 3.49, "rating": 4.5, "reviewCount": 76, "discountPercentage": 14, "isFeatured": false, "objectType": "product", "subCategoryID": "19", "categoryID": "4"}, {"objectID": "73", "name": "French Press", "description": "Glass french press coffee maker, 600ml", "imageUrl": "lib/assets/products/french-press.png", "price": 14.99, "originalPrice": 17.99, "rating": 4.8, "reviewCount": 124, "discountPercentage": 17, "isFeatured": true, "objectType": "product", "subCategoryID": "19", "categoryID": "4"}, {"objectID": "74", "name": "Potato Chips", "description": "Classic salted potato chips, 150g", "imageUrl": "lib/assets/products/potato-chips.png", "price": 1.99, "originalPrice": 2.49, "rating": 4.6, "reviewCount": 92, "discountPercentage": 20, "isFeatured": true, "objectType": "product", "subCategoryID": "20", "categoryID": "5"}, {"objectID": "75", "name": "Tortilla Chips", "description": "Corn tortilla chips, 200g", "imageUrl": "lib/assets/products/tortilla-chips.png", "price": 2.49, "originalPrice": 2.99, "rating": 4.5, "reviewCount": 84, "discountPercentage": 17, "isFeatured": false, "objectType": "product", "subCategoryID": "20", "categoryID": "5"}, {"objectID": "76", "name": "Popcorn", "description": "Microwave popcorn, butter flavor, pack of 3", "imageUrl": "lib/assets/products/popcorn.png", "price": 2.99, "originalPrice": 3.49, "rating": 4.7, "reviewCount": 98, "discountPercentage": 14, "isFeatured": true, "objectType": "product", "subCategoryID": "20", "categoryID": "5"}, {"objectID": "77", "name": "Mixed Nuts", "description": "Roasted and salted mixed nuts, 250g", "imageUrl": "lib/assets/products/mixed-nuts.png", "price": 5.99, "originalPrice": 6.99, "rating": 4.8, "reviewCount": 112, "discountPercentage": 14, "isFeatured": true, "objectType": "product", "subCategoryID": "20", "categoryID": "5"}, {"objectID": "78", "name": "Milk Chocolate", "description": "Creamy milk chocolate bar, 100g", "imageUrl": "lib/assets/products/milk-chocolate.png", "price": 2.49, "originalPrice": 2.99, "rating": 4.7, "reviewCount": 105, "discountPercentage": 17, "isFeatured": true, "objectType": "product", "subCategoryID": "21", "categoryID": "5"}, {"objectID": "79", "name": "Dark Chocolate", "description": "70% cocoa dark chocolate, 100g", "imageUrl": "lib/assets/products/dark-chocolate.png", "price": 2.99, "originalPrice": 3.49, "rating": 4.8, "reviewCount": 98, "discountPercentage": 14, "isFeatured": false, "objectType": "product", "subCategoryID": "21", "categoryID": "5"}, {"objectID": "80", "name": "Gummy Bears", "description": "Assorted fruit flavored gummy bears, 200g", "imageUrl": "lib/assets/products/gummy-bears.png", "price": 1.99, "originalPrice": 2.49, "rating": 4.6, "reviewCount": 87, "discountPercentage": 20, "isFeatured": true, "objectType": "product", "subCategoryID": "21", "categoryID": "5"}, {"objectID": "81", "name": "<PERSON><PERSON>", "description": "Soft caramel toffee candies, 150g", "imageUrl": "lib/assets/products/caramel-toffees.png", "price": 2.79, "originalPrice": 3.29, "rating": 4.5, "reviewCount": 76, "discountPercentage": 15, "isFeatured": false, "objectType": "product", "subCategoryID": "21", "categoryID": "5"}, {"objectID": "82", "name": "Chocolate Chip Cookies", "description": "Classic chocolate chip cookies, 200g", "imageUrl": "lib/assets/products/chocolate-chip-cookies.png", "price": 2.99, "originalPrice": 3.49, "rating": 4.7, "reviewCount": 112, "discountPercentage": 14, "isFeatured": true, "objectType": "product", "subCategoryID": "22", "categoryID": "5"}, {"objectID": "83", "name": "Butter Cookies", "description": "Rich butter cookies in tin box, 300g", "imageUrl": "lib/assets/products/butter-cookies.png", "price": 4.99, "originalPrice": 5.99, "rating": 4.6, "reviewCount": 94, "discountPercentage": 17, "isFeatured": false, "objectType": "product", "subCategoryID": "22", "categoryID": "5"}, {"objectID": "84", "name": "Oatmeal Cookies", "description": "Healthy oatmeal raisin cookies, 180g", "imageUrl": "lib/assets/products/oatmeal-cookies.png", "price": 3.49, "originalPrice": 3.99, "rating": 4.5, "reviewCount": 82, "discountPercentage": 13, "isFeatured": true, "objectType": "product", "subCategoryID": "22", "categoryID": "5"}, {"objectID": "85", "name": "Sandwich Cookies", "description": "Chocolate sandwich cookies with cream filling, 250g", "imageUrl": "lib/assets/products/sandwich-cookies.png", "price": 2.79, "originalPrice": 3.29, "rating": 4.6, "reviewCount": 88, "discountPercentage": 15, "isFeatured": false, "objectType": "product", "subCategoryID": "22", "categoryID": "5"}, {"objectID": "86", "name": "Cola", "description": "Classic cola soft drink, 2L", "imageUrl": "lib/assets/products/cola.png", "price": 1.99, "originalPrice": 2.49, "rating": 4.5, "reviewCount": 96, "discountPercentage": 20, "isFeatured": true, "objectType": "product", "subCategoryID": "23", "categoryID": "5"}, {"objectID": "87", "name": "Lemon Soda", "description": "Refreshing lemon soda, 1.5L", "imageUrl": "lib/assets/products/lemon-soda.png", "price": 1.79, "originalPrice": 2.29, "rating": 4.4, "reviewCount": 78, "discountPercentage": 22, "isFeatured": false, "objectType": "product", "subCategoryID": "23", "categoryID": "5"}, {"objectID": "88", "name": "Diet Cola", "description": "Sugar-free cola drink, 2L", "imageUrl": "lib/assets/products/diet-cola.png", "price": 1.99, "originalPrice": 2.49, "rating": 4.3, "reviewCount": 72, "discountPercentage": 20, "isFeatured": true, "objectType": "product", "subCategoryID": "23", "categoryID": "5"}, {"objectID": "89", "name": "<PERSON>", "description": "Classic ginger ale, 1L", "imageUrl": "lib/assets/products/ginger-ale.png", "price": 1.89, "originalPrice": 2.39, "rating": 4.6, "reviewCount": 84, "discountPercentage": 21, "isFeatured": false, "objectType": "product", "subCategoryID": "23", "categoryID": "5"}, {"objectID": "90", "name": "Orange Juice", "description": "100% pure orange juice, 1L", "imageUrl": "lib/assets/products/orange-juice.png", "price": 2.99, "originalPrice": 3.49, "rating": 4.8, "reviewCount": 116, "discountPercentage": 14, "isFeatured": true, "objectType": "product", "subCategoryID": "24", "categoryID": "5"}, {"objectID": "91", "name": "Apple Juice", "description": "Pure apple juice, not from concentrate, 1L", "imageUrl": "lib/assets/products/apple-juice.png", "price": 2.79, "originalPrice": 3.29, "rating": 4.7, "reviewCount": 98, "discountPercentage": 15, "isFeatured": false, "objectType": "product", "subCategoryID": "24", "categoryID": "5"}, {"objectID": "92", "name": "Mixed Fruit Juice", "description": "Blend of tropical fruits juice, 1L", "imageUrl": "lib/assets/products/mixed-fruit-juice.png", "price": 3.49, "originalPrice": 3.99, "rating": 4.6, "reviewCount": 87, "discountPercentage": 13, "isFeatured": true, "objectType": "product", "subCategoryID": "24", "categoryID": "5"}, {"objectID": "93", "name": "Cranberry Juice", "description": "Pure cranberry juice, 750ml", "imageUrl": "lib/assets/products/cranberry-juice.png", "price": 3.99, "originalPrice": 4.49, "rating": 4.5, "reviewCount": 76, "discountPercentage": 11, "isFeatured": false, "objectType": "product", "subCategoryID": "24", "categoryID": "5"}, {"objectID": "94", "name": "Sports Drink", "description": "Electrolyte sports drink, lemon flavor, 750ml", "imageUrl": "lib/assets/products/sports-drink.png", "price": 2.49, "originalPrice": 2.99, "rating": 4.6, "reviewCount": 92, "discountPercentage": 17, "isFeatured": true, "objectType": "product", "subCategoryID": "25", "categoryID": "5"}, {"objectID": "95", "name": "Energy Drink", "description": "Caffeine energy drink, 250ml", "imageUrl": "lib/assets/products/energy-drink.png", "price": 1.99, "originalPrice": 2.49, "rating": 4.4, "reviewCount": 78, "discountPercentage": 20, "isFeatured": false, "objectType": "product", "subCategoryID": "25", "categoryID": "5"}, {"objectID": "96", "name": "<PERSON><PERSON>", "description": "Ready-to-drink chocolate protein shake, 330ml", "imageUrl": "lib/assets/products/protein-shake.png", "price": 3.49, "originalPrice": 3.99, "rating": 4.7, "reviewCount": 105, "discountPercentage": 13, "isFeatured": true, "objectType": "product", "subCategoryID": "25", "categoryID": "5"}, {"objectID": "97", "name": "Coconut Water", "description": "Natural coconut water, 500ml", "imageUrl": "lib/assets/products/coconut-water.png", "price": 2.79, "originalPrice": 3.29, "rating": 4.8, "reviewCount": 96, "discountPercentage": 15, "isFeatured": true, "objectType": "product", "subCategoryID": "25", "categoryID": "5"}, {"objectID": "98", "name": "Frozen Peas", "description": "Garden fresh frozen peas, 500g", "imageUrl": "lib/assets/products/frozen-peas.png", "price": 1.99, "originalPrice": 2.49, "rating": 4.5, "reviewCount": 82, "discountPercentage": 20, "isFeatured": true, "objectType": "product", "subCategoryID": "26", "categoryID": "6"}, {"objectID": "99", "name": "Frozen Corn", "description": "Sweet corn kernels, frozen, 500g", "imageUrl": "lib/assets/products/frozen-corn.png", "price": 1.89, "originalPrice": 2.29, "rating": 4.4, "reviewCount": 76, "discountPercentage": 17, "isFeatured": false, "objectType": "product", "subCategoryID": "26", "categoryID": "6"}, {"objectID": "100", "name": "Mixed Vegetables", "description": "Frozen mixed vegetables, 750g", "imageUrl": "lib/assets/products/mixed-vegetables.png", "price": 2.99, "originalPrice": 3.49, "rating": 4.6, "reviewCount": 94, "discountPercentage": 14, "isFeatured": true, "objectType": "product", "subCategoryID": "26", "categoryID": "6"}, {"objectID": "101", "name": "<PERSON><PERSON><PERSON>", "description": "Chopped frozen spinach, 400g", "imageUrl": "lib/assets/products/frozen-spinach.png", "price": 2.49, "originalPrice": 2.99, "rating": 4.5, "reviewCount": 78, "discountPercentage": 17, "isFeatured": false, "objectType": "product", "subCategoryID": "26", "categoryID": "6"}, {"objectID": "102", "name": "Frozen Strawberries", "description": "Whole frozen strawberries, 400g", "imageUrl": "lib/assets/products/frozen-strawberries.png", "price": 3.99, "originalPrice": 4.49, "rating": 4.7, "reviewCount": 102, "discountPercentage": 11, "isFeatured": true, "objectType": "product", "subCategoryID": "27", "categoryID": "6"}, {"objectID": "103", "name": "Frozen Blueberries", "description": "Wild blueberries, frozen, 300g", "imageUrl": "lib/assets/products/frozen-blueberries.png", "price": 4.49, "originalPrice": 4.99, "rating": 4.8, "reviewCount": 96, "discountPercentage": 10, "isFeatured": true, "objectType": "product", "subCategoryID": "27", "categoryID": "6"}, {"objectID": "104", "name": "Frozen <PERSON>", "description": "Frozen mango chunks, 500g", "imageUrl": "lib/assets/products/frozen-mango.png", "price": 4.99, "originalPrice": 5.49, "rating": 4.6, "reviewCount": 87, "discountPercentage": 9, "isFeatured": false, "objectType": "product", "subCategoryID": "27", "categoryID": "6"}, {"objectID": "105", "name": "Mixed Berries", "description": "Frozen mixed berries, 400g", "imageUrl": "lib/assets/products/mixed-berries.png", "price": 4.79, "originalPrice": 5.29, "rating": 4.7, "reviewCount": 92, "discountPercentage": 9, "isFeatured": true, "objectType": "product", "subCategoryID": "27", "categoryID": "6"}, {"objectID": "106", "name": "Frozen Pizza", "description": "Margherita frozen pizza, ready to bake, 400g", "imageUrl": "lib/assets/products/frozen-pizza.png", "price": 4.99, "originalPrice": 5.99, "rating": 4.6, "reviewCount": 112, "discountPercentage": 17, "isFeatured": true, "objectType": "product", "subCategoryID": "28", "categoryID": "6"}, {"objectID": "107", "name": "<PERSON><PERSON><PERSON>", "description": "Beef lasagna, ready meal, 350g", "imageUrl": "lib/assets/products/frozen-lasagna.png", "price": 3.99, "originalPrice": 4.49, "rating": 4.5, "reviewCount": 86, "discountPercentage": 11, "isFeatured": false, "objectType": "product", "subCategoryID": "28", "categoryID": "6"}, {"objectID": "108", "name": "Frozen Pasta", "description": "Cheese tortellini, frozen, 500g", "imageUrl": "lib/assets/products/frozen-pasta.png", "price": 4.49, "originalPrice": 4.99, "rating": 4.7, "reviewCount": 94, "discountPercentage": 10, "isFeatured": true, "objectType": "product", "subCategoryID": "28", "categoryID": "6"}, {"objectID": "109", "name": "<PERSON>ozen <PERSON>", "description": "Vegetable curry with rice, 400g", "imageUrl": "lib/assets/products/frozen-curry.png", "price": 3.79, "originalPrice": 4.29, "rating": 4.4, "reviewCount": 78, "discountPercentage": 12, "isFeatured": false, "objectType": "product", "subCategoryID": "28", "categoryID": "6"}, {"objectID": "110", "name": "Vanilla Ice Cream", "description": "Classic vanilla ice cream, 1L", "imageUrl": "lib/assets/products/vanilla-ice-cream.png", "price": 3.99, "originalPrice": 4.49, "rating": 4.8, "reviewCount": 126, "discountPercentage": 11, "isFeatured": true, "objectType": "product", "subCategoryID": "29", "categoryID": "6"}, {"objectID": "111", "name": "Chocolate Ice Cream", "description": "Rich chocolate ice cream, 1L", "imageUrl": "lib/assets/products/chocolate-ice-cream.png", "price": 4.29, "originalPrice": 4.79, "rating": 4.7, "reviewCount": 114, "discountPercentage": 10, "isFeatured": true, "objectType": "product", "subCategoryID": "29", "categoryID": "6"}, {"objectID": "112", "name": "Strawberry Ice Cream", "description": "Creamy strawberry ice cream, 1L", "imageUrl": "lib/assets/products/strawberry-ice-cream.png", "price": 4.29, "originalPrice": 4.79, "rating": 4.6, "reviewCount": 98, "discountPercentage": 10, "isFeatured": false, "objectType": "product", "subCategoryID": "29", "categoryID": "6"}, {"objectID": "113", "name": "Ice Cream Bars", "description": "Chocolate coated vanilla ice cream bars, pack of 6", "imageUrl": "lib/assets/products/ice-cream-bars.png", "price": 4.99, "originalPrice": 5.49, "rating": 4.7, "reviewCount": 108, "discountPercentage": 9, "isFeatured": true, "objectType": "product", "subCategoryID": "29", "categoryID": "6"}, {"objectID": "114", "name": "Cheesecake", "description": "Frozen New York style cheesecake, 400g", "imageUrl": "lib/assets/products/cheesecake.png", "price": 5.99, "originalPrice": 6.99, "rating": 4.8, "reviewCount": 118, "discountPercentage": 14, "isFeatured": true, "objectType": "product", "subCategoryID": "30", "categoryID": "6"}, {"objectID": "115", "name": "Chocolate Cake", "description": "Frozen chocolate fudge cake, 500g", "imageUrl": "lib/assets/products/chocolate-cake.png", "price": 6.49, "originalPrice": 7.49, "rating": 4.9, "reviewCount": 124, "discountPercentage": 13, "isFeatured": true, "objectType": "product", "subCategoryID": "30", "categoryID": "6"}, {"objectID": "116", "name": "Apple Pie", "description": "Frozen apple pie, ready to bake, 600g", "imageUrl": "lib/assets/products/apple-pie.png", "price": 5.49, "originalPrice": 5.99, "rating": 4.7, "reviewCount": 96, "discountPercentage": 8, "isFeatured": false, "objectType": "product", "subCategoryID": "30", "categoryID": "6"}, {"objectID": "117", "name": "Tiramisu", "description": "Frozen tiramisu dessert, 300g", "imageUrl": "lib/assets/products/tiramisu.png", "price": 4.99, "originalPrice": 5.49, "rating": 4.8, "reviewCount": 102, "discountPercentage": 9, "isFeatured": true, "objectType": "product", "subCategoryID": "30", "categoryID": "6"}, {"objectID": "118", "name": "Chicken Breast", "description": "Boneless chicken breast fillets, 500g", "imageUrl": "lib/assets/products/chicken-breast.png", "price": 5.99, "originalPrice": 6.99, "rating": 4.7, "reviewCount": 112, "discountPercentage": 14, "isFeatured": true, "objectType": "product", "subCategoryID": "31", "categoryID": "7"}, {"objectID": "119", "name": "Chicken Thighs", "description": "Fresh chicken thighs, 600g", "imageUrl": "lib/assets/products/chicken-thighs.png", "price": 4.99, "originalPrice": 5.49, "rating": 4.6, "reviewCount": 94, "discountPercentage": 9, "isFeatured": false, "objectType": "product", "subCategoryID": "31", "categoryID": "7"}, {"objectID": "120", "name": "Whole Chicken", "description": "Fresh whole chicken, 1.5kg", "imageUrl": "lib/assets/products/whole-chicken.png", "price": 7.99, "originalPrice": 8.99, "rating": 4.8, "reviewCount": 124, "discountPercentage": 11, "isFeatured": true, "objectType": "product", "subCategoryID": "31", "categoryID": "7"}, {"objectID": "121", "name": "Chicken Drumsticks", "description": "Fresh chicken drumsticks, pack of 6", "imageUrl": "lib/assets/products/chicken-drumsticks.png", "price": 4.49, "originalPrice": 4.99, "rating": 4.5, "reviewCount": 86, "discountPercentage": 10, "isFeatured": false, "objectType": "product", "subCategoryID": "31", "categoryID": "7"}, {"objectID": "122", "name": "<PERSON>t", "description": "Fresh Atlantic salmon fillet, 300g", "imageUrl": "lib/assets/products/salmon-fillet.png", "price": 8.99, "originalPrice": 9.99, "rating": 4.8, "reviewCount": 118, "discountPercentage": 10, "isFeatured": true, "objectType": "product", "subCategoryID": "32", "categoryID": "7"}, {"objectID": "123", "name": "<PERSON><PERSON><PERSON>", "description": "Fresh tilapia fillets, 400g", "imageUrl": "lib/assets/products/tilapia-fillet.png", "price": 6.99, "originalPrice": 7.99, "rating": 4.6, "reviewCount": 92, "discountPercentage": 13, "isFeatured": false, "objectType": "product", "subCategoryID": "32", "categoryID": "7"}, {"objectID": "124", "name": "Prawns", "description": "Fresh tiger prawns, 250g", "imageUrl": "lib/assets/products/prawns.png", "price": 9.99, "originalPrice": 11.99, "rating": 4.7, "reviewCount": 108, "discountPercentage": 17, "isFeatured": true, "objectType": "product", "subCategoryID": "32", "categoryID": "7"}, {"objectID": "125", "name": "Sea Bass", "description": "Fresh whole sea bass, 500g", "imageUrl": "lib/assets/products/sea-bass.png", "price": 7.99, "originalPrice": 8.99, "rating": 4.8, "reviewCount": 96, "discountPercentage": 11, "isFeatured": true, "objectType": "product", "subCategoryID": "32", "categoryID": "7"}, {"objectID": "126", "name": "<PERSON><PERSON>", "description": "Fresh mutton chops, 500g", "imageUrl": "lib/assets/products/mutton-chops.png", "price": 8.99, "originalPrice": 9.99, "rating": 4.7, "reviewCount": 102, "discountPercentage": 10, "isFeatured": true, "objectType": "product", "subCategoryID": "33", "categoryID": "7"}, {"objectID": "127", "name": "<PERSON><PERSON>", "description": "Fresh ground mutton, 400g", "imageUrl": "lib/assets/products/mutton-mince.png", "price": 7.49, "originalPrice": 8.49, "rating": 4.6, "reviewCount": 88, "discountPercentage": 12, "isFeatured": false, "objectType": "product", "subCategoryID": "33", "categoryID": "7"}, {"objectID": "128", "name": "<PERSON><PERSON> Curry Cut", "description": "Fresh mutton curry cut pieces, 500g", "imageUrl": "lib/assets/products/mutton-curry-cut.png", "price": 9.49, "originalPrice": 10.99, "rating": 4.8, "reviewCount": 116, "discountPercentage": 14, "isFeatured": true, "objectType": "product", "subCategoryID": "33", "categoryID": "7"}, {"objectID": "129", "name": "<PERSON><PERSON> Ribs", "description": "Fresh mutton ribs, 600g", "imageUrl": "lib/assets/products/mutton-ribs.png", "price": 10.99, "originalPrice": 12.49, "rating": 4.7, "reviewCount": 94, "discountPercentage": 12, "isFeatured": false, "objectType": "product", "subCategoryID": "33", "categoryID": "7"}, {"objectID": "130", "name": "Beef Steak", "description": "Premium beef sirloin steak, 300g", "imageUrl": "lib/assets/products/beef-steak.png", "price": 9.99, "originalPrice": 11.99, "rating": 4.8, "reviewCount": 124, "discountPercentage": 17, "isFeatured": true, "objectType": "product", "subCategoryID": "34", "categoryID": "7"}, {"objectID": "131", "name": "Ground Beef", "description": "Lean ground beef, 500g", "imageUrl": "lib/assets/products/ground-beef.png", "price": 6.99, "originalPrice": 7.99, "rating": 4.6, "reviewCount": 98, "discountPercentage": 13, "isFeatured": false, "objectType": "product", "subCategoryID": "34", "categoryID": "7"}, {"objectID": "132", "name": "<PERSON><PERSON>", "description": "Fresh beef ribs, 700g", "imageUrl": "lib/assets/products/beef-ribs.png", "price": 11.99, "originalPrice": 13.99, "rating": 4.7, "reviewCount": 106, "discountPercentage": 14, "isFeatured": true, "objectType": "product", "subCategoryID": "34", "categoryID": "7"}, {"objectID": "133", "name": "Beef Brisket", "description": "Fresh beef brisket, 800g", "imageUrl": "lib/assets/products/beef-brisket.png", "price": 12.99, "originalPrice": 14.99, "rating": 4.8, "reviewCount": 112, "discountPercentage": 13, "isFeatured": false, "objectType": "product", "subCategoryID": "34", "categoryID": "7"}, {"objectID": "134", "name": "Tandoori Chicken", "description": "Marinated tandoori chicken, ready to cook, 500g", "imageUrl": "lib/assets/products/tandoori-chicken.png", "price": 7.99, "originalPrice": 8.99, "rating": 4.8, "reviewCount": 118, "discountPercentage": 11, "isFeatured": true, "objectType": "product", "subCategoryID": "35", "categoryID": "7"}, {"objectID": "135", "name": "Lemon Herb Chicken", "description": "Lemon and herb marinated chicken, 450g", "imageUrl": "lib/assets/products/lemon-herb-chicken.png", "price": 7.49, "originalPrice": 8.49, "rating": 4.7, "reviewCount": 96, "discountPercentage": 12, "isFeatured": false, "objectType": "product", "subCategoryID": "35", "categoryID": "7"}, {"objectID": "136", "name": "BBQ Pork Ribs", "description": "BBQ marinated pork ribs, ready to cook, 600g", "imageUrl": "lib/assets/products/bbq-pork-ribs.png", "price": 9.99, "originalPrice": 11.49, "rating": 4.9, "reviewCount": 126, "discountPercentage": 13, "isFeatured": true, "objectType": "product", "subCategoryID": "35", "categoryID": "7"}, {"objectID": "137", "name": "<PERSON><PERSON><PERSON>", "description": "Garlic butter marinated fish fillets, 400g", "imageUrl": "lib/assets/products/garlic-butter-fish.png", "price": 8.49, "originalPrice": 9.49, "rating": 4.6, "reviewCount": 88, "discountPercentage": 11, "isFeatured": false, "objectType": "product", "subCategoryID": "35", "categoryID": "7"}, {"objectID": "138", "name": "Pork Chops", "description": "Fresh pork chops, 500g", "imageUrl": "lib/assets/products/pork-chops.png", "price": 7.99, "originalPrice": 8.99, "rating": 4.7, "reviewCount": 102, "discountPercentage": 11, "isFeatured": true, "objectType": "product", "subCategoryID": "36", "categoryID": "7"}, {"objectID": "139", "name": "Pork Belly", "description": "Fresh pork belly slices, 400g", "imageUrl": "lib/assets/products/pork-belly.png", "price": 8.49, "originalPrice": 9.49, "rating": 4.8, "reviewCount": 94, "discountPercentage": 11, "isFeatured": false, "objectType": "product", "subCategoryID": "36", "categoryID": "7"}, {"objectID": "140", "name": "Ground Pork", "description": "Fresh ground pork, 500g", "imageUrl": "lib/assets/products/ground-pork.png", "price": 5.99, "originalPrice": 6.99, "rating": 4.5, "reviewCount": 82, "discountPercentage": 14, "isFeatured": true, "objectType": "product", "subCategoryID": "36", "categoryID": "7"}, {"objectID": "141", "name": "Pork Tenderloin", "description": "Fresh pork tenderloin, 450g", "imageUrl": "lib/assets/products/pork-tenderloin.png", "price": 9.49, "originalPrice": 10.49, "rating": 4.7, "reviewCount": 88, "discountPercentage": 10, "isFeatured": false, "objectType": "product", "subCategoryID": "36", "categoryID": "7"}, {"objectID": "142", "name": "Tomatoes", "description": "Fresh red tomatoes, 500g", "imageUrl": "lib/assets/products/tomatoes.png", "price": 1.99, "originalPrice": 2.49, "rating": 4.6, "reviewCount": 112, "discountPercentage": 20, "isFeatured": true, "objectType": "product", "subCategoryID": "37", "categoryID": "8"}, {"objectID": "143", "name": "Onions", "description": "Fresh red onions, 1kg", "imageUrl": "lib/assets/products/onions.png", "price": 1.49, "originalPrice": 1.99, "rating": 4.5, "reviewCount": 96, "discountPercentage": 25, "isFeatured": false, "objectType": "product", "subCategoryID": "37", "categoryID": "8"}, {"objectID": "144", "name": "Potatoes", "description": "Fresh potatoes, 2kg", "imageUrl": "lib/assets/products/potatoes.png", "price": 2.49, "originalPrice": 2.99, "rating": 4.7, "reviewCount": 118, "discountPercentage": 17, "isFeatured": true, "objectType": "product", "subCategoryID": "37", "categoryID": "8"}, {"objectID": "145", "name": "Carrots", "description": "Fresh carrots, 500g", "imageUrl": "lib/assets/products/carrots.png", "price": 1.29, "originalPrice": 1.69, "rating": 4.6, "reviewCount": 92, "discountPercentage": 24, "isFeatured": false, "objectType": "product", "subCategoryID": "37", "categoryID": "8"}, {"objectID": "146", "name": "Organic Spinach", "description": "Organic baby spinach, 200g", "imageUrl": "lib/assets/products/organic-spinach.png", "price": 2.99, "originalPrice": 3.49, "rating": 4.8, "reviewCount": 106, "discountPercentage": 14, "isFeatured": true, "objectType": "product", "subCategoryID": "38", "categoryID": "8"}, {"objectID": "147", "name": "Organic Kale", "description": "Organic kale, 200g", "imageUrl": "lib/assets/products/organic-kale.png", "price": 2.79, "originalPrice": 3.29, "rating": 4.7, "reviewCount": 88, "discountPercentage": 15, "isFeatured": false, "objectType": "product", "subCategoryID": "38", "categoryID": "8"}, {"objectID": "148", "name": "Organic Tomatoes", "description": "Organic cherry tomatoes, 250g", "imageUrl": "lib/assets/products/organic-tomatoes.png", "price": 2.99, "originalPrice": 3.49, "rating": 4.8, "reviewCount": 102, "discountPercentage": 14, "isFeatured": true, "objectType": "product", "subCategoryID": "38", "categoryID": "8"}, {"objectID": "149", "name": "Organic Bell Peppers", "description": "Organic mixed bell peppers, 3 pack", "imageUrl": "lib/assets/products/organic-bell-peppers.png", "price": 3.99, "originalPrice": 4.49, "rating": 4.6, "reviewCount": 94, "discountPercentage": 11, "isFeatured": false, "objectType": "product", "subCategoryID": "38", "categoryID": "8"}, {"objectID": "150", "name": "Banana<PERSON>", "description": "Fresh bananas, 1kg", "imageUrl": "lib/assets/products/bananas.png", "price": 1.99, "originalPrice": 2.49, "rating": 4.7, "reviewCount": 126, "discountPercentage": 20, "isFeatured": true, "objectType": "product", "subCategoryID": "39", "categoryID": "8"}, {"objectID": "151", "name": "Apples", "description": "Fresh red apples, 1kg", "imageUrl": "lib/assets/products/apples.png", "price": 2.49, "originalPrice": 2.99, "rating": 4.6, "reviewCount": 108, "discountPercentage": 17, "isFeatured": false, "objectType": "product", "subCategoryID": "39", "categoryID": "8"}, {"objectID": "152", "name": "Oranges", "description": "Fresh juicy oranges, 1kg", "imageUrl": "lib/assets/products/oranges.png", "price": 2.99, "originalPrice": 3.49, "rating": 4.8, "reviewCount": 116, "discountPercentage": 14, "isFeatured": true, "objectType": "product", "subCategoryID": "39", "categoryID": "8"}, {"objectID": "153", "name": "Grapes", "description": "Fresh seedless grapes, 500g", "imageUrl": "lib/assets/products/grapes.png", "price": 3.49, "originalPrice": 3.99, "rating": 4.7, "reviewCount": 98, "discountPercentage": 13, "isFeatured": false, "objectType": "product", "subCategoryID": "39", "categoryID": "8"}, {"objectID": "154", "name": "Organic Apples", "description": "Organic red apples, 1kg", "imageUrl": "lib/assets/products/organic-apples.png", "price": 3.99, "originalPrice": 4.49, "rating": 4.8, "reviewCount": 112, "discountPercentage": 11, "isFeatured": true, "objectType": "product", "subCategoryID": "40", "categoryID": "8"}, {"objectID": "155", "name": "Organic Bananas", "description": "Organic bananas, 1kg", "imageUrl": "lib/assets/products/organic-bananas.png", "price": 2.99, "originalPrice": 3.49, "rating": 4.7, "reviewCount": 98, "discountPercentage": 14, "isFeatured": false, "objectType": "product", "subCategoryID": "40", "categoryID": "8"}, {"objectID": "156", "name": "Organic Strawberries", "description": "Organic strawberries, 250g", "imageUrl": "lib/assets/products/organic-strawberries.png", "price": 4.49, "originalPrice": 4.99, "rating": 4.9, "reviewCount": 124, "discountPercentage": 10, "isFeatured": true, "objectType": "product", "subCategoryID": "40", "categoryID": "8"}, {"objectID": "157", "name": "Organic Blueberries", "description": "Organic blueberries, 125g", "imageUrl": "lib/assets/products/organic-blueberries.png", "price": 3.99, "originalPrice": 4.49, "rating": 4.8, "reviewCount": 106, "discountPercentage": 11, "isFeatured": false, "objectType": "product", "subCategoryID": "40", "categoryID": "8"}, {"objectID": "158", "name": "Fresh Basil", "description": "Fresh basil bunch, 30g", "imageUrl": "lib/assets/products/fresh-basil.png", "price": 1.49, "originalPrice": 1.99, "rating": 4.7, "reviewCount": 88, "discountPercentage": 25, "isFeatured": true, "objectType": "product", "subCategoryID": "41", "categoryID": "8"}, {"objectID": "159", "name": "Fresh Coriander", "description": "Fresh coriander bunch, 30g", "imageUrl": "lib/assets/products/fresh-coriander.png", "price": 1.29, "originalPrice": 1.79, "rating": 4.6, "reviewCount": 82, "discountPercentage": 28, "isFeatured": false, "objectType": "product", "subCategoryID": "41", "categoryID": "8"}, {"objectID": "160", "name": "Fresh Mint", "description": "Fresh mint bunch, 30g", "imageUrl": "lib/assets/products/fresh-mint.png", "price": 1.49, "originalPrice": 1.99, "rating": 4.7, "reviewCount": 94, "discountPercentage": 25, "isFeatured": true, "objectType": "product", "subCategoryID": "41", "categoryID": "8"}, {"objectID": "161", "name": "Fresh <PERSON>", "description": "Fresh rosemary sprigs, 20g", "imageUrl": "lib/assets/products/fresh-rosemary.png", "price": 1.69, "originalPrice": 1.99, "rating": 4.8, "reviewCount": 86, "discountPercentage": 15, "isFeatured": false, "objectType": "product", "subCategoryID": "41", "categoryID": "8"}, {"objectID": "162", "name": "Dragon Fruit", "description": "Fresh dragon fruit, 1 piece", "imageUrl": "lib/assets/products/dragon-fruit.png", "price": 3.99, "originalPrice": 4.49, "rating": 4.7, "reviewCount": 96, "discountPercentage": 11, "isFeatured": true, "objectType": "product", "subCategoryID": "42", "categoryID": "8"}, {"objectID": "163", "name": "Passion Fruit", "description": "Fresh passion fruit, pack of 3", "imageUrl": "lib/assets/products/passion-fruit.png", "price": 4.49, "originalPrice": 4.99, "rating": 4.8, "reviewCount": 88, "discountPercentage": 10, "isFeatured": false, "objectType": "product", "subCategoryID": "42", "categoryID": "8"}, {"objectID": "164", "name": "Star Fruit", "description": "Fresh star fruit, 2 pieces", "imageUrl": "lib/assets/products/star-fruit.png", "price": 3.79, "originalPrice": 4.29, "rating": 4.6, "reviewCount": 76, "discountPercentage": 12, "isFeatured": true, "objectType": "product", "subCategoryID": "42", "categoryID": "8"}, {"objectID": "165", "name": "<PERSON><PERSON><PERSON>", "description": "Fresh lychee, 250g", "imageUrl": "lib/assets/products/lychee.png", "price": 4.99, "originalPrice": 5.49, "rating": 4.9, "reviewCount": 102, "discountPercentage": 9, "isFeatured": false, "objectType": "product", "subCategoryID": "42", "categoryID": "8"}, {"objectID": "166", "name": "All-Purpose Cleaner", "description": "Multi-surface cleaner, lemon scent, 750ml", "imageUrl": "lib/assets/products/all-purpose-cleaner.png", "price": 2.99, "originalPrice": 3.49, "rating": 4.7, "reviewCount": 118, "discountPercentage": 14, "isFeatured": true, "objectType": "product", "subCategoryID": "43", "categoryID": "9"}, {"objectID": "167", "name": "Glass Cleaner", "description": "Streak-free glass cleaner, 500ml", "imageUrl": "lib/assets/products/glass-cleaner.png", "price": 2.49, "originalPrice": 2.99, "rating": 4.6, "reviewCount": 96, "discountPercentage": 17, "isFeatured": false, "objectType": "product", "subCategoryID": "43", "categoryID": "9"}, {"objectID": "168", "name": "Kitchen Cleaner", "description": "Degreasing kitchen cleaner, 500ml", "imageUrl": "lib/assets/products/kitchen-cleaner.png", "price": 3.49, "originalPrice": 3.99, "rating": 4.8, "reviewCount": 124, "discountPercentage": 13, "isFeatured": true, "objectType": "product", "subCategoryID": "43", "categoryID": "9"}, {"objectID": "169", "name": "Floor Cleaner", "description": "Multi-surface floor cleaner, 1L", "imageUrl": "lib/assets/products/floor-cleaner.png", "price": 3.99, "originalPrice": 4.49, "rating": 4.7, "reviewCount": 108, "discountPercentage": 11, "isFeatured": false, "objectType": "product", "subCategoryID": "43", "categoryID": "9"}, {"objectID": "170", "name": "<PERSON><PERSON><PERSON> Deter<PERSON>", "description": "Concentrated liquid laundry detergent, 2L", "imageUrl": "lib/assets/products/laundry-detergent.png", "price": 7.99, "originalPrice": 8.99, "rating": 4.8, "reviewCount": 136, "discountPercentage": 11, "isFeatured": true, "objectType": "product", "subCategoryID": "44", "categoryID": "9"}, {"objectID": "171", "name": "Fabric Softener", "description": "Concentrated fabric softener, fresh scent, 1L", "imageUrl": "lib/assets/products/fabric-softener.png", "price": 4.49, "originalPrice": 4.99, "rating": 4.7, "reviewCount": 98, "discountPercentage": 10, "isFeatured": false, "objectType": "product", "subCategoryID": "44", "categoryID": "9"}, {"objectID": "172", "name": "Stain Remover", "description": "Pre-wash stain remover spray, 500ml", "imageUrl": "lib/assets/products/stain-remover.png", "price": 3.99, "originalPrice": 4.49, "rating": 4.8, "reviewCount": 112, "discountPercentage": 11, "isFeatured": true, "objectType": "product", "subCategoryID": "44", "categoryID": "9"}, {"objectID": "173", "name": "<PERSON><PERSON><PERSON>", "description": "3-in-1 laundry detergent pods, pack of 30", "imageUrl": "lib/assets/products/laundry-pods.png", "price": 8.99, "originalPrice": 9.99, "rating": 4.9, "reviewCount": 142, "discountPercentage": 10, "isFeatured": false, "objectType": "product", "subCategoryID": "44", "categoryID": "9"}, {"objectID": "174", "name": "<PERSON><PERSON><PERSON>", "description": "Powerful toilet bowl cleaner, 750ml", "imageUrl": "lib/assets/products/toilet-cleaner.png", "price": 2.79, "originalPrice": 3.29, "rating": 4.7, "reviewCount": 106, "discountPercentage": 15, "isFeatured": true, "objectType": "product", "subCategoryID": "45", "categoryID": "9"}, {"objectID": "175", "name": "Bathroom Cleaner", "description": "Anti-bacterial bathroom cleaner, 500ml", "imageUrl": "lib/assets/products/bathroom-cleaner.png", "price": 3.49, "originalPrice": 3.99, "rating": 4.8, "reviewCount": 118, "discountPercentage": 13, "isFeatured": false, "objectType": "product", "subCategoryID": "45", "categoryID": "9"}, {"objectID": "176", "name": "Shower Cleaner", "description": "Daily shower spray cleaner, 500ml", "imageUrl": "lib/assets/products/shower-cleaner.png", "price": 3.29, "originalPrice": 3.79, "rating": 4.6, "reviewCount": 92, "discountPercentage": 13, "isFeatured": true, "objectType": "product", "subCategoryID": "45", "categoryID": "9"}, {"objectID": "177", "name": "Mold Remover", "description": "Powerful mold and mildew remover, 500ml", "imageUrl": "lib/assets/products/mold-remover.png", "price": 4.49, "originalPrice": 4.99, "rating": 4.8, "reviewCount": 104, "discountPercentage": 10, "isFeatured": false, "objectType": "product", "subCategoryID": "45", "categoryID": "9"}, {"objectID": "178", "name": "Dish Soap", "description": "Concentrated dish washing liquid, lemon scent, 750ml", "imageUrl": "lib/assets/products/dish-soap.png", "price": 2.49, "originalPrice": 2.99, "rating": 4.7, "reviewCount": 128, "discountPercentage": 17, "isFeatured": true, "objectType": "product", "subCategoryID": "46", "categoryID": "9"}, {"objectID": "179", "name": "Dishwasher Tablets", "description": "All-in-one dishwasher tablets, pack of 30", "imageUrl": "lib/assets/products/dishwasher-tablets.png", "price": 7.99, "originalPrice": 8.99, "rating": 4.8, "reviewCount": 136, "discountPercentage": 11, "isFeatured": false, "objectType": "product", "subCategoryID": "46", "categoryID": "9"}, {"objectID": "180", "name": "Sponges", "description": "Multi-purpose kitchen sponges, pack of 5", "imageUrl": "lib/assets/products/sponges.png", "price": 1.99, "originalPrice": 2.49, "rating": 4.6, "reviewCount": 98, "discountPercentage": 20, "isFeatured": true, "objectType": "product", "subCategoryID": "46", "categoryID": "9"}, {"objectID": "181", "name": "Kitchen Wipes", "description": "Antibacterial kitchen wipes, pack of 80", "imageUrl": "lib/assets/products/kitchen-wipes.png", "price": 2.79, "originalPrice": 3.29, "rating": 4.7, "reviewCount": 106, "discountPercentage": 15, "isFeatured": false, "objectType": "product", "subCategoryID": "46", "categoryID": "9"}, {"objectID": "182", "name": "Toilet Paper", "description": "Soft toilet paper rolls, pack of 9", "imageUrl": "lib/assets/products/toilet-paper.png", "price": 4.99, "originalPrice": 5.99, "rating": 4.8, "reviewCount": 156, "discountPercentage": 17, "isFeatured": true, "objectType": "product", "subCategoryID": "47", "categoryID": "9"}, {"objectID": "183", "name": "Kitchen Towels", "description": "Absorbent kitchen paper towels, pack of 4", "imageUrl": "lib/assets/products/kitchen-towels.png", "price": 3.49, "originalPrice": 3.99, "rating": 4.7, "reviewCount": 124, "discountPercentage": 13, "isFeatured": false, "objectType": "product", "subCategoryID": "47", "categoryID": "9"}, {"objectID": "184", "name": "<PERSON><PERSON><PERSON>", "description": "Soft facial tissues, pack of 6 boxes", "imageUrl": "lib/assets/products/facial-tissues.png", "price": 3.99, "originalPrice": 4.49, "rating": 4.6, "reviewCount": 112, "discountPercentage": 11, "isFeatured": true, "objectType": "product", "subCategoryID": "47", "categoryID": "9"}, {"objectID": "185", "name": "<PERSON><PERSON><PERSON>", "description": "Paper napkins, pack of 100", "imageUrl": "lib/assets/products/napkins.png", "price": 1.99, "originalPrice": 2.49, "rating": 4.5, "reviewCount": 96, "discountPercentage": 20, "isFeatured": false, "objectType": "product", "subCategoryID": "47", "categoryID": "9"}, {"objectID": "186", "name": "Ant <PERSON>", "description": "Effective ant killer spray, 300ml", "imageUrl": "lib/assets/products/ant-killer.png", "price": 3.99, "originalPrice": 4.49, "rating": 4.7, "reviewCount": 108, "discountPercentage": 11, "isFeatured": true, "objectType": "product", "subCategoryID": "48", "categoryID": "9"}, {"objectID": "187", "name": "Mosquito Repellent", "description": "Long-lasting mosquito repellent spray, 200ml", "imageUrl": "lib/assets/products/mosquito-repellent.png", "price": 4.49, "originalPrice": 4.99, "rating": 4.8, "reviewCount": 126, "discountPercentage": 10, "isFeatured": false, "objectType": "product", "subCategoryID": "48", "categoryID": "9"}, {"objectID": "188", "name": "Cockroach Traps", "description": "Effective cockroach bait traps, pack of 6", "imageUrl": "lib/assets/products/cockroach-traps.png", "price": 5.99, "originalPrice": 6.99, "rating": 4.6, "reviewCount": 98, "discountPercentage": 14, "isFeatured": true, "objectType": "product", "subCategoryID": "48", "categoryID": "9"}, {"objectID": "189", "name": "Fly Swatter", "description": "Durable plastic fly swatter", "imageUrl": "lib/assets/products/fly-swatter.png", "price": 1.49, "originalPrice": 1.99, "rating": 4.4, "reviewCount": 82, "discountPercentage": 25, "isFeatured": false, "objectType": "product", "subCategoryID": "48", "categoryID": "9"}, {"objectID": "190", "name": "Room Spray", "description": "Fresh linen scent room spray, 300ml", "imageUrl": "lib/assets/products/room-spray.png", "price": 3.49, "originalPrice": 3.99, "rating": 4.7, "reviewCount": 112, "discountPercentage": 13, "isFeatured": true, "objectType": "product", "subCategoryID": "49", "categoryID": "10"}, {"objectID": "191", "name": "Scented Candle", "description": "Vanilla scented candle, 30 hour burn time", "imageUrl": "lib/assets/products/scented-candle.png", "price": 4.99, "originalPrice": 5.99, "rating": 4.8, "reviewCount": 126, "discountPercentage": 17, "isFeatured": false, "objectType": "product", "subCategoryID": "49", "categoryID": "10"}, {"objectID": "192", "name": "<PERSON> Diffuser", "description": "Lavender reed diffuser, 100ml", "imageUrl": "lib/assets/products/reed-diffuser.png", "price": 7.99, "originalPrice": 8.99, "rating": 4.9, "reviewCount": 138, "discountPercentage": 11, "isFeatured": true, "objectType": "product", "subCategoryID": "49", "categoryID": "10"}, {"objectID": "193", "name": "Car Air Freshener", "description": "Long-lasting car air freshener, ocean breeze", "imageUrl": "lib/assets/products/car-air-freshener.png", "price": 2.49, "originalPrice": 2.99, "rating": 4.6, "reviewCount": 96, "discountPercentage": 17, "isFeatured": false, "objectType": "product", "subCategoryID": "49", "categoryID": "10"}, {"objectID": "194", "name": "Shampoo", "description": "Moisturizing shampoo for all hair types, 400ml", "imageUrl": "lib/assets/products/shampoo.png", "price": 4.99, "originalPrice": 5.99, "rating": 4.8, "reviewCount": 142, "discountPercentage": 17, "isFeatured": true, "objectType": "product", "subCategoryID": "50", "categoryID": "10"}, {"objectID": "195", "name": "Conditioner", "description": "Nourishing hair conditioner, 400ml", "imageUrl": "lib/assets/products/conditioner.png", "price": 4.99, "originalPrice": 5.99, "rating": 4.7, "reviewCount": 128, "discountPercentage": 17, "isFeatured": false, "objectType": "product", "subCategoryID": "50", "categoryID": "10"}, {"objectID": "196", "name": "Shower <PERSON><PERSON>", "description": "Refreshing shower gel, coconut scent, 500ml", "imageUrl": "lib/assets/products/shower-gel.png", "price": 3.99, "originalPrice": 4.49, "rating": 4.8, "reviewCount": 136, "discountPercentage": 11, "isFeatured": true, "objectType": "product", "subCategoryID": "50", "categoryID": "10"}, {"objectID": "197", "name": "Body Lotion", "description": "Moisturizing body lotion, 400ml", "imageUrl": "lib/assets/products/body-lotion.png", "price": 5.49, "originalPrice": 5.99, "rating": 4.9, "reviewCount": 148, "discountPercentage": 8, "isFeatured": false, "objectType": "product", "subCategoryID": "50", "categoryID": "10"}], "orders": [{"id": "ORD-1703123456789", "customerId": "customer_123", "customerName": "<PERSON>", "status": "delivered", "orderDate": "2024-06-25T10:30:00Z", "deliveryDate": "2024-06-25T14:45:00Z", "estimatedDeliveryTime": "30-45 mins", "totalAmount": 485.5, "subtotal": 420.0, "tax": 25.5, "deliveryFee": 40.0, "discount": 0.0, "paymentMethod": "Cash on Delivery", "deliveryAddress": {"id": "addr_1", "name": "<PERSON>", "phone": "+91 9876543210", "addressLine1": "123 Main Street", "addressLine2": "Near City Mall", "city": "Mumbai", "state": "Maharashtra", "pincode": "400001", "landmark": "Opposite Metro Station", "addressType": "Home"}, "items": [{"id": "item_1", "productId": "1", "name": "Fresh Bananas", "imageUrl": "assets/products/banana.png", "price": 120.0, "quantity": 2, "unit": "kg", "discountedPrice": 120.0, "facilityId": "facility_1", "facilityName": "Fresh Mart Store", "skuID": "SKU001"}, {"id": "item_2", "productId": "15", "name": "Organic Milk", "imageUrl": "assets/products/milk.png", "price": 150.0, "quantity": 3, "unit": "liter", "discountedPrice": 150.0, "facilityId": "facility_1", "facilityName": "Fresh Mart Store", "skuID": "SKU015"}, {"id": "item_3", "productId": "25", "name": "<PERSON> Bread", "imageUrl": "assets/products/bread.png", "price": 150.0, "quantity": 1, "unit": "piece", "discountedPrice": 150.0, "facilityId": "facility_1", "facilityName": "Fresh Mart Store", "skuID": "SKU025"}], "orderTimeline": [{"status": "placed", "timestamp": "2024-06-25T10:30:00Z", "title": "Order Placed", "description": "Your order has been placed successfully"}, {"status": "confirmed", "timestamp": "2024-06-25T10:35:00Z", "title": "Order Confirmed", "description": "Your order has been confirmed and is being prepared"}, {"status": "preparing", "timestamp": "2024-06-25T10:45:00Z", "title": "Preparing Order", "description": "Your order is being prepared at the store"}, {"status": "out_for_delivery", "timestamp": "2024-06-25T14:15:00Z", "title": "Out for Delivery", "description": "Your order is on the way to your location"}, {"status": "delivered", "timestamp": "2024-06-25T14:45:00Z", "title": "Delivered", "description": "Your order has been delivered successfully"}], "canCancel": false, "canReorder": true, "objectType": "order"}, {"id": "ORD-1703123456790", "customerId": "customer_123", "customerName": "<PERSON>", "status": "out_for_delivery", "orderDate": "2024-06-26T09:15:00Z", "deliveryDate": null, "estimatedDeliveryTime": "15-30 mins", "totalAmount": 325.75, "subtotal": 280.0, "tax": 15.75, "deliveryFee": 30.0, "discount": 0.0, "paymentMethod": "Cash on Delivery", "deliveryAddress": {"id": "addr_1", "name": "<PERSON>", "phone": "+91 9876543210", "addressLine1": "123 Main Street", "addressLine2": "Near City Mall", "city": "Mumbai", "state": "Maharashtra", "pincode": "400001", "landmark": "Opposite Metro Station", "addressType": "Home"}, "items": [{"id": "item_4", "productId": "5", "name": "Fresh Apples", "imageUrl": "assets/products/apple.png", "price": 180.0, "quantity": 1, "unit": "kg", "discountedPrice": 180.0, "facilityId": "facility_1", "facilityName": "Fresh Mart Store", "skuID": "SKU005"}, {"id": "item_5", "productId": "30", "name": "Orange Juice", "imageUrl": "assets/products/orange-juice.png", "price": 100.0, "quantity": 1, "unit": "bottle", "discountedPrice": 100.0, "facilityId": "facility_1", "facilityName": "Fresh Mart Store", "skuID": "SKU030"}], "orderTimeline": [{"status": "placed", "timestamp": "2024-06-26T09:15:00Z", "title": "Order Placed", "description": "Your order has been placed successfully"}, {"status": "confirmed", "timestamp": "2024-06-26T09:20:00Z", "title": "Order Confirmed", "description": "Your order has been confirmed and is being prepared"}, {"status": "preparing", "timestamp": "2024-06-26T09:30:00Z", "title": "Preparing Order", "description": "Your order is being prepared at the store"}, {"status": "out_for_delivery", "timestamp": "2024-06-26T10:00:00Z", "title": "Out for Delivery", "description": "Your order is on the way to your location"}], "canCancel": true, "canReorder": true, "objectType": "order"}, {"id": "ORD-1703123456791", "customerId": "customer_123", "customerName": "<PERSON>", "status": "pending", "orderDate": "2024-06-27T11:20:00Z", "deliveryDate": null, "estimatedDeliveryTime": "45-60 mins", "totalAmount": 650.25, "subtotal": 580.0, "tax": 30.25, "deliveryFee": 40.0, "discount": 0.0, "paymentMethod": "Cash on Delivery", "deliveryAddress": {"id": "addr_2", "name": "<PERSON>", "phone": "+91 9876543210", "addressLine1": "456 Park Avenue", "addressLine2": "Sector 15", "city": "Mumbai", "state": "Maharashtra", "pincode": "400015", "landmark": "Near Shopping Complex", "addressType": "Office"}, "items": [{"id": "item_6", "productId": "10", "name": "Basmati Rice", "imageUrl": "assets/products/rice.png", "price": 250.0, "quantity": 2, "unit": "kg", "discountedPrice": 250.0, "facilityId": "facility_2", "facilityName": "Grocery Hub", "skuID": "SKU010"}, {"id": "item_7", "productId": "20", "name": "Chicken Breast", "imageUrl": "assets/products/chicken.png", "price": 330.0, "quantity": 1, "unit": "kg", "discountedPrice": 330.0, "facilityId": "facility_2", "facilityName": "Grocery Hub", "skuID": "SKU020"}], "orderTimeline": [{"status": "placed", "timestamp": "2024-06-27T11:20:00Z", "title": "Order Placed", "description": "Your order has been placed successfully"}], "canCancel": true, "canReorder": true, "objectType": "order"}, {"id": "ORD-1703123456792", "customerId": "customer_123", "customerName": "<PERSON>", "status": "cancelled", "orderDate": "2024-06-24T16:45:00Z", "deliveryDate": null, "estimatedDeliveryTime": null, "totalAmount": 275.0, "subtotal": 250.0, "tax": 12.5, "deliveryFee": 12.5, "discount": 0.0, "paymentMethod": "Cash on Delivery", "deliveryAddress": {"id": "addr_1", "name": "<PERSON>", "phone": "+91 9876543210", "addressLine1": "123 Main Street", "addressLine2": "Near City Mall", "city": "Mumbai", "state": "Maharashtra", "pincode": "400001", "landmark": "Opposite Metro Station", "addressType": "Home"}, "items": [{"id": "item_8", "productId": "35", "name": "Tomatoes", "imageUrl": "assets/products/tomato.png", "price": 80.0, "quantity": 2, "unit": "kg", "discountedPrice": 80.0, "facilityId": "facility_1", "facilityName": "Fresh Mart Store", "skuID": "SKU035"}, {"id": "item_9", "productId": "40", "name": "Onions", "imageUrl": "assets/products/onion.png", "price": 90.0, "quantity": 1, "unit": "kg", "discountedPrice": 90.0, "facilityId": "facility_1", "facilityName": "Fresh Mart Store", "skuID": "SKU040"}], "orderTimeline": [{"status": "placed", "timestamp": "2024-06-24T16:45:00Z", "title": "Order Placed", "description": "Your order has been placed successfully"}, {"status": "cancelled", "timestamp": "2024-06-24T17:00:00Z", "title": "Order Cancelled", "description": "Order was cancelled by customer"}], "canCancel": false, "canReorder": true, "objectType": "order"}]}