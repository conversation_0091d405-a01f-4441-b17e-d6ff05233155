export 'profile_event.dart';
export 'profile_state.dart';

import 'dart:convert';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/app/bloc/app_bloc.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/services/app_preferences_service.dart';
import 'package:rozana/features/location/services/adress_services.dart';
import 'package:rozana/core/services/user_profile_service.dart';

import 'profile_event.dart';
import 'profile_state.dart';

class ProfileBloc extends Bloc<ProfileEvent, ProfileState> {
  final UserProfileService _userProfileService;

  ProfileBloc({UserProfileService? userProfileService})
      : _userProfileService = userProfileService ?? UserProfileService(),
        super(const ProfileState.initial()) {
    on<ProfileEvent>((event, emit) async {
      await event.map(
        loadUserData: (e) => _mapLoadUserDataToState(emit),
        logout: (e) => _mapLogoutToState(emit),
        loadAddressCount: (e) => _mapLoadAddressCountToState(emit),
      );
    });
  }

  Future<void> _mapLoadUserDataToState(Emitter<ProfileState> emit) async {
    emit(const ProfileState.loading());
    try {
      int addressCount = await _getAddressCount();

      // Try to get profile from Firestore first
      final profile = await _userProfileService.getUserProfile();

      if (profile != null) {
        emit(ProfileState.loaded(
          userName: profile.displayName ?? 'No Name',
          userEmail: profile.email ?? 'No Email',
          userGender: 'Not specified', // We don't collect gender anymore
          addressCount: addressCount,
        ));
      } else {
        // Fallback to SharedPreferences (enhanced user data)
        String? userJson = AppPreferences.getUserdata();
        if (userJson != null && userJson.isNotEmpty) {
          Map<String, dynamic> userData = jsonDecode(userJson);

          // Check if profile is complete
          final isProfileComplete = userData['profileComplete'] ?? false;

          String userName;
          String userEmail;

          if (isProfileComplete) {
            // Profile is complete - show real name and email
            userName = userData['displayName'] ?? 'No Name';
            userEmail = userData['email'] ?? 'No Email';
          } else {
            // Profile incomplete - show phone number and prompt
            userName = userData['phoneNumber'] ?? 'User';
            userEmail = 'Complete your profile';
          }

          emit(ProfileState.loaded(
            userName: userName,
            userEmail: userEmail,
            userGender: 'Not specified',
            addressCount: addressCount,
          ));
        } else {
          emit(ProfileState.loaded(
            userName: 'Guest User',
            userEmail: 'Please login',
            userGender: 'Not specified',
            addressCount: addressCount,
          ));
        }
      }
    } catch (e) {
      emit(ProfileState.error(message: 'Failed to load user data: $e'));
    }
  }

  Future<void> _mapLogoutToState(Emitter<ProfileState> emit) async {
    emit(const ProfileState.loading());
    try {
      getIt<AppBloc>().add(AppEvent.logoutRequested());
    } catch (e) {
      emit(ProfileState.error(message: 'Failed to logout: $e'));
    }
  }

  Future<void> _mapLoadAddressCountToState(Emitter<ProfileState> emit) async {
    try {
      int addressCount = await _getAddressCount();
      // Keep previous user data if available
      final currentState = state;
      currentState.mapOrNull(
        loaded: (loadedState) {
          emit(ProfileState.loaded(
            userName: loadedState.userName,
            userEmail: loadedState.userEmail,
            userGender: loadedState.userGender,
            addressCount: addressCount,
          ));
        },
      );
    } catch (e) {
      emit(ProfileState.error(message: 'Failed to load address count: $e'));
    }
  }

  Future<int> _getAddressCount() async {
    final addresses = await AddressService().getAllAddresses();
    return addresses.length;
  }
}
