import 'package:flutter/material.dart';
// import 'package:lottie/lottie.dart'; // Uncomment if you add Lottie asset

class LottieAnimationPlaceholder extends StatelessWidget {
  const LottieAnimationPlaceholder({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 150, // Adjust height as needed for your Lottie animation
      color: Colors.red, // Background color matching the app bar
      alignment: Alignment.center,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // TODO: Replace with your Lottie animation widget
            // Lottie.asset(
            //   'assets/lottie_animation.json', // Your Lottie file path
            //   width: 200,
            //   height: 100,
            //   fit: BoxFit.contain,
            // ),
            const Icon(
              Icons.animation, // Placeholder icon
              size: 60,
              color: Colors.white,
            ),
            const SizedBox(height: 10),
            Text(
              'Lottie Animation Area',
              style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.8), fontSize: 16),
            ),
            Text(
              'Grab your favorite snacks!', // Placeholder text from image
              style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.8), fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }
}
