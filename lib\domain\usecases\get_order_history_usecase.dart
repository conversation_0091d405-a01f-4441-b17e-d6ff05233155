import '../entities/order_entity.dart';
import '../repositories/order_repository_interface.dart';

/// Use case for getting order history
/// Encapsulates the business logic for fetching customer orders
class GetOrderHistoryUseCase {
  final OrderRepositoryInterface _repository;

  GetOrderHistoryUseCase(this._repository);

  /// Execute the use case to get order history
  ///
  /// [status] - Filter by order status (empty for all orders)
  /// [page] - Page number for pagination (0-based)
  /// [pageSize] - Number of orders per page
  /// [refresh] - Whether to refresh the data
  ///
  /// Note: customerId is automatically retrieved from current authenticated user
  Future<List<OrderEntity>> call({
    String status = '',
    int page = 0,
    int pageSize = 10,
    bool refresh = false,
  }) async {
    try {
      return await _repository.getOrderHistory(
        status: status,
        page: page,
        pageSize: pageSize,
        refresh: refresh,
      );
    } catch (e) {
      // Log error and rethrow
      throw Exception('Failed to fetch order history: $e');
    }
  }

  /// Get orders by status with predefined filters
  Future<List<OrderEntity>> getOrdersByStatus({
    required String status,
    int page = 0,
    int pageSize = 10,
  }) async {
    return await call(
      status: status,
      page: page,
      pageSize: pageSize,
    );
  }

  /// Get active orders (pending, confirmed, preparing, out_for_delivery)
  Future<List<OrderEntity>> getActiveOrders({
    int page = 0,
    int pageSize = 10,
  }) async {
    // For active orders, we'll fetch all and filter in the use case
    // In a real API, this would be handled server-side
    final allOrders = await call(
      page: 0,
      pageSize: 100, // Get more orders to filter
    );

    final activeOrders =
        allOrders.where((order) => order.isInProgress).toList();

    // Apply pagination to filtered results
    final start = page * pageSize;
    final end = start + pageSize;

    if (start >= activeOrders.length) {
      return [];
    }

    return activeOrders.sublist(
        start, end > activeOrders.length ? activeOrders.length : end);
  }

  /// Get completed orders (delivered)
  Future<List<OrderEntity>> getCompletedOrders({
    int page = 0,
    int pageSize = 10,
  }) async {
    return await getOrdersByStatus(
      status: 'delivered',
      page: page,
      pageSize: pageSize,
    );
  }

  /// Get cancelled orders
  Future<List<OrderEntity>> getCancelledOrders({
    int page = 0,
    int pageSize = 10,
  }) async {
    return await getOrdersByStatus(
      status: 'cancelled',
      page: page,
      pageSize: pageSize,
    );
  }
}
