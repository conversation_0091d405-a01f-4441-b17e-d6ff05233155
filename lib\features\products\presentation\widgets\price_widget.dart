import 'package:flutter/material.dart';

import '../../../../core/themes/color_schemes.dart';

class PriceWidget extends StatelessWidget {
  final double price;
  final double? originalPrice;
  final double discountPercentage;
  final TextStyle? priceStyle;
  final TextStyle? originalPriceStyle;
  final TextStyle? discountStyle;
  final bool showDiscount;
  final MainAxisAlignment alignment;

  /// Widget to display product price with optional original price and discount
  const PriceWidget({
    super.key,
    required this.price,
    this.originalPrice,
    this.discountPercentage = 0,
    this.priceStyle,
    this.originalPriceStyle,
    this.discountStyle,
    this.showDiscount = false,
    this.alignment = MainAxisAlignment.start,
  });

  @override
  Widget build(BuildContext context) {
    final hasDiscount = originalPrice != null && originalPrice! > price;
    final calculatedDiscount = hasDiscount
        ? ((originalPrice! - price) / originalPrice! * 100).round()
        : discountPercentage.round();

    return Row(
      mainAxisAlignment: alignment,
      crossAxisAlignment: CrossAxisAlignment.baseline,
      textBaseline: TextBaseline.alphabetic,
      children: [
        Text(
          '₹${price.toStringAsFixed(price.truncateToDouble() == price ? 0 : 2)}',
          style: priceStyle ??
              const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
        ),
        if (hasDiscount) ...[
          const SizedBox(width: 8),
          Text(
            '₹${originalPrice!.toStringAsFixed(originalPrice!.truncateToDouble() == originalPrice ? 0 : 2)}',
            style: originalPriceStyle ??
                const TextStyle(
                  fontSize: 14,
                  decoration: TextDecoration.lineThrough,
                  color: AppColors.textSecondary,
                ),
          ),
          if (showDiscount && calculatedDiscount > 0) ...[
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
              decoration: BoxDecoration(
                color: AppColors.success.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                '$calculatedDiscount% OFF',
                style: discountStyle ??
                    const TextStyle(
                      fontSize: 8,
                      fontWeight: FontWeight.bold,
                      color: AppColors.success,
                    ),
              ),
            ),
          ],
        ],
      ],
    );
  }
}
