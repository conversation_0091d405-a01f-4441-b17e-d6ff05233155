import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:rozana/core/utils/logger.dart';

/// Service to handle SMS and phone permissions for autofill functionality
class SmsPermissionService {
  // Singleton instance
  static final SmsPermissionService _instance =
      SmsPermissionService._internal();
  factory SmsPermissionService() => _instance;
  SmsPermissionService._internal();

  /// Check if SMS permissions are granted
  Future<bool> checkSmsPermissions() async {
    if (kIsWeb) return false; // SMS autofill not available on web

    try {
      final smsStatus = await Permission.sms.status;
      final phoneStatus = await Permission.phone.status;

      LogMessage.p('SMS permission status: ${smsStatus.name}');
      LogMessage.p('Phone permission status: ${phoneStatus.name}');

      return smsStatus.isGranted && phoneStatus.isGranted;
    } catch (e) {
      LogMessage.p('Error checking SMS permissions: $e');
      return false;
    }
  }

  /// Request SMS permissions
  Future<bool> requestSmsPermissions() async {
    if (kIsWeb) return false; // SMS autofill not available on web

    try {
      // Request SMS permission
      final smsStatus = await Permission.sms.request();
      LogMessage.p('SMS permission request result: ${smsStatus.name}');

      // Request phone permission
      final phoneStatus = await Permission.phone.request();
      LogMessage.p('Phone permission request result: ${phoneStatus.name}');

      return smsStatus.isGranted && phoneStatus.isGranted;
    } catch (e) {
      LogMessage.p('Error requesting SMS permissions: $e');
      return false;
    }
  }

  /// Check if phone permission is granted (for phone number hint)
  Future<bool> checkPhonePermission() async {
    if (kIsWeb) return false;

    try {
      final status = await Permission.phone.status;
      LogMessage.p('Phone permission status: ${status.name}');
      return status.isGranted;
    } catch (e) {
      LogMessage.p('Error checking phone permission: $e');
      return false;
    }
  }

  /// Request phone permission (for phone number hint)
  Future<bool> requestPhonePermission() async {
    if (kIsWeb) return false;

    try {
      final status = await Permission.phone.request();
      LogMessage.p('Phone permission request result: ${status.name}');
      return status.isGranted;
    } catch (e) {
      LogMessage.p('Error requesting phone permission: $e');
      return false;
    }
  }

  /// Check if permissions are permanently denied
  Future<bool> arePermissionsPermanentlyDenied() async {
    if (kIsWeb) return false;

    try {
      final smsStatus = await Permission.sms.status;
      final phoneStatus = await Permission.phone.status;

      return smsStatus.isPermanentlyDenied || phoneStatus.isPermanentlyDenied;
    } catch (e) {
      LogMessage.p('Error checking permanently denied permissions: $e');
      return false;
    }
  }

  /// Open app settings if permissions are permanently denied
  Future<bool> openAppSettings() async {
    try {
      return await openAppSettings();
    } catch (e) {
      LogMessage.p('Error opening app settings: $e');
      return false;
    }
  }

  /// Get permission status description for user
  Future<String> getPermissionStatusDescription() async {
    if (kIsWeb) return 'SMS autofill not available on web';

    try {
      final smsStatus = await Permission.sms.status;
      final phoneStatus = await Permission.phone.status;

      if (smsStatus.isGranted && phoneStatus.isGranted) {
        return 'All permissions granted - SMS autofill available';
      } else if (smsStatus.isPermanentlyDenied ||
          phoneStatus.isPermanentlyDenied) {
        return 'Permissions permanently denied - Please enable in settings';
      } else if (smsStatus.isDenied || phoneStatus.isDenied) {
        return 'Permissions denied - SMS autofill not available';
      } else {
        return 'Permissions not determined - SMS autofill may not work';
      }
    } catch (e) {
      LogMessage.p('Error getting permission status description: $e');
      return 'Unable to check permissions';
    }
  }
}
