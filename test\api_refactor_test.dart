import 'package:flutter_test/flutter_test.dart';
import 'package:rozana/core/config/environment_config.dart';
import 'package:rozana/core/network/api_client.dart';

void main() {
  group('API Refactor Tests', () {
    test('EnvironmentConfig should provide Google Maps API URLs', () {
      // Test that all Google Maps API URLs are properly configured
      expect(
          EnvironmentConfig.googlePlacesNearbySearchUrl,
          equals(
              'https://maps.googleapis.com/maps/api/place/nearbysearch/json'));

      expect(EnvironmentConfig.googlePlacesDetailsUrl,
          equals('https://maps.googleapis.com/maps/api/place/details/json'));

      expect(EnvironmentConfig.googleGeocodingUrl,
          equals('https://maps.googleapis.com/maps/api/geocode/json'));

      expect(
          EnvironmentConfig.googlePlacesAutocompleteUrl,
          equals(
              'https://maps.googleapis.com/maps/api/place/autocomplete/json'));

      expect(EnvironmentConfig.googleMapsBaseUrl,
          equals(EnvironmentConfig.googlePlacesAutocompleteUrl));
    });

    test('EnvironmentConfig should provide Google Places API key', () {
      // Test that API key is available
      expect(EnvironmentConfig.googlePlacesApiKey, isNotEmpty);
    });

    test('ApiClient method signature supports external API parameters', () {
      // This test verifies that the ApiClient method signature supports external APIs
      // We test the method signature by checking it compiles without errors

      // Test that the method exists and accepts the new parameters
      const methodExists = ApiClient.sendHttpRequest;
      expect(methodExists, isNotNull);

      // Test that HttpMethod enum has the expected values
      expect(HttpMethod.get, isNotNull);
      expect(HttpMethod.post, isNotNull);
      expect(HttpMethod.put, isNotNull);
      expect(HttpMethod.patch, isNotNull);
      expect(HttpMethod.delete, isNotNull);
    });
  });
}
