import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:rozana/data/models/adress_model.dart';
import 'package:rozana/core/utils/logger.dart';
import 'package:rozana/core/themes/color_schemes.dart';

/// Service to handle address operations with Firestore
class FirestoreAddressService {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;

  // Collection names
  static const String _usersCollection = 'users';
  static const String _addressesSubcollection = 'addresses';

  FirestoreAddressService({
    FirebaseFirestore? firestore,
    FirebaseAuth? auth,
  })  : _firestore = firestore ?? FirebaseFirestore.instance,
        _auth = auth ?? FirebaseAuth.instance;

  /// Get current user ID
  String? get _currentUserId => _auth.currentUser?.uid;

  /// Check if user is authenticated
  bool get isUserAuthenticated => _currentUserId != null;

  /// Get user's addresses collection reference
  CollectionReference<Map<String, dynamic>>? get _userAddressesCollection {
    final userId = _currentUserId;
    if (userId == null) return null;

    return _firestore
        .collection(_usersCollection)
        .doc(userId)
        .collection(_addressesSubcollection);
  }

  /// Save address to Firestore
  Future<void> saveAddress(AddressModel address) async {
    if (!isUserAuthenticated) {
      throw Exception('User must be authenticated to save addresses');
    }

    final collection = _userAddressesCollection;
    if (collection == null) {
      throw Exception('Unable to access user addresses collection');
    }

    try {
      // Generate ID if not provided
      final addressId =
          address.id ?? DateTime.now().millisecondsSinceEpoch.toString();
      final addressWithId = address.copyWith(id: addressId);

      // If this is being set as default, update other addresses first
      if (addressWithId.isDefault == true) {
        await _updateOtherAddressesAsNonDefault(addressId);
      }

      // Save the address
      await collection.doc(addressId).set(addressWithId.toJson());

      LogMessage.p('Address saved successfully to Firestore: $addressId');
    } catch (e) {
      LogMessage.p('Error saving address to Firestore: $e',
          color: AppColors.error);
      rethrow;
    }
  }

  /// Get all addresses for the current user
  Future<List<AddressModel>> getAllAddresses() async {
    if (!isUserAuthenticated) {
      return []; // Return empty list for unauthenticated users
    }

    final collection = _userAddressesCollection;
    if (collection == null) {
      return [];
    }

    try {
      final querySnapshot = await collection.get();

      return querySnapshot.docs
          .map((doc) => AddressModel.fromJson({
                ...doc.data(),
                'id': doc.id, // Ensure ID is set from document ID
              }))
          .toList();
    } catch (e) {
      LogMessage.p('Error fetching addresses from Firestore: $e',
          color: AppColors.error);
      return [];
    }
  }

  /// Get default address for the current user
  Future<AddressModel?> getDefaultAddress() async {
    final addresses = await getAllAddresses();

    if (addresses.isEmpty) {
      return null;
    }

    // Find address marked as default
    try {
      return addresses.firstWhere((address) => address.isDefault == true);
    } catch (e) {
      // If no default found, return the first address
      return addresses.first;
    }
  }

  /// Delete address from Firestore
  Future<void> deleteAddress(String addressId) async {
    if (!isUserAuthenticated) {
      throw Exception('User must be authenticated to delete addresses');
    }

    final collection = _userAddressesCollection;
    if (collection == null) {
      throw Exception('Unable to access user addresses collection');
    }

    try {
      // Check if this is the default address
      final addressDoc = await collection.doc(addressId).get();
      final isDefault = addressDoc.data()?['isDefault'] == true;

      // Delete the address
      await collection.doc(addressId).delete();

      // If we deleted the default address, set another as default
      if (isDefault) {
        await _setFirstAddressAsDefault();
      }

      LogMessage.p('Address deleted successfully from Firestore: $addressId');
    } catch (e) {
      LogMessage.p('Error deleting address from Firestore: $e',
          color: AppColors.error);
      rethrow;
    }
  }

  /// Set address as default
  Future<void> setDefaultAddress(String addressId) async {
    if (!isUserAuthenticated) {
      throw Exception('User must be authenticated to set default address');
    }

    final collection = _userAddressesCollection;
    if (collection == null) {
      throw Exception('Unable to access user addresses collection');
    }

    try {
      // First, update all other addresses to not be default
      await _updateOtherAddressesAsNonDefault(addressId);

      // Then set this address as default
      await collection.doc(addressId).update({'isDefault': true});

      LogMessage.p('Default address updated successfully: $addressId');
    } catch (e) {
      LogMessage.p('Error setting default address: $e', color: AppColors.error);
      rethrow;
    }
  }

  /// Update all addresses except the specified one to not be default
  Future<void> _updateOtherAddressesAsNonDefault(
      String excludeAddressId) async {
    final collection = _userAddressesCollection;
    if (collection == null) return;

    try {
      final querySnapshot =
          await collection.where('isDefault', isEqualTo: true).get();

      final batch = _firestore.batch();

      for (final doc in querySnapshot.docs) {
        if (doc.id != excludeAddressId) {
          batch.update(doc.reference, {'isDefault': false});
        }
      }

      await batch.commit();
    } catch (e) {
      LogMessage.p('Error updating other addresses as non-default: $e',
          color: AppColors.error);
      // Don't rethrow here as this is a helper method
    }
  }

  /// Set the first available address as default (used after deleting default address)
  Future<void> _setFirstAddressAsDefault() async {
    final addresses = await getAllAddresses();

    if (addresses.isNotEmpty) {
      final firstAddress = addresses.first;
      if (firstAddress.id != null) {
        await setDefaultAddress(firstAddress.id!);
      }
    }
  }
}
