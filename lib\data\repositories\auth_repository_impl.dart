import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:rozana/core/utils/logger.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:rozana/core/services/app_verification_service.dart';
import 'package:rozana/domain/repositories/auth_repository_interface.dart';

class AuthRepositoryImpl implements IAuthRepository {
  final FirebaseAuth _firebaseAuth;
  final AppVerificationService _verificationService;

  // Constructor with dependency injection
  AuthRepositoryImpl({FirebaseAuth? firebaseAuth})
      : _firebaseAuth = firebaseAuth ?? FirebaseAuth.instance,
        _verificationService = AppVerificationService(
            auth: firebaseAuth ?? FirebaseAuth.instance) {
    // Configure Firebase Auth settings using the verification service
    _verificationService.configureFirebaseAuth();
  }

  // Mock method to simulate mobile number verification
  @override
  Future<void> verifyMobileNumber(
    String mobileNumber, {
    required Function(String, int?) codeSent,
    required Function(Exception) verificationFailed,
    required Function(UserCredential) verificationCompleted,
  }) async {
    try {
      final formattedNumber = mobileNumber.trim();
      await _firebaseAuth.verifyPhoneNumber(
        phoneNumber: formattedNumber,
        verificationCompleted: (PhoneAuthCredential credential) async {
          LogMessage.p('Auto-verification completed');
          try {
            UserCredential userCred =
                await _firebaseAuth.signInWithCredential(credential);
            verificationCompleted(userCred);
          } catch (e) {
            LogMessage.p('Error in auto-verification: $e');
            verificationFailed(Exception(e.toString()));
          }
        },
        verificationFailed: (FirebaseAuthException e) {
          // Use the verification service to get a readable error message
          String errorMessage = _verificationService.getReadableErrorMessage(e);
          LogMessage.p('Formatted error message: $errorMessage');
          verificationFailed(Exception(errorMessage));
        },
        codeSent: (String verificationId, int? resendToken) {
          LogMessage.p(
              'Code sent successfully, verificationId: $verificationId');
          codeSent(verificationId, resendToken);
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          LogMessage.p('Code auto retrieval timeout: $verificationId');
        },
        timeout: const Duration(seconds: 120),
      );
    } on FirebaseAuthException catch (e) {
      LogMessage.p('FirebaseAuthException: ${e.code} - ${e.message}');
      throw _verificationService.getReadableErrorMessage(e);
    } catch (e) {
      LogMessage.p('Unknown error during mobile number verification: $e');
      throw 'Something went wrong. Please try again.';
    }
  }

  @override
  Future<UserCredential> validateOTP(String verificationId, String otp) async {
    try {
      // Create credential
      PhoneAuthCredential credential = PhoneAuthProvider.credential(
        verificationId: verificationId,
        smsCode: otp,
      );

      // Sign in
      UserCredential userCredential =
          await _firebaseAuth.signInWithCredential(credential);

      if (userCredential.user != null) {
        return userCredential;
      }

      throw FirebaseAuthException(
        code: 'null-user',
        message: 'Failed to verify OTP, no user returned.',
      );
    } on FirebaseAuthException catch (e) {
      LogMessage.p('FirebaseAuthException: ${e.code} - ${e.message}');
      throw e.message ?? 'Something went wrong while verifying the OTP.';
    } catch (e) {
      LogMessage.p('Unknown error during OTP verification: $e');
      throw 'Something went wrong. Please try again.';
    }
  }

  @override
  Future<UserCredential> googleSignin() async {
    final GoogleSignIn googleSignIn = _initGoogleSignIn();
    try {
      // Start the Google sign-in process
      final GoogleSignInAccount? googleUser = await googleSignIn.signIn();

      // User canceled the sign-in flow
      if (googleUser == null) {
        throw 'Google sign in was canceled';
      }
      // Get authentication details
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      // Create credential for Firebase
      final OAuthCredential credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Sign in with Firebase
      final UserCredential userCredential =
          await _firebaseAuth.signInWithCredential(credential);

      if (userCredential.user != null) {
        return userCredential;
      }

      throw 'Failed to authenticate with Google';
    } catch (e) {
      // Handle specific authentication errors
      await googleSignIn.signOut(); // Sign out to clear any partial state
      throw 'Something went wrong. Please try again.';
    }
  }

  static GoogleSignIn _initGoogleSignIn() {
    if (kIsWeb) {
      // Web-specific configuration
      return GoogleSignIn(
        clientId:
            "184880023304-9k0lqv7bq9ld2ckbkdjkbvqk7vgn9qe0.apps.googleusercontent.com",
        scopes: ['email', 'profile'],
      );
    } else {
      // Mobile configuration (Android/iOS)
      return GoogleSignIn(
        scopes: ['email', 'profile'],
      );
    }
  }
}
