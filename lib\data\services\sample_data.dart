import 'dart:convert';
import 'package:flutter/services.dart';

class SampleDataService {
  Future<List<Map<String, dynamic>>> getCategories() async {
    final String jsonData =
        await rootBundle.loadString('assets/data/data.json');
    final data = jsonDecode(jsonData);
    return List<Map<String, dynamic>>.from(data['categories']);
  }

  Future<List<Map<String, dynamic>>> getSubCategories(
      {String categoryID = ''}) async {
    final String jsonData =
        await rootBundle.loadString('assets/data/data.json');
    final data = jsonDecode(jsonData);
    if (categoryID.isEmpty) {
      return List<Map<String, dynamic>>.from(data['subCategories'])
          .take(20)
          .toList();
    }
    return List<Map<String, dynamic>>.from(data['subCategories']
        .where((subCategory) => subCategory['categoryID'] == categoryID)
        .toList());
  }

  Future<List<Map<String, dynamic>>> getProducts(
      {String subCategoryID = ''}) async {
    final String jsonData =
        await rootBundle.loadString('assets/data/data.json');
    final data = jsonDecode(jsonData);

    if (subCategoryID.isEmpty) {
      final allProducts = List<Map<String, dynamic>>.from(data['products']);
      return allProducts.take(50).toList();
    }

    return List<Map<String, dynamic>>.from(data['products']
        .where((product) => product['subCategoryID'] == subCategoryID)
        .toList());
  }

  Future<List<Map<String, dynamic>>> getFeaturedProducts() async {
    final String jsonData =
        await rootBundle.loadString('assets/data/data.json');

    final data = jsonDecode(jsonData);
    return List<Map<String, dynamic>>.from(data['featuredProducts']);
  }

  Future<List<Map<String, dynamic>>> getBanners() async {
    final String jsonData =
        await rootBundle.loadString('assets/data/data.json');
    final data = jsonDecode(jsonData);
    return List<Map<String, dynamic>>.from(data['banners']);
  }

  /// Get orders for a specific customer
  Future<List<Map<String, dynamic>>> getOrders({
    String customerId = 'customer_123',
    String status = '',
  }) async {
    final String jsonData =
        await rootBundle.loadString('assets/data/data.json');
    final data = jsonDecode(jsonData);

    List<Map<String, dynamic>> orders =
        List<Map<String, dynamic>>.from(data['orders']);

    // Filter by customer ID
    orders =
        orders.where((order) => order['customerId'] == customerId).toList();

    // Filter by status if provided
    if (status.isNotEmpty && status != 'all') {
      orders = orders.where((order) => order['status'] == status).toList();
    }

    // Sort by order date (newest first)
    orders.sort((a, b) {
      final dateA = DateTime.tryParse(a['orderDate'] ?? '') ?? DateTime.now();
      final dateB = DateTime.tryParse(b['orderDate'] ?? '') ?? DateTime.now();
      return dateB.compareTo(dateA);
    });

    return orders;
  }

  /// Get a specific order by ID
  Future<Map<String, dynamic>?> getOrderById(String orderId) async {
    final String jsonData =
        await rootBundle.loadString('assets/data/data.json');
    final data = jsonDecode(jsonData);

    List<Map<String, dynamic>> orders =
        List<Map<String, dynamic>>.from(data['orders']);

    try {
      return orders.firstWhere((order) => order['id'] == orderId);
    } catch (e) {
      return null;
    }
  }
}
