class HomeBanner {
  final String id;
  final String imageUrl;
  final String? categoryId; // To link to a product category on tap
  final String? categoryName; // To link to a product category on tap

  HomeBanner({
    required this.id,
    required this.imageUrl,
    this.categoryId,
    this.categoryName,
  });

  factory HomeBanner.fromJson(Map<String, dynamic> json) {
    return HomeBanner(
      id: json['id'] as String? ?? '',
      imageUrl: json['imageUrl'] as String? ?? '',
      categoryId: json['categoryId'] as String?,
      categoryName: json['categoryName'] as String?,
    );
  }
}
