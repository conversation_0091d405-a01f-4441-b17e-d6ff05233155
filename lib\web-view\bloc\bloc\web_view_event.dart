part of 'web_view_bloc.dart';

@freezed
class WebViewEvent with _$WebViewEvent {
  const factory WebViewEvent.started() = _Started;
  const factory WebViewEvent.contentLoaded() = _ContentLoaded;
  const factory WebViewEvent.contentFailed(String error) = _ContentFailed;
  const factory WebViewEvent.switchToWebView() = _SwitchToWebView;
  const factory WebViewEvent.switchToNativeView() = _SwitchToNativeView;
  const factory WebViewEvent.checkScreenWidth(double width) = _CheckScreenWidth;
}
