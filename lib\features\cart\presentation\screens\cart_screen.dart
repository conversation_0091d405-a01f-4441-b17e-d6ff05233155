import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/app/bloc/app_bloc.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/services/app_preferences_service.dart';
import 'package:rozana/data/models/cart_item_model.dart';
import 'package:rozana/data/models/adress_model.dart';
import 'package:rozana/domain/repositories/order_repository_interface.dart';
import 'package:rozana/features/location/services/adress_services.dart';
import 'package:rozana/features/cart/bloc/cart_bloc.dart';
import 'package:rozana/features/cart/bloc/cart_event.dart';
import 'package:rozana/features/cart/bloc/cart_state.dart';
import 'package:rozana/routes/app_router.dart';

import '../../../../core/themes/color_schemes.dart';
import '../../../../core/extensions/localization_extension.dart';
import '../../../../widgets/custom_button.dart';
import '../widgets/cart_item_card.dart';
import '../widgets/cart_summary_card.dart';
import '../widgets/empty_cart.dart';
import '../widgets/payment_method_selector.dart';

class CartScreen extends StatefulWidget {
  const CartScreen({super.key});

  @override
  State<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends State<CartScreen> {
  final CartBloc _cartBloc = getIt<CartBloc>();
  final TextEditingController _couponController = TextEditingController();
  bool _isProcessingOrder = false;

  // Checkout data with default address
  final Map<String, dynamic> _checkoutData = {
    'paymentMethod': 'cash', // ID for Cash on Delivery
    'address': {
      'fullName': 'John Doe',
      'phoneNumber': '9876543210',
      'addressLine1': '123 Main Street',
      'addressLine2': 'Apartment 4B',
      'city': 'Mumbai',
      'state': 'Maharashtra',
      'pincode': '400001',
      'addressType': 'Home',
    },
    'deliverySlot': 'standard',
  };

  @override
  void initState() {
    super.initState();
    _cartBloc.add(CartEvent.init());

    // Load default address if user is authenticated
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final appBloc = getIt<AppBloc>();
      if (appBloc.isAuthenticated) {
        _cartBloc.add(CartEvent.loadDefaultAddress());
      }
    });
  }

  @override
  void dispose() {
    _couponController.dispose();
    super.dispose();
  }

  void _showClearCartConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Cart'),
        content: const Text(
            'Are you sure you want to remove all items from your cart?'),
        actions: [
          TextButton(
            onPressed: () => context.pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              _cartBloc.add(CartEvent.clear());
              context.pop();
            },
            child:
                const Text('Clear', style: TextStyle(color: AppColors.error)),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Use ChangeNotifierProvider.value at the root level but with a more stable structure
    return BlocListener<AppBloc, AppState>(
      listener: (context, state) {
        state.maybeMap(
          loaded: (loaded) {
            if (loaded.isAuthenticated) {
              // Load default address when user logs in
              _cartBloc.add(CartEvent.loadDefaultAddress());
            } else {
              // Clear address when user logs out
              _cartBloc.add(CartEvent.clearAddress());
            }
          },
          orElse: () {},
        );
      },
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          backgroundColor: AppColors.surface,
          elevation: 0.5,
          title: Text(
            context.l10n.myCart,
            style: const TextStyle(
              color: AppColors.textPrimary,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: AppColors.textPrimary),
            onPressed: () => context.pop(),
          ),
          actions: [
            BlocBuilder<CartBloc, CartState>(
              builder: (context, state) {
                if (state.cart.items?.isEmpty ?? false) return const SizedBox();

                return IconButton(
                  icon:
                      const Icon(Icons.delete_outline, color: AppColors.error),
                  onPressed: () {
                    HapticFeedback.mediumImpact();
                    _showClearCartConfirmation(context);
                  },
                );
              },
            ),
          ],
        ),
        body: BlocBuilder<CartBloc, CartState>(
          buildWhen: (previous, current) =>
              previous.isLoading != current.isLoading,
          builder: (context, state) {
            if (state.isLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            return BlocBuilder<CartBloc, CartState>(
              buildWhen: (previous, current) =>
                  previous.cart.items != current.cart.items,
              builder: (context, state) {
                if (state.cart.items?.isEmpty ?? true) {
                  return const EmptyCart();
                }

                return _buildCartContentOptimized(context);
              },
            );
          },
        ),
        bottomNavigationBar: BlocBuilder<CartBloc, CartState>(
          buildWhen: (previous, current) =>
              previous.cart.items != current.cart.items ||
              previous.cart.total != current.cart.total ||
              previous.cart.totalItems != current.cart.totalItems,
          builder: (context, state) {
            if (state.cart.items?.isEmpty ?? true) {
              return const SizedBox();
            }

            return _buildCheckoutBar(context);
          },
        ),
      ),
    );
  }

  Widget _buildCartContentOptimized(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Cart items without separate scrolling
          BlocBuilder<CartBloc, CartState>(
            buildWhen: (previous, current) =>
                previous.cart.items != current.cart.items,
            builder: (context, state) {
              return Column(
                children: state.cart.items
                        ?.map((item) => _buildCartItemWithSelector(item))
                        .toList() ??
                    [],
              );
            },
          ),

          // const SizedBox(height: 16),

          // Coupon section
          // _buildCouponSection(),

          const SizedBox(height: 16),

          // Cart summary with Selector to update when cart totals change
          BlocBuilder<CartBloc, CartState>(
            buildWhen: (previous, current) => previous.cart != current.cart,
            builder: (context, state) => CartSummaryCard(cart: state.cart),
          ),

          const SizedBox(height: 24),

          // Delivery Address Section - Only show for authenticated users
          BlocBuilder<AppBloc, AppState>(
            builder: (context, appState) {
              return appState.maybeMap(
                loaded: (loaded) {
                  if (loaded.isAuthenticated) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Delivery Address',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 12),
                        _buildAddressCard(),
                        const SizedBox(height: 24),
                      ],
                    );
                  } else {
                    return const SizedBox(); // Hide address section for unauthenticated users
                  }
                },
                orElse: () => const SizedBox(),
              );
            },
          ),

          // Payment Method Section
          const Text(
            'Payment Method',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          PaymentMethodSelector(
            selectedMethod: _checkoutData['paymentMethod'],
            onMethodSelected: (method) {
              setState(() {
                _checkoutData['paymentMethod'] = method;
              });
            },
          ),

          const SizedBox(height: 100), // Space for bottom bar
        ],
      ),
    );
  }

  Widget _buildCartItemWithSelector(CartItemModel item) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: CartItemCard(
        key: ValueKey(item.id), // Add key for better Flutter diffing
        item: item,
        onUpdateQuantity: (quantity) async {
          HapticFeedback.lightImpact();
          // Use async/await to ensure operation completes
          _cartBloc.add(CartEvent.updateQuantity(item.id ?? '', quantity));
        },
        onRemove: () async {
          HapticFeedback.mediumImpact();
          // Use async/await to ensure operation completes
          _cartBloc.add(CartEvent.removeItem(item.id ?? ''));
        },
      ),
    );
  }

  Widget _buildCheckoutBar(BuildContext context) {
    return BlocBuilder<AppBloc, AppState>(
      builder: (context, appState) {
        return BlocBuilder<CartBloc, CartState>(
          builder: (context, cartState) {
            final isAuthenticated = appState.maybeMap(
              loaded: (loaded) => loaded.isAuthenticated,
              orElse: () => false,
            );

            // Determine button text and action based on authentication and address status
            String buttonText;
            VoidCallback? onPressed;

            if (_isProcessingOrder) {
              buttonText = 'Processing...';
              onPressed = null;
            } else if (!isAuthenticated) {
              buttonText = context.l10n.loginToProceed;
              onPressed = () => _placeOrder(cartState);
            } else if (cartState.deliveryAddress == null) {
              buttonText = context.l10n.selectAddress;
              onPressed = _navigateToAddressSelection;
            } else {
              buttonText = context.l10n.placeOrder;
              onPressed = () => _placeOrder(cartState);
            }

            return Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.surface,
                boxShadow: [
                  BoxShadow(
                    color: AppColors.shadowGrey,
                    blurRadius: 10,
                    offset: const Offset(0, -5),
                  ),
                ],
              ),
              child: SafeArea(
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            '₹${(cartState.cart.total ?? 0).toStringAsFixed(2)}',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textPrimary,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            context.l10n.totalItems(cartState.cart.totalItems),
                            style: TextStyle(
                              fontSize: 12,
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 48,
                      child: AppButton(
                        text: buttonText,
                        onPressed: onPressed,
                        backgroundColor: AppColors.primary,
                        textColor: AppColors.white,
                        borderRadius: 8,
                        width: 180,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildAddressCard() {
    // This method is only called for authenticated users now
    return BlocBuilder<CartBloc, CartState>(
      bloc: _cartBloc,
      buildWhen: (previous, current) =>
          previous.deliveryAddress != current.deliveryAddress ||
          previous.isLoadingAddress != current.isLoadingAddress,
      builder: (context, cartState) {
        if (cartState.isLoadingAddress) {
          return _buildLoadingAddressCard();
        }

        if (cartState.deliveryAddress != null) {
          return _buildSelectedAddressCard(cartState.deliveryAddress!);
        } else {
          // No address card needed - user will use "Select Address" button in checkout bar
          return const SizedBox();
        }
      },
    );
  }

  Widget _buildLoadingAddressCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.textHint),
      ),
      child: const Row(
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
          SizedBox(width: 12),
          Text('Loading address...'),
        ],
      ),
    );
  }

  Widget _buildSelectedAddressCard(AddressModel address) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.textHint),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.location_on,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  address.addressType?.toUpperCase() ?? 'ADDRESS',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              TextButton(
                onPressed: _navigateToAddressSelection,
                style: TextButton.styleFrom(
                  foregroundColor: AppColors.primary,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                child: const Text('Change'),
              ),
            ],
          ),
          const SizedBox(height: 8),
          if (address.fullAddress != null) ...[
            Text(
              address.fullAddress!,
              style: TextStyle(
                fontSize: 14,
                color: AppColors.textSecondary,
              ),
            ),
          ] else ...[
            Text(
              '${address.addressLine1 ?? ''}, ${address.addressLine2 ?? ''}',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              '${address.city ?? ''}, ${address.state ?? ''} - ${address.pincode ?? ''}',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.textSecondary,
              ),
            ),
          ],
          if (address.phone != null) ...[
            const SizedBox(height: 4),
            Text(
              'Phone: ${address.phone}',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Future<void> _navigateToAddressSelection() async {
    // Check if user has any existing addresses
    final addressService = AddressService();
    final addresses = await addressService.getAllAddresses();

    if (!mounted) return;

    if (addresses.isEmpty) {
      // No addresses exist, go directly to add address form
      final newAddress = await context.push<AddressModel>(
        RouteNames.mapForNewAddress,
        extra: {'fromCart': true},
      );

      // After returning from address form, select the new address
      if (mounted && newAddress != null) {
        _cartBloc.add(CartEvent.selectAddress(newAddress));
      }
    } else {
      final selectedAddress = await context.push<AddressModel>(
        RouteNames.addresses,
        extra: {
          'selectMode': true,
          'onAddressSelected': (AddressModel address) {
            context.pop(address);
          },
        },
      );

      if (mounted && selectedAddress != null) {
        _cartBloc.add(CartEvent.selectAddress(selectedAddress));
      }
    }
  }

  Future<void> _placeOrder(CartState state) async {
    if (state.cart.items?.isEmpty ?? false) return;

    // Check if user is authenticated before proceeding with checkout
    final appBloc = getIt<AppBloc>();
    if (!appBloc.isAuthenticated) {
      // Navigate to login screen with return route information
      // Store the return route in extra data to be used after login
      context.push(RouteNames.login, extra: {'returnRoute': RouteNames.cart});
      return;
    }

    setState(() {
      _isProcessingOrder = true;
    });

    try {
      // Get the order repository from dependency injection
      final orderRepository = getIt<OrderRepositoryInterface>();

      // Get user data from preferences
      final userDataJson = AppPreferences.getUserdata();
      Map<String, dynamic> userData = {};
      String customerId = 'CUST-001'; // Default fallback
      String customerName = 'nithin'; // Default name as per requirement

      if (userDataJson != null && userDataJson.isNotEmpty) {
        userData = jsonDecode(userDataJson);
        // Use Firebase UID as customer ID if available
        customerId = userData['uid'] ?? customerId;
      }

      // Get facility information from the first item in cart
      final firstItem = state.cart.items?.first;
      final facilityId = firstItem?.facilityId ?? 'FAC-001';
      final facilityName = firstItem?.facilityName ?? 'abc';

      // Format items as per API requirements
      final formattedItems = state.cart.items
              ?.map((item) => {
                    'sku': item.skuID ?? 'ITEM-${item.id}',
                    'quantity': item.quantity ?? 0,
                    'unit_price': item.price ?? 0,
                    // Make sure sale_price is never 0 - use the same as unit_price if no discount
                    'sale_price': (item.discountedPrice != null &&
                            item.discountedPrice! > 0)
                        ? item.discountedPrice!
                        : item.price ?? 0
                  })
              .toList() ??
          [];

      // Use the cart model's total for consistency across screens
      num totalAmount = state.cart.total ?? 0;

      // Ensure total amount is never zero for non-empty carts
      if (totalAmount <= 0 && state.cart.items?.isNotEmpty == true) {
        // Calculate a fallback total if the cart model's total is somehow zero
        state.cart.items?.forEach((item) {
          num itemPrice = (item.price ?? 0);
          num itemTotal = itemPrice * (item.quantity ?? 1);
          totalAmount += itemTotal;
        });

        // Add delivery fee if applicable
        if (totalAmount > 0) {
          if (totalAmount < 200) {
            totalAmount += 60.0; // Base fee + small order fee
          } else if (totalAmount < 500) {
            totalAmount += 40.0; // Base delivery fee
          }
          // Add 5% tax
          totalAmount += (totalAmount * 0.05);
        }
      }

      // Create order using the repository
      final response = await orderRepository.createOrder(
        customerId: customerId,
        customerName: customerName,
        facilityId: facilityId,
        facilityName: facilityName,
        totalAmount: totalAmount,
        items: formattedItems,
      );

      if (response?.statusCode == 200 || response?.statusCode == 201) {
        // Generate order ID from response or generate locally
        final orderId = response?.data['order_id'] ??
            'ORD-${DateTime.now().millisecondsSinceEpoch}';

        // Create order data for success screen
        final orderData = {
          'items': state.cart.items,
          'address': _checkoutData['address'],
          'paymentMethod': _checkoutData['paymentMethod'],
          'total': totalAmount, // Use our correctly calculated total
          'subtotal': state.cart.subTotal,
          'tax': state.cart.tax, // Add tax value
          'discount': state.cart.discount,
          'deliveryFee': state.cart.deliveryFee,
          'itemCount': state.cart.totalItems,
          'orderDate': DateTime.now().toIso8601String(),
        };

        // Clear cart after successful order
        _cartBloc.add(CartEvent.clear());

        // Navigate to order success screen
        if (mounted) {
          context.pushReplacement(
            RouteNames.orderSuccess,
            extra: {
              'orderId': orderId,
              'orderData': orderData,
            },
          );
        }
      } else {
        throw Exception('Failed to create order: ${response?.statusMessage}');
      }
    } catch (e) {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${context.l10n.error}: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessingOrder = false;
        });
      }
    }
  }
}
