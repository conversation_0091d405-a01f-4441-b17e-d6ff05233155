import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../data/models/adress_model.dart';

part 'location_state.freezed.dart';

@freezed
class LocationState with _$LocationState {
  const factory LocationState.initial() = _Initial;
  const factory LocationState.loading() = _Loading;
  const factory LocationState.loaded(AddressModel address) = _Loaded;
  const factory LocationState.error(String message) = _Error;
}
