import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:rozana/core/dependency_injection/di_setup.dart';
import 'package:rozana/core/services/bloc_cleanup_service.dart';
import 'package:rozana/features/cart/bloc/cart_bloc.dart';
import 'package:rozana/features/cart/bloc/cart_state.dart';
import 'package:rozana/features/location/bloc/location%20bloc/location_bloc.dart';

void main() {
  group('BLoC Memory Management Tests', () {
    late GetIt getIt;

    setUpAll(() {
      // Initialize Flutter bindings for testing
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    setUp(() {
      // Reset GetIt before each test
      GetIt.instance.reset();
      getIt = GetIt.instance;

      // Setup dependency injection
      setupDI();
    });

    tearDown(() {
      // Clean up after each test
      GetIt.instance.reset();
    });

    test('CartBloc should have proper dependency injection', () {
      // Arrange & Act
      final cartBloc = getIt<CartBloc>();

      // Assert
      expect(cartBloc, isNotNull);
      expect(cartBloc.state, isA<CartState>());
    });

    test('CartBloc should have reset method', () {
      // Arrange
      final cartBloc = getIt<CartBloc>();

      // Act & Assert - Verify reset method exists and can be called
      expect(() => cartBloc.resetCart(), returnsNormally);
    });

    test('LocationBloc should reset properly', () async {
      // Arrange
      final locationBloc = getIt<LocationBloc>();

      // Act - Reset location
      locationBloc.resetLocation();
      await Future.delayed(const Duration(milliseconds: 100));

      // Assert
      expect(locationBloc.state, isA<LocationState>());
    });

    test('BlocCleanupService should reset user-specific BLoCs', () {
      // Act & Assert - Verify cleanup service can be called without errors
      expect(
          () => BlocCleanupService.resetUserSpecificBlocs(), returnsNormally);
    });

    test('BLoCs should be properly initialized', () {
      // Act
      final areInitialized = BlocCleanupService.areBloCsInitialized();

      // Assert
      expect(areInitialized, isTrue);
    });

    test('BLoCs should close properly without memory leaks', () async {
      // Arrange
      final cartBloc = getIt<CartBloc>();
      final locationBloc = getIt<LocationBloc>();

      // Act - Close BLoCs
      await cartBloc.close();
      await locationBloc.close();

      // Assert
      expect(cartBloc.isClosed, isTrue);
      expect(locationBloc.isClosed, isTrue);
    });
  });
}
