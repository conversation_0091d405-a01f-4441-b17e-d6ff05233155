// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'location_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$LocationEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function() requestPermissionAndDetect,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function()? requestPermissionAndDetect,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function()? requestPermissionAndDetect,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_RequestPermissionAndDetect value)
        requestPermissionAndDetect,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_RequestPermissionAndDetect value)?
        requestPermissionAndDetect,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_RequestPermissionAndDetect value)?
        requestPermissionAndDetect,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocationEventCopyWith<$Res> {
  factory $LocationEventCopyWith(
          LocationEvent value, $Res Function(LocationEvent) then) =
      _$LocationEventCopyWithImpl<$Res, LocationEvent>;
}

/// @nodoc
class _$LocationEventCopyWithImpl<$Res, $Val extends LocationEvent>
    implements $LocationEventCopyWith<$Res> {
  _$LocationEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$StartedImplCopyWith<$Res> {
  factory _$$StartedImplCopyWith(
          _$StartedImpl value, $Res Function(_$StartedImpl) then) =
      __$$StartedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$StartedImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$StartedImpl>
    implements _$$StartedImplCopyWith<$Res> {
  __$$StartedImplCopyWithImpl(
      _$StartedImpl _value, $Res Function(_$StartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$StartedImpl implements _Started {
  const _$StartedImpl();

  @override
  String toString() {
    return 'LocationEvent.started()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$StartedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function() requestPermissionAndDetect,
  }) {
    return started();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function()? requestPermissionAndDetect,
  }) {
    return started?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function()? requestPermissionAndDetect,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_RequestPermissionAndDetect value)
        requestPermissionAndDetect,
  }) {
    return started(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_RequestPermissionAndDetect value)?
        requestPermissionAndDetect,
  }) {
    return started?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_RequestPermissionAndDetect value)?
        requestPermissionAndDetect,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(this);
    }
    return orElse();
  }
}

abstract class _Started implements LocationEvent {
  const factory _Started() = _$StartedImpl;
}

/// @nodoc
abstract class _$$RefreshLocationImplCopyWith<$Res> {
  factory _$$RefreshLocationImplCopyWith(_$RefreshLocationImpl value,
          $Res Function(_$RefreshLocationImpl) then) =
      __$$RefreshLocationImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RefreshLocationImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$RefreshLocationImpl>
    implements _$$RefreshLocationImplCopyWith<$Res> {
  __$$RefreshLocationImplCopyWithImpl(
      _$RefreshLocationImpl _value, $Res Function(_$RefreshLocationImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$RefreshLocationImpl implements _RefreshLocation {
  const _$RefreshLocationImpl();

  @override
  String toString() {
    return 'LocationEvent.refreshLocation()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$RefreshLocationImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function() requestPermissionAndDetect,
  }) {
    return refreshLocation();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function()? requestPermissionAndDetect,
  }) {
    return refreshLocation?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function()? requestPermissionAndDetect,
    required TResult orElse(),
  }) {
    if (refreshLocation != null) {
      return refreshLocation();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_RequestPermissionAndDetect value)
        requestPermissionAndDetect,
  }) {
    return refreshLocation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_RequestPermissionAndDetect value)?
        requestPermissionAndDetect,
  }) {
    return refreshLocation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_RequestPermissionAndDetect value)?
        requestPermissionAndDetect,
    required TResult orElse(),
  }) {
    if (refreshLocation != null) {
      return refreshLocation(this);
    }
    return orElse();
  }
}

abstract class _RefreshLocation implements LocationEvent {
  const factory _RefreshLocation() = _$RefreshLocationImpl;
}

/// @nodoc
abstract class _$$RequestPermissionAndDetectImplCopyWith<$Res> {
  factory _$$RequestPermissionAndDetectImplCopyWith(
          _$RequestPermissionAndDetectImpl value,
          $Res Function(_$RequestPermissionAndDetectImpl) then) =
      __$$RequestPermissionAndDetectImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RequestPermissionAndDetectImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$RequestPermissionAndDetectImpl>
    implements _$$RequestPermissionAndDetectImplCopyWith<$Res> {
  __$$RequestPermissionAndDetectImplCopyWithImpl(
      _$RequestPermissionAndDetectImpl _value,
      $Res Function(_$RequestPermissionAndDetectImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$RequestPermissionAndDetectImpl implements _RequestPermissionAndDetect {
  const _$RequestPermissionAndDetectImpl();

  @override
  String toString() {
    return 'LocationEvent.requestPermissionAndDetect()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RequestPermissionAndDetectImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function() requestPermissionAndDetect,
  }) {
    return requestPermissionAndDetect();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function()? requestPermissionAndDetect,
  }) {
    return requestPermissionAndDetect?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function()? requestPermissionAndDetect,
    required TResult orElse(),
  }) {
    if (requestPermissionAndDetect != null) {
      return requestPermissionAndDetect();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_RequestPermissionAndDetect value)
        requestPermissionAndDetect,
  }) {
    return requestPermissionAndDetect(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_RequestPermissionAndDetect value)?
        requestPermissionAndDetect,
  }) {
    return requestPermissionAndDetect?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_RequestPermissionAndDetect value)?
        requestPermissionAndDetect,
    required TResult orElse(),
  }) {
    if (requestPermissionAndDetect != null) {
      return requestPermissionAndDetect(this);
    }
    return orElse();
  }
}

abstract class _RequestPermissionAndDetect implements LocationEvent {
  const factory _RequestPermissionAndDetect() =
      _$RequestPermissionAndDetectImpl;
}
