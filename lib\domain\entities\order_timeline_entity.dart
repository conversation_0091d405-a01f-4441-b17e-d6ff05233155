/// Domain entity representing an order timeline entry
/// Pure business object without external dependencies
class OrderTimelineEntity {
  final String status;
  final DateTime timestamp;
  final String title;
  final String description;

  const OrderTimelineEntity({
    required this.status,
    required this.timestamp,
    required this.title,
    required this.description,
  });

  /// Get formatted timestamp
  String get formattedTime {
    final hour = timestamp.hour.toString().padLeft(2, '0');
    final minute = timestamp.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  /// Get formatted date
  String get formattedDate {
    return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
  }

  /// Get formatted date and time
  String get formattedDateTime {
    return '$formattedDate at $formattedTime';
  }

  /// Check if this timeline entry is completed
  bool get isCompleted =>
      true; // All timeline entries in the list are completed

  /// Get status icon based on status
  String get statusIcon {
    switch (status) {
      case 'placed':
        return '📝';
      case 'confirmed':
        return '✅';
      case 'preparing':
        return '👨‍🍳';
      case 'out_for_delivery':
        return '🚚';
      case 'delivered':
        return '📦';
      case 'cancelled':
        return '❌';
      default:
        return '📋';
    }
  }
}
