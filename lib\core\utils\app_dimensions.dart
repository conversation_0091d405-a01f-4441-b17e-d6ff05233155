import 'package:flutter/material.dart';

class AppDimensions {
  static const double screenHzPadding = 20;
  static const double screenTopPadding = 20;
  static const double screenBottomPadding = 30;
  static const EdgeInsets screenPadding = EdgeInsets.fromLTRB(
      screenHzPadding, screenTopPadding, screenHzPadding, screenBottomPadding);
  static double getAvailableScreenHeight(BuildContext context) =>
      (MediaQuery.of(context).size.height) -
      ((MediaQuery.of(context).viewPadding.top) +
          AppDimensions.screenTopPadding +
          AppDimensions.screenBottomPadding);
}
