export 'login_event.dart';
export 'login_state.dart';

import 'dart:async';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/core/utils/logger.dart';
import 'package:sms_autofill/sms_autofill.dart';

import '../../../../app/bloc/app_bloc.dart';
import '../../../../core/utils/app_validators.dart';
import '../../../../core/utils/notifier.dart';
import '../../../../core/utils/text_field_manager.dart';
import '../../../../domain/repositories/auth_repository_interface.dart';

import 'login_event.dart';
import 'login_state.dart';

class LoginBloc extends Bloc<LoginEvent, LoginState> {
  final IAuthRepository authRepository;
  final AppNotifier notifier;
  final AppBloc appBloc;

  static TextFieldManager? mobileNumberManager;
  static TextFieldManager? otpManager;
  int mobileAttempt = 0;
  int otpAttempt = 0;

  StreamSubscription<String>? _otpSubscription;
  Timer? _resendTimer;

  LoginBloc({
    required this.authRepository,
    required this.notifier,
    required this.appBloc,
  }) : super(const LoginState.initial()) {
    // Initialize or reuse mobile number manager
    if (mobileNumberManager == null || mobileNumberManager!.isDisposed) {
      mobileNumberManager = TextFieldManager();
    }
    on<LoginEvent>(
      (event, emit) => event.map(
        initLogin: (_) async => await _onInit(emit),
        mobileChanged: (e) => _onMobileChanged(e, emit),
        submitLogin: (_) async => await _onSubmitLogin(emit),
        loginFailed: (_) => _onLoginFail(emit),
        initOTP: (e) => _onInitOTP(e.verificationId, e.mobileNumber, emit),
        updateTimer: (e) => _onUpdateTimer(e.second, emit),
        resendOTP: (_) => _onResendOTP(emit),
        otpChanged: (e) => _onOTPChanged(e, emit),
        submitOTP: (_) async => await _onSubmitOTP(emit),
        otpFailed: (_) => _onOTPFail(emit),
        googleSignin: (_) => _onGoogleSignin(emit),
      ),
    );
  }

  Future<void> _onInit(Emitter<LoginState> emit) async {
    await _showPhoneNumberHint();
    mobileNumberManager?.controller.addListener(() {
      add(LoginEvent.mobileChanged(mobileNumberManager?.text ?? ''));
    });
    emit(const LoginState.initial(
      mobile: '',
      showError: false,
      isLoading: false,
    ));
  }

  Future<void> _showPhoneNumberHint() async {
    try {
      FocusManager.instance.primaryFocus?.unfocus();
      await Future<void>.delayed(const Duration(milliseconds: 100));
      final phoneNumber = await SmsAutoFill().hint;
      if (phoneNumber != null && phoneNumber.isNotEmpty) {
        final digits = phoneNumber.replaceAll(RegExp(r'\D'), '');
        final last10Digits =
            digits.length > 10 ? digits.substring(digits.length - 10) : digits;
        mobileNumberManager?.text = last10Digits;
      }
    } catch (e) {
      debugPrint('Error getting phone hint: $e');
    }
  }

  void _onMobileChanged(LoginEvent event, Emitter<LoginState> emit) {
    event.maybeMap(
      mobileChanged: (e) {
        final mobile = e.value;
        if (mobileAttempt > 0) {
          ValidationState validationState =
              AppValidator.mobileNumberValidator(mobile);

          mobileNumberManager?.throwError(validationState.message ?? '');

          state.maybeMap(
            initial: (currentState) => emit(currentState.copyWith(
              mobile: mobile,
              showError: !validationState.valid,
            )),
            orElse: () {},
          );
        } else {
          state.maybeMap(
            initial: (currentState) =>
                emit(currentState.copyWith(mobile: mobile)),
            orElse: () {},
          );
        }
      },
      orElse: () {},
    );
  }

  Future<void> _onSubmitLogin(Emitter<LoginState> emit) async {
    mobileAttempt++;
    final mobileText = mobileNumberManager?.text ?? '';
    if (mobileText != state.mobile) {
      emit(state.copyWith(mobile: mobileText));
    }
    ValidationState validationState =
        AppValidator.mobileNumberValidator(mobileText);

    if (!validationState.valid) {
      mobileNumberManager?.throwError(validationState.message ?? '');
      emit(state.copyWith(
        showError: true,
      ));
      return;
    }

    emit(state.copyWith(isLoading: true, showError: false));

    if (!kIsWeb) {
      _otpSubscription?.cancel();
      SmsAutoFill().listenForCode();

      _otpSubscription = SmsAutoFill().code.listen((code) {
        state.mapOrNull(otp: (_) {
          if (code.isNotEmpty) {
            otpManager?.text = code;
            Future<void>.delayed(const Duration(milliseconds: 300), () {
              add(LoginEvent.submitOTP());
            });
          }
        });
      });
    }

    try {
      await authRepository.verifyMobileNumber(
        "+91${mobileNumberManager?.text}",
        verificationCompleted: (UserCredential credential) async {
          String token = await credential.user?.getIdToken() ?? '';
          Map<String, dynamic> user = {
            'uid': credential.user?.uid,
            'phoneNumber': credential.user?.phoneNumber,
            'displayName': credential.user?.displayName,
            'email': credential.user?.email,
            'photoURL': credential.user?.photoURL,
            'lastLogin': DateTime.now().toIso8601String(),
          };
          appBloc.add(AppEvent.loginWithProfileCheck(token, user: user));
        },
        verificationFailed: (Exception e) {
          add(LoginEvent.loginFailed());
          notifier.showSnackBar('Verification failed: ${e.toString()}');
        },
        codeSent: (String verificationId, int? resendToken) {
          add(LoginEvent.initOTP(
              verificationId, mobileNumberManager?.text ?? ''));
        },
      );
    } catch (e) {
      emit(state.copyWith(isLoading: false));
      notifier.showSnackBar(e.toString());
    }
  }

  void _onLoginFail(Emitter<LoginState> emit) {
    emit(LoginState.initial(
        isLoading: false, mobile: state.mobile, showError: state.showError));
  }

  //OTP Logic
  void _onInitOTP(
      String verificationId, String mobileNumber, Emitter<LoginState> emit) {
    if (otpManager == null || otpManager!.isDisposed) {
      otpManager = TextFieldManager();
      otpManager?.controller.addListener(() {
        add(LoginEvent.otpChanged(otpManager?.text ?? ''));
      });
    }
    emit(LoginState.otp(mobile: mobileNumber, verificationId: verificationId));
    _startNewOTPTimer();
  }

  Future<void> _startNewOTPTimer() async {
    _resendTimer?.cancel();
    _resendTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      state.mapOrNull(otp: (otpState) {
        if (otpState.resendSeconds > 0) {
          int second = otpState.resendSeconds - 1;
          add(LoginEvent.updateTimer(second));
        } else {
          add(LoginEvent.updateTimer(0));
          timer.cancel();
        }
      });
    });
  }

  void _onUpdateTimer(int second, Emitter<LoginState> emit) {
    state.mapOrNull(otp: (otpState) {
      emit(otpState.copyWith(resendSeconds: second, canResend: second <= 0));
    });
  }

  Future<void> _onResendOTP(Emitter<LoginState> emit) async {
    try {
      state.mapOrNull(otp: (otpState) {
        emit(otpState.copyWith(canResend: false, resendSeconds: 30));
      });

      _startNewOTPTimer();
      await authRepository.verifyMobileNumber(
        "+91${state.mobile}",
        verificationCompleted: (UserCredential credential) async {
          String token = await credential.user?.getIdToken() ?? '';
          Map<String, dynamic> user = {
            'uid': credential.user?.uid,
            'phoneNumber': credential.user?.phoneNumber,
            'displayName': credential.user?.displayName,
            'email': credential.user?.email,
            'photoURL': credential.user?.photoURL,
            'lastLogin': DateTime.now().toIso8601String(),
          };
          appBloc.add(AppEvent.loginWithProfileCheck(token, user: user));
        },
        verificationFailed: (Exception e) {
          add(LoginEvent.otpFailed());
          notifier.showSnackBar('Resend otp failed: ${e.toString()}');
        },
        codeSent: (String verificationId, int? resendToken) {
          add(LoginEvent.initOTP(
              verificationId, mobileNumberManager?.text ?? ''));
        },
      );
    } catch (e) {
      notifier.showSnackBar(e.toString());
    }
  }

  void _onOTPChanged(LoginEvent event, Emitter<LoginState> emit) {
    event.maybeMap(
      otpChanged: (e) {
        final otp = e.value;
        if (otpAttempt > 0) {
          ValidationState validationState = AppValidator.otpValidator(otp);
          otpManager?.throwError(validationState.message ?? '');

          state.mapOrNull(otp: (otpState) {
            emit(otpState.copyWith(
              otp: otp,
              showError: !validationState.valid,
            ));
          });
        } else {
          state.mapOrNull(otp: (otpState) {
            emit(otpState.copyWith(
              otp: otp,
            ));
          });
        }
      },
      orElse: () {},
    );
  }

  Future<void> _onSubmitOTP(Emitter<LoginState> emit) async {
    otpAttempt++;
    ValidationState validationState =
        AppValidator.otpValidator(otpManager?.text);

    if (!validationState.valid) {
      otpManager?.throwError(validationState.message ?? '');
      emit(state.copyWith(
        showError: true,
      ));
      return;
    }

    emit(state.copyWith(isLoading: true, showError: false));

    try {
      UserCredential? cred = await authRepository.validateOTP(
          state.mapOrNull(otp: (s) => s.verificationId) ?? '',
          otpManager?.text ?? '');

      String token = await cred.user?.getIdToken() ?? '';
      Map<String, dynamic> user = {
        'uid': cred.user?.uid,
        'phoneNumber': cred.user?.phoneNumber,
        'displayName': cred.user?.displayName,
        'email': cred.user?.email,
        'photoURL': cred.user?.photoURL,
        'lastLogin': DateTime.now().toIso8601String(),
      };
      // Login request with profile check will be handled by the app bloc
      appBloc.add(AppEvent.loginWithProfileCheck(token, user: user));
    } catch (e) {
      LogMessage.l('');
      emit(state.copyWith(isLoading: false));
      notifier.showSnackBar(e.toString());
    }
  }

  void _onOTPFail(Emitter<LoginState> emit) {
    emit(LoginState.otp(
        isLoading: false, mobile: state.mobile, showError: state.showError));
  }

  Future<void> _onGoogleSignin(Emitter<LoginState> emit) async {
    try {
      UserCredential? cred = await authRepository.googleSignin();

      String token = await cred.user?.getIdToken() ?? '';
      Map<String, dynamic> user = {
        'uid': cred.user?.uid,
        'phoneNumber': cred.user?.phoneNumber,
        'displayName': cred.user?.displayName,
        'email': cred.user?.email,
        'photoURL': cred.user?.photoURL,
        'lastLogin': DateTime.now().toIso8601String(),
      };
      appBloc.add(AppEvent.loginWithProfileCheck(token, user: user));
    } catch (e) {
      notifier.showSnackBar(e.toString());
    }
  }

  @override
  Future<void> close() {
    if (mobileNumberManager != null && !mobileNumberManager!.isDisposed) {
      mobileNumberManager?.dispose();
    }
    if (otpManager != null && !otpManager!.isDisposed) {
      otpManager?.dispose();
    }
    _otpSubscription?.cancel();
    _resendTimer?.cancel();
    return super.close();
  }
}
