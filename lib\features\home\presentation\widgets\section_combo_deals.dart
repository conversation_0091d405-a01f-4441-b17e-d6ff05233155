import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/core/utils/app_dimensions.dart';
import 'package:rozana/data/models/product_model.dart';
import 'package:rozana/widgets/custom_text.dart';

import '../../../../widgets/app_carousel.dart';
import '../../../../widgets/custom_image.dart';

class ComboDealSection extends StatelessWidget {
  const ComboDealSection({super.key, this.productCombos});

  final List<ProductComboModel>? productCombos;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      // height: MediaQuery.of(context).size.width,
      margin: EdgeInsets.fromLTRB(
          AppDimensions.screenHzPadding, 30, AppDimensions.screenHzPadding, 30),
      decoration: BoxDecoration(
        color: Color(0xFFF1311A),
        image: DecorationImage(
            fit: BoxFit.fill,
            image: AssetImage('assets/images/background_texture_2.png')),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 20, bottom: 20),
            child: Image.asset(
              'assets/images/combo_deals_text.png',
              width: MediaQuery.of(context).size.width * 0.52,
            ),
          ),
          AppCarousel(
            items: productCombos ?? [ProductComboModel(), ProductComboModel()],
            height: 285,
            viewportFraction: 0.9,
            borderRadius: 12,
            itemSpacing: 20,
            padding: EdgeInsets.all(0),
            autoPlayInterval: const Duration(seconds: 4),
            indicatorType: CarouselIndicatorType.dottedBar,
            activeIndicatorColor: AppColors.surface,
            inactiveIndicatorColor: Color(0xFFFF9184),
            itemBuilder: (context, product, index) {
              return ComboCard(
                comboProduct: product,
              );
            },
            onItemTap: (index) {
              HapticFeedback.lightImpact();
            },
          ),
          SizedBox(height: 30),
        ],
      ),
    );
  }
}

class ComboCard extends StatelessWidget {
  const ComboCard({super.key, this.comboProduct});
  final ProductComboModel? comboProduct;

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        Container(
          margin: EdgeInsets.only(bottom: 18),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Column(
            children: [
              Expanded(
                  child: Padding(
                padding: EdgeInsets.fromLTRB(12.5, 20, 12.5, 20),
                child: Column(
                  children: [
                    CustomText(
                      'Subah Ka Nashta Combo',
                      fontSize: 14,
                      fontWeight: FontWeight.w800,
                    ),
                    SizedBox(height: 10),
                    Stack(
                      alignment: Alignment.center,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: List.generate(
                              (comboProduct?.products?.length ?? 0) > 3
                                  ? 3
                                  : (comboProduct?.products?.length ?? 0), (i) {
                            ProductModel? product = comboProduct?.products?[i];
                            return Container(
                              height: 135,
                              width:
                                  (MediaQuery.of(context).size.width - 140) / 3,
                              padding: EdgeInsets.all(5),
                              margin: EdgeInsets.symmetric(horizontal: 2.5),
                              decoration: BoxDecoration(
                                color: Color(0xFFF3F2FF),
                                borderRadius: BorderRadius.circular(5),
                              ),
                              child: Column(
                                children: [
                                  CustomImage(
                                    imageUrl: product?.imageUrl,
                                    height: 70,
                                    width: double.infinity,
                                    fit: BoxFit.contain,
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 5),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        CustomText(
                                          product?.name ?? '--',
                                          fontSize: 12,
                                          fontWeight: FontWeight.w800,
                                        ),
                                        SizedBox(height: 5),
                                        FittedBox(
                                          child: RichText(
                                              text: TextSpan(
                                                  text:
                                                      '₹${(product?.price ?? 0).toStringAsFixed(0)}.',
                                                  children: [
                                                    TextSpan(
                                                        text: (product
                                                                    ?.originalPrice ??
                                                                product
                                                                    ?.price ??
                                                                0)
                                                            .toStringAsFixed(2)
                                                            .split('.')
                                                            .last,
                                                        style: TextStyle(
                                                            fontSize: 8))
                                                  ],
                                                  style: TextStyle(
                                                    color:
                                                        AppColors.textPrimary,
                                                    fontSize: 10,
                                                    fontWeight: FontWeight.w600,
                                                  ))),
                                        ),
                                        SizedBox(height: 5),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.end,
                                          children: [
                                            CustomText(
                                              '100g',
                                              fontSize: 10,
                                              color: Color(0xFF9CA3AF),
                                              fontWeight: FontWeight.w600,
                                            ),
                                            Expanded(
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.end,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  CustomText(
                                                    (product?.rating ?? 0)
                                                        .toStringAsFixed(1),
                                                    fontSize: 10,
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                  const SizedBox(width: 2),
                                                  Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            bottom: 2),
                                                    child: Image.asset(
                                                      'assets/icons/star.png',
                                                      width: 10,
                                                      height: 10,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }),
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 50),
                          child: Row(
                            children: List.generate(
                                (comboProduct?.products?.length ?? 0) > 3
                                    ? 2
                                    : ((comboProduct?.products?.length ?? 0) ==
                                            1)
                                        ? 1
                                        : (comboProduct?.products?.length ??
                                                0) -
                                            1, (i) {
                              return Expanded(
                                child: Image.asset(
                                  'assets/images/combo_plus.png',
                                  width: 30,
                                  height: 30,
                                ),
                              );
                            }),
                          ),
                        )
                      ],
                    )
                  ],
                ),
              )),
              Align(
                alignment: Alignment.bottomLeft,
                child: Row(
                  mainAxisSize: MainAxisSize.min, // Essential to wrap content
                  children: [
                    Flexible(
                      flex: 2,
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                        decoration: BoxDecoration(
                          color: Color(0xFF388E3C), // Darker green
                          borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(10),
                          ),
                        ),
                        alignment: Alignment.center,
                        child: FittedBox(
                          child: RichText(
                              text: TextSpan(
                                  text: '₹ ',
                                  children: [
                                    TextSpan(
                                        text:
                                            '${(comboProduct?.price ?? 0).toStringAsFixed(0)}.',
                                        style: TextStyle(fontSize: 16)),
                                    TextSpan(
                                      text: (comboProduct?.price ?? 0)
                                          .toStringAsFixed(2)
                                          .split('.')
                                          .last,
                                    )
                                  ],
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w800,
                                  ))),
                        ),
                      ),
                    ),
                    Flexible(
                      child: Stack(
                        children: [
                          Container(
                            constraints: BoxConstraints(minHeight: 35),
                            height: double.minPositive,
                            width: double.infinity,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 10, vertical: 8),
                            decoration: const BoxDecoration(
                              color: Color(0xFFFFD700), // Yellow
                              borderRadius: BorderRadius.only(
                                topRight: Radius.circular(
                                    5.0), // Creates the diagonal cut visually
                              ),
                            ),
                            child: FittedBox(
                              child: RichText(
                                  text: TextSpan(
                                      text: '₹ ',
                                      children: [
                                        TextSpan(
                                            text:
                                                '${(comboProduct?.originalPrice ?? comboProduct?.price ?? 0).toStringAsFixed(0)}.',
                                            style: TextStyle(fontSize: 16)),
                                        TextSpan(
                                          text: (comboProduct?.originalPrice ??
                                                  comboProduct?.price ??
                                                  0)
                                              .toStringAsFixed(2)
                                              .split('.')
                                              .last,
                                        )
                                      ],
                                      style: TextStyle(
                                        color: AppColors.textPrimary,
                                        fontSize: 12,
                                        fontWeight: FontWeight.w800,
                                      ))),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(
                                right: 6, top: 10, left: 6),
                            child:
                                Image.asset('assets/icons/discount_mark.png'),
                          )
                        ],
                      ),
                    ),
                    Expanded(flex: 3, child: SizedBox())
                  ],
                ),
              )
            ],
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Container(
              height: 40,
              padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
              margin: EdgeInsets.only(right: 35),
              decoration: BoxDecoration(
                color: Color(0xFFFFF19A),
                borderRadius: BorderRadius.circular(10),
                border: Border.all(
                  color: Color(0xFFF1311A),
                  width: 2,
                ),
              ),
              alignment: Alignment.center,
              child: FittedBox(
                child: CustomText(
                  'Add To Cart',
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
