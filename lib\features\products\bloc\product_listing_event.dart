import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../data/models/category_model.dart';

part 'product_listing_event.freezed.dart';

@freezed
class ProductListingEvent with _$ProductListingEvent {
  const factory ProductListingEvent.initial({
    CategoryModel? category,
    CategoryModel? subCategory,
  }) = _Initial;

  const factory ProductListingEvent.selectSubcategory({
    CategoryModel? subCategory,
  }) = _SelectSubcategory;
}
