import 'package:flutter/material.dart';
import 'package:rozana/domain/entities/product_entity.dart';

import '../../../../core/utils/app_dimensions.dart';
import '../../../products/presentation/widgets/product_card.dart';

class ProductGrid extends StatelessWidget {
  const ProductGrid({super.key, required this.productList});

  final List<ProductEntity>? productList;

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
        shrinkWrap: true,
        primary: false,
        padding:
            EdgeInsets.symmetric(horizontal: AppDimensions.screenHzPadding),
        itemCount: (productList == null)
            ? 6
            : (productList!.length > 5)
                ? 6
                : productList!.length,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            childAspectRatio: 0.67,
            mainAxisSpacing: 20,
            crossAxisSpacing: 10),
        itemBuilder: (ctx, index) {
          return DiscountedProductCard(
            product: productList?[index],
            isLoading: productList == null,
          );
        });
  }
}
