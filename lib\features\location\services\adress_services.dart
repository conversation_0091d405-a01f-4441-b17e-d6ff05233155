import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';

import 'locaion_services.dart';
import '../../../core/services/firestore_address_service.dart';
import '../../../core/dependency_injection/di_container.dart';
import '../../../core/utils/logger.dart';
import '../../../data/models/adress_model.dart';
import '../../../core/config/environment_config.dart';
import '../../../core/network/api_client.dart';

class AddressService {
  static final AddressService _instance = AddressService._internal();
  factory AddressService() => _instance;
  AddressService._internal();

  final LocationService _locationService = LocationService();
  FirestoreAddressService? _firestoreService;

  static const double _deliveryRadiusMeters = 100.0;

  // Simple state management
  List<AddressModel> _addresses = [];
  AddressModel? _currentSelectedAddress;
  Position? _lastKnownPosition;
  bool _addressesLoaded = false;

  /// Get Firestore service instance
  FirestoreAddressService get _firestore {
    _firestoreService ??= getIt<FirestoreAddressService>();
    return _firestoreService!;
  }

  /// Check if user is authenticated
  bool get isAuthenticated => _firestore.isUserAuthenticated;

  /// Get current selected address (cached, no location detection)
  AddressModel? get currentSelectedAddress => _currentSelectedAddress;

  /// Get current selected address without location detection (for UI display)
  AddressModel? getCurrentSelectedAddressSync() {
    return _currentSelectedAddress;
  }

  /// Manually set selected address (when user selects from address list)
  void setSelectedAddress(AddressModel address) {
    _currentSelectedAddress = address;
    LogMessage.p('Manually selected address: ${address.addressType}');

    // Notify that address has been manually selected
    _notifyAddressChanged();
  }

  /// Callback to notify when address changes (for LocationBloc)
  Function(AddressModel)? _onAddressChanged;

  /// Set callback for address changes
  void setOnAddressChanged(Function(AddressModel) callback) {
    _onAddressChanged = callback;
  }

  /// Notify listeners that address has changed
  void _notifyAddressChanged() {
    if (_currentSelectedAddress != null && _onAddressChanged != null) {
      _onAddressChanged!(_currentSelectedAddress!);
    }
  }

  /// Get all saved addresses (simple state management)
  Future<List<AddressModel>> getAllAddresses(
      {bool forceRefresh = false}) async {
    if (!isAuthenticated) return [];

    // If already loaded and not forcing refresh, return cached data
    if (_addressesLoaded && !forceRefresh) {
      LogMessage.p('Using cached addresses (${_addresses.length} addresses)');
      return _addresses;
    }

    try {
      LogMessage.p('Fetching addresses from Firestore');
      _addresses = await _firestore.getAllAddresses();
      _addressesLoaded = true;
      return _addresses;
    } catch (e) {
      LogMessage.p('Error fetching addresses: $e');
      return _addresses; // Return what we have, even if empty
    }
  }

  /// Get selected address based on current location (main method)
  Future<AddressModel?> getSelectedAddress() async {
    if (!isAuthenticated) return null;

    // Return cached selected address if available and location hasn't changed much
    if (_currentSelectedAddress != null && _lastKnownPosition != null) {
      final currentPosition = await _locationService.getCurrentPosition();
      if (currentPosition != null) {
        final distance = Geolocator.distanceBetween(
          _lastKnownPosition!.latitude,
          _lastKnownPosition!.longitude,
          currentPosition.latitude,
          currentPosition.longitude,
        );
        // If user hasn't moved more than 100m, return cached result
        if (distance < 100) {
          LogMessage.p(
              'Using cached selected address (moved ${distance.round()}m)');
          return _currentSelectedAddress;
        }
      }
    }

    try {
      // Get current location
      final currentPosition = await _locationService.getCurrentPosition();
      if (currentPosition == null) {
        // Fallback to first saved address if location unavailable
        final addresses = await getAllAddresses();
        return addresses.isNotEmpty ? addresses.first : null;
      }

      // Update last known position
      _lastKnownPosition = currentPosition;

      // Find nearest address within delivery radius
      final nearestAddress =
          await _findNearestAddressWithinRadius(currentPosition);

      if (nearestAddress != null) {
        // Found saved address within delivery radius
        _currentSelectedAddress = nearestAddress;
        return nearestAddress;
      }

      // No saved address within radius - return null
      // UI will show current location details and user can create address
      _currentSelectedAddress = null;
      return null;
    } catch (e) {
      LogMessage.p('Error getting selected address: $e');
      // Fallback to first saved address
      final addresses = await getAllAddresses();
      return addresses.isNotEmpty ? addresses.first : null;
    }
  }

  /// Manually refresh location and selected address (like Zepto's refresh button)
  /// Call this when user taps refresh or when returning from address screens
  Future<AddressModel?> refreshSelectedAddress() async {
    // Clear location cache to force fresh detection
    _lastKnownPosition = null;
    // Don't clear _currentSelectedAddress if user manually selected one

    // Get fresh selected address
    return await getSelectedAddress();
  }

  /// Force refresh addresses from server (call when needed)
  Future<List<AddressModel>> refreshAddresses() async {
    return await getAllAddresses(forceRefresh: true);
  }

  /// Save address to Firestore and update state
  Future<void> saveAddress(AddressModel address) async {
    if (!isAuthenticated) {
      throw Exception('User must be authenticated to save addresses');
    }

    try {
      await _firestore.saveAddress(address);

      // Update local state instead of refetching
      if (_addressesLoaded) {
        final existingIndex = _addresses.indexWhere((a) => a.id == address.id);
        if (existingIndex >= 0) {
          _addresses[existingIndex] = address; // Update existing
        } else {
          _addresses.add(address); // Add new
        }
        LogMessage.p('Updated local address state');
      }

      // Refresh selected address to potentially switch to new address if it's closer
      await getSelectedAddress();
    } catch (e) {
      LogMessage.p('Error saving address: $e');
      rethrow;
    }
  }

  /// Delete address from Firestore and update state
  Future<void> deleteAddress(String addressId) async {
    if (!isAuthenticated) {
      throw Exception('User must be authenticated to delete addresses');
    }

    try {
      await _firestore.deleteAddress(addressId);

      // Update local state instead of refetching
      if (_addressesLoaded) {
        _addresses.removeWhere((address) => address.id == addressId);
        LogMessage.p('Removed address from local state');
      }

      // Refresh selected address
      await getSelectedAddress();
    } catch (e) {
      LogMessage.p('Error deleting address: $e');
      rethrow;
    }
  }

  /// Find nearest saved address within delivery radius (500m)
  Future<AddressModel?> _findNearestAddressWithinRadius(
      Position currentPosition) async {
    final addresses = await getAllAddresses();
    if (addresses.isEmpty) return null;

    AddressModel? nearest;
    double minDistance = double.infinity;

    for (final address in addresses) {
      if (address.latitude != null && address.longitude != null) {
        final distance = Geolocator.distanceBetween(
          currentPosition.latitude,
          currentPosition.longitude,
          address.latitude!.toDouble(),
          address.longitude!.toDouble(),
        );

        // Only consider addresses within delivery radius
        if (distance <= _deliveryRadiusMeters && distance < minDistance) {
          minDistance = distance;
          nearest = address;
        }
      }
    }

    return nearest;
  }

  /// Get distance to selected address (for UI display)
  Future<double?> getDistanceToSelectedAddress() async {
    if (_currentSelectedAddress == null || _lastKnownPosition == null) {
      return null;
    }

    if (_currentSelectedAddress!.latitude != null &&
        _currentSelectedAddress!.longitude != null) {
      return Geolocator.distanceBetween(
        _lastKnownPosition!.latitude,
        _lastKnownPosition!.longitude,
        _currentSelectedAddress!.latitude!.toDouble(),
        _currentSelectedAddress!.longitude!.toDouble(),
      );
    }

    return null;
  }

  /// Check if user needs to go to settings for location permission
  Future<bool> shouldOpenAppSettings() async {
    final permission = await Geolocator.checkPermission();
    return permission == LocationPermission.deniedForever;
  }

  /// Open location settings
  Future<bool> openLocationSettings() async {
    try {
      return await Geolocator.openLocationSettings();
    } catch (e) {
      LogMessage.p('Error opening location settings: $e');
      return false;
    }
  }

  /// Open app settings
  Future<bool> openAppSettings() async {
    try {
      return await Geolocator.openAppSettings();
    } catch (e) {
      debugPrint('Error opening app settings: $e');
      return false;
    }
  }

  // Get current position
  Future<Position?> getCurrentPosition() async {
    try {
      // Check if we have permission first
      final hasPermission = await _locationService.checkLocationPermission();
      if (!hasPermission) {
        debugPrint('Location permission not granted');
        return null;
      }

      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        debugPrint('Location services are disabled');
        return null;
      }

      // Get current position with timeout and error handling
      // Try with high accuracy first, then fall back to medium if it fails
      try {
        return await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high,
          timeLimit: const Duration(seconds: 10),
        );
      } catch (e) {
        debugPrint('High accuracy failed, trying medium accuracy: $e');
        return await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.medium,
          timeLimit: const Duration(seconds: 15),
        );
      }
    } catch (e) {
      debugPrint('Error getting current position: $e');
      return null;
    }
  }

  // Get address from coordinates
  Future<List<Placemark>?> getAddressFromCoordinates(
      double latitude, double longitude) async {
    try {
      return await placemarkFromCoordinates(latitude, longitude);
    } catch (e) {
      debugPrint('Error getting address from coordinates: $e');
      return null;
    }
  }

  // Convert Placemark to AddressModel
  AddressModel placemarkToAddressModel(Placemark placemark, Position position,
      {String addressType = 'home', bool isDefault = false}) {
    final id = DateTime.now().millisecondsSinceEpoch.toString();

    final street = placemark.street ?? '';
    final subLocality = placemark.subLocality ?? '';
    final locality = placemark.locality ?? '';
    final administrativeArea = placemark.administrativeArea ?? '';
    final postalCode = placemark.postalCode ?? '';

    // Create address line 1 from street and subLocality
    final addressLine1 =
        [street, subLocality].where((e) => e.isNotEmpty).join(', ');

    // Create full address
    final fullAddress = [
      street,
      subLocality,
      locality,
      administrativeArea,
      postalCode,
    ].where((e) => e.isNotEmpty).join(', ');

    return AddressModel(
      id: id,
      fullAddress: fullAddress,
      addressLine1: addressLine1,
      city: locality,
      state: administrativeArea,
      pincode: postalCode,
      latitude: position.latitude,
      longitude: position.longitude,
      addressType: addressType,
      isDefault: isDefault,
    );
  }

  // Search for addresses based on query
  Future<List<Location>?> searchAddresses(String query) async {
    try {
      return await locationFromAddress(query);
    } catch (e) {
      debugPrint('Error searching addresses: $e');
      return null;
    }
  }

  // Get placemark from location
  Future<List<Placemark>?> getPlacemarkFromLocation(Location location) async {
    try {
      return await placemarkFromCoordinates(
          location.latitude, location.longitude);
    } catch (e) {
      debugPrint('Error getting placemark from location: $e');
      return null;
    }
  }

  /// Initialize address service (call this when user logs in)
  Future<void> initialize() async {
    if (isAuthenticated) {
      // Load addresses once on app start
      await getAllAddresses();
      // Get initial selected address (this will cache it for UI screens)
      await getSelectedAddress();
      LogMessage.p(
          'AddressService initialized with ${_addresses.length} addresses');
    }
  }

  /// Cleanup resources (call this when user logs out)
  void dispose() {
    _currentSelectedAddress = null;
    _lastKnownPosition = null;
    _addresses.clear();
    _addressesLoaded = false;
    _onAddressChanged = null; // Clear callback to prevent memory leaks
    LogMessage.p('AddressService disposed - all user data cleared');
  }

  /// Reset for new user session
  void reset() {
    dispose();
  }

  /// Get formatted distance string for UI
  String formatDistance(double distanceMeters) {
    if (distanceMeters < 1000) {
      return '${distanceMeters.round()}m away';
    } else {
      final km = distanceMeters / 1000;
      return '${km.toStringAsFixed(1)}km away';
    }
  }

  /// Get delivery radius in meters (for external use)
  static double get deliveryRadius => _deliveryRadiusMeters;

  // Get place details including building name using Google Places API
  Future<Map<String, dynamic>?> getPlaceDetails(
      double latitude, double longitude) async {
    try {
      final apiKey = EnvironmentConfig.googlePlacesApiKey;

      // First, try to find nearby places using Nearby Search
      final nearbyResponse = await ApiClient.sendHttpRequest(
        endUrl: '',
        method: HttpMethod.get,
        customBaseUrl: EnvironmentConfig.googlePlacesNearbySearchUrl,
        isExternalApi: true,
        queryParameters: {
          'location': '$latitude,$longitude',
          'radius': '10', // 10 meter radius for more precise results
          'key': apiKey,
        },
        tag: 'GooglePlacesNearby',
      );

      if (nearbyResponse?.statusCode == 200) {
        final nearbyData = nearbyResponse!.data;
        final results = nearbyData['results'] as List?;

        if (results != null && results.isNotEmpty) {
          // Find the best place (filter out broad location types)
          final bestPlace = _findBestPlace(results);
          if (bestPlace == null) {
            return null;
          }

          final closestPlace = bestPlace;

          // If we have a place_id, get detailed information
          final placeId = closestPlace['place_id'] as String?;
          if (placeId != null) {
            final detailsResponse = await ApiClient.sendHttpRequest(
              endUrl: '',
              method: HttpMethod.get,
              customBaseUrl: EnvironmentConfig.googlePlacesDetailsUrl,
              isExternalApi: true,
              queryParameters: {
                'place_id': placeId,
                'fields': 'name,formatted_address,types',
                'key': apiKey,
              },
              tag: 'GooglePlacesDetails',
            );

            if (detailsResponse?.statusCode == 200) {
              final detailsData = detailsResponse!.data;
              final result = detailsData['result'] as Map<String, dynamic>?;
              if (result != null) {
                return {
                  'name': result['name'] ?? '',
                  'formatted_address': result['formatted_address'] ?? '',
                  'types': result['types'] ?? [],
                  'place_id': placeId,
                };
              }
            }
          }

          // Fallback to nearby search result if details API fails
          return {
            'name': closestPlace['name'] ?? '',
            'formatted_address': closestPlace['vicinity'] ?? '',
            'types': closestPlace['types'] ?? [],
            'place_id': closestPlace['place_id'] ?? '',
          };
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error getting place details: $e');
      return null;
    }
  }

  // Enhanced method to get address with building name
  Future<String> getEnhancedAddress(double latitude, double longitude) async {
    try {
      // Always get geocoding data for complete address information
      final placemarks = await getAddressFromCoordinates(latitude, longitude);
      String fallbackAddress = 'Address not found';

      if (placemarks != null && placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        fallbackAddress = _buildCompleteAddressString(placemark);
      }

      // Try to get place details for building/establishment names
      final placeDetails = await getPlaceDetails(latitude, longitude);

      if (placeDetails != null &&
          placeDetails['name'] != null &&
          placeDetails['name'].toString().isNotEmpty) {
        final placeName = placeDetails['name'].toString();
        final formattedAddress =
            placeDetails['formatted_address']?.toString() ?? '';

        // If we have a meaningful place name (not just an address), use it
        if (placeName.isNotEmpty && !_isAddressLikeName(placeName)) {
          // For buildings/establishments, combine place name with complete address
          if (placemarks != null && placemarks.isNotEmpty) {
            final placemark = placemarks.first;
            final completeAddress = _buildCompleteAddressString(placemark);

            // If the place name is not already in the address, prepend it
            if (!completeAddress
                .toLowerCase()
                .contains(placeName.toLowerCase())) {
              return '$placeName, $completeAddress';
            } else {
              return completeAddress;
            }
          }

          // Fallback to formatted address from Places API
          if (formattedAddress.isNotEmpty) {
            return formattedAddress;
          }

          return '$placeName, $fallbackAddress';
        }
      }

      // If geocoding doesn't provide street info, try Google Geocoding API
      if (placemarks == null ||
          placemarks.isEmpty ||
          (placemarks.first.street == null ||
              placemarks.first.street!.isEmpty)) {
        final googleGeocodedAddress =
            await _getGoogleGeocodedAddress(latitude, longitude);
        if (googleGeocodedAddress.isNotEmpty) {
          fallbackAddress = googleGeocodedAddress;
        }
      }

      // Return the complete geocoded address (includes road names, area, city, state)
      return fallbackAddress;
    } catch (e) {
      debugPrint('Error getting enhanced address: $e');
      return 'Failed to get address';
    }
  }

  // Helper method to get address using Google Geocoding API
  Future<String> _getGoogleGeocodedAddress(
      double latitude, double longitude) async {
    try {
      final apiKey = EnvironmentConfig.googlePlacesApiKey;

      final response = await ApiClient.sendHttpRequest(
        endUrl: '',
        method: HttpMethod.get,
        customBaseUrl: EnvironmentConfig.googleGeocodingUrl,
        isExternalApi: true,
        queryParameters: {
          'latlng': '$latitude,$longitude',
          'result_type': 'street_address|route|intersection',
          'key': apiKey,
        },
        tag: 'GoogleGeocoding',
      );

      if (response?.statusCode == 200) {
        final data = response!.data;

        if (data['status'] == 'OK' &&
            data['results'] != null &&
            data['results'].isNotEmpty) {
          final result = data['results'][0];
          final formattedAddress =
              result['formatted_address']?.toString() ?? '';

          if (formattedAddress.isNotEmpty) {
            return formattedAddress;
          }
        }
      }

      return '';
    } catch (e) {
      debugPrint('Error getting Google geocoded address: $e');
      return '';
    }
  }

  // Helper method to check if a place name is just an address
  bool _isAddressLikeName(String name) {
    // Check if the name looks like an address (contains numbers, common address words)
    final addressPatterns = [
      RegExp(r'^\d+'), // Starts with numbers
      RegExp(r'\d+.*\d+'), // Contains multiple numbers
      RegExp(r'(street|road|avenue|lane|drive|way|place|court|circle)',
          caseSensitive: false),
    ];

    return addressPatterns.any((pattern) => pattern.hasMatch(name));
  }

  // Helper method to find the best place from results (filter out broad location types)
  Map<String, dynamic>? _findBestPlace(List<dynamic> results) {
    // Define priority order for place types (higher priority = better)
    final typePriorities = {
      // Specific establishments (highest priority)
      'restaurant': 100,
      'store': 95,
      'shopping_mall': 95,
      'hospital': 90,
      'school': 90,
      'bank': 90,
      'gas_station': 90,
      'pharmacy': 90,
      'lodging': 85,
      'tourist_attraction': 85,
      'gym': 85,
      'beauty_salon': 85,
      'car_dealer': 85,
      'car_repair': 85,
      'clothing_store': 85,
      'electronics_store': 85,
      'furniture_store': 85,
      'grocery_or_supermarket': 85,
      'hardware_store': 85,
      'home_goods_store': 85,
      'jewelry_store': 85,
      'laundry': 85,
      'meal_delivery': 85,
      'meal_takeaway': 85,
      'movie_theater': 85,
      'night_club': 85,
      'pet_store': 85,
      'shoe_store': 85,
      'spa': 85,
      'storage': 85,
      'supermarket': 85,
      'veterinary_care': 85,

      // General establishments
      'establishment': 80,
      'point_of_interest': 75,
      'food': 70,

      // Buildings and premises
      'premise': 60,
      'subpremise': 55,

      // Broad location types (lower priority)
      'neighborhood': 30,
      'sublocality': 25,
      'sublocality_level_1': 25,
      'sublocality_level_2': 20,
      'locality': 10, // City names
      'political': 5, // Administrative areas
      'administrative_area_level_1': 5,
      'administrative_area_level_2': 5,
      'country': 1,
    };

    Map<String, dynamic>? bestPlace;
    int highestPriority = -1;

    for (final result in results) {
      final place = result as Map<String, dynamic>;
      final types = (place['types'] as List<dynamic>?)?.cast<String>() ?? [];
      final name = place['name']?.toString() ?? '';

      // Skip if no name
      if (name.isEmpty) continue;

      // Calculate priority based on types
      int priority = 0;
      for (final type in types) {
        final typePriority = typePriorities[type] ?? 0;
        if (typePriority > priority) {
          priority = typePriority;
        }
      }

      // Boost priority for non-address-like names
      if (!_isAddressLikeName(name)) {
        priority += 10;
      }

      if (priority > highestPriority) {
        highestPriority = priority;
        bestPlace = place;
      }
    }

    return bestPlace;
  }

  // Helper method to build complete address string from placemark (includes state, country)
  String _buildCompleteAddressString(dynamic placemark) {
    List<String> addressParts = [];

    // Add street/road number and name
    if (placemark.street != null && placemark.street!.isNotEmpty) {
      addressParts.add(placemark.street!);
    }

    // Add sub-locality (area/neighborhood)
    if (placemark.subLocality != null && placemark.subLocality!.isNotEmpty) {
      addressParts.add(placemark.subLocality!);
    }

    // Add locality (city/town)
    if (placemark.locality != null && placemark.locality!.isNotEmpty) {
      addressParts.add(placemark.locality!);
    }

    // Add administrative area (state/province)
    if (placemark.administrativeArea != null &&
        placemark.administrativeArea!.isNotEmpty) {
      addressParts.add(placemark.administrativeArea!);
    }

    // Add postal code if available
    if (placemark.postalCode != null && placemark.postalCode!.isNotEmpty) {
      addressParts.add(placemark.postalCode!);
    }

    // Add country
    if (placemark.country != null && placemark.country!.isNotEmpty) {
      addressParts.add(placemark.country!);
    }

    return addressParts.isNotEmpty
        ? addressParts.join(', ')
        : 'Unknown location';
  }
}
