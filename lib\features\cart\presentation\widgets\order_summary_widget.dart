import 'package:flutter/material.dart';

class OrderSummaryWidget extends StatelessWidget {
  final Map<String, dynamic> orderData;

  const OrderSummaryWidget({
    super.key,
    required this.orderData,
  });

  @override
  Widget build(BuildContext context) {
    // Extract order summary from orderData
    final int itemCount = orderData['itemCount'] ?? 0;
    final num subtotal = orderData['subtotal'] ?? 0.0;
    final num deliveryFee = orderData['deliveryFee'] ?? 0.0;
    final num tax = orderData['tax'] ?? 0.0;
    final num discount = orderData['discount'] ?? 0.0;
    final num total = orderData['total'] ?? 0.0;
    final String paymentMethod = orderData['paymentMethod'] ?? 'Cash on Delivery';
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          _buildSummaryRow('Items', '$itemCount items'),
          const SizedBox(height: 8),
          _buildSummaryRow('Subtotal', '₹${subtotal.toStringAsFixed(2)}'),
          const SizedBox(height: 8),
          _buildSummaryRow('Delivery Fee', '₹${deliveryFee.toStringAsFixed(2)}'),
          const SizedBox(height: 8),
          _buildSummaryRow('Tax', '₹${tax.toStringAsFixed(2)}'),
          if (discount > 0) ...[  
            const SizedBox(height: 8),
            _buildSummaryRow('Discount', '-₹${discount.toStringAsFixed(2)}'),
          ],
          const Divider(height: 16),
          _buildSummaryRow('Total', '₹${total.toStringAsFixed(2)}', isTotal: true),
          const SizedBox(height: 8),
          _buildSummaryRow('Payment Method', paymentMethod),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isTotal ? 16 : 14,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            color: Colors.black54,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: isTotal ? 16 : 14,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.w600,
            color: isTotal ? Colors.black : Colors.black87,
          ),
        ),
      ],
    );
  }
}
