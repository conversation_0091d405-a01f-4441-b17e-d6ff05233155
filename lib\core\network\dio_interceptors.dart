import 'dart:async';
import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

import '../dependency_injection/di_container.dart';
import '../services/app_preferences_service.dart';
import '../services/token_refresh_service.dart';
import '../utils/logger.dart';

// ======================= Token Interceptor =======================
class TokenInterceptor extends Interceptor {
  late final TokenRefreshService _tokenRefreshService;
  final Dio _dio = Dio();
  bool _isRefreshing = false;

  // Queue of requests that are waiting for token refresh
  final List<_RetryRequest> _queue = [];

  TokenInterceptor() {
    _tokenRefreshService = getIt<TokenRefreshService>();
  }

  @override
  void onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    final token = AppPreferences.getToken();
    if ((token ?? '').isNotEmpty) {
      options.headers['Authorization'] = token;
    }
    super.onRequest(options, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    // Check if the error is due to an unauthorized request (401)
    if (err.response?.statusCode == 401) {
      LogMessage.p('Received 401 error - attempting to refresh token');

      // Get the original request options to retry later
      final options = err.requestOptions;

      // Try to refresh the token and retry the request
      try {
        final newToken = await _refreshToken();

        // Update the request header with the new token
        options.headers['Authorization'] = newToken;

        // Retry the request with the new token
        final response = await _retry(options);
        handler.resolve(response);
        return;
      } catch (e) {
        // If token refresh fails, pass the original error
        LogMessage.p('Token refresh failed: $e');
      }
    }

    // For other errors or if token refresh fails, proceed with the original error
    super.onError(err, handler);
  }

  /// Refreshes the token and processes any queued requests
  Future<String> _refreshToken() async {
    // If already refreshing, wait for it to complete
    if (_isRefreshing) {
      // Wait for the refresh to complete and return the new token
      return await Future.delayed(const Duration(milliseconds: 500), () {
        final token = AppPreferences.getToken();
        if (token == null || token.isEmpty) {
          throw 'Token refresh failed';
        }
        return token;
      });
    }

    try {
      _isRefreshing = true;

      // Refresh the token
      final newToken = await _tokenRefreshService.refreshToken();

      // Process any queued requests with the new token
      _processQueue(newToken);

      return newToken;
    } finally {
      _isRefreshing = false;
    }
  }

  /// Retry a request with updated options
  Future<Response<dynamic>> _retry(RequestOptions options) async {
    final response = await _dio.request<dynamic>(
      options.path,
      data: options.data,
      queryParameters: options.queryParameters,
      options: Options(
        method: options.method,
        headers: options.headers,
        responseType: options.responseType,
        contentType: options.contentType,
        validateStatus: options.validateStatus,
        receiveTimeout: options.receiveTimeout,
        sendTimeout: options.sendTimeout,
        extra: options.extra,
      ),
    );

    return response;
  }

  /// Process queued requests with a new token
  void _processQueue(String token) {
    for (var request in _queue) {
      request.options.headers['Authorization'] = token;
      request.completer.complete(_retry(request.options));
    }

    _queue.clear();
  }
}

/// Helper class to store retry request information
class _RetryRequest {
  final RequestOptions options;
  final Completer<Response<dynamic>> completer;

  _RetryRequest(this.options, this.completer);
}

// ======================= Logging Interceptor =======================
class LoggingInterceptor extends Interceptor {
  final String tag;

  LoggingInterceptor({required this.tag});

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    LogMessage.p(
        "REQUEST [${options.method}] => ${options.baseUrl}${options.path}",
        subTag: tag,
        color: Colors.white);
    LogMessage.p("HEADERS: ${jsonEncode(options.headers)}", subTag: tag);
    LogMessage.p("QUERY: ${jsonEncode(options.queryParameters)}",
        subTag: tag, color: Colors.orange);
    LogMessage.p("BODY: ${jsonEncode(options.data)}",
        subTag: tag, color: Colors.orange);
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    LogMessage.l(
        "RESPONSE [${response.statusCode}] => ${jsonEncode(response.data)}",
        subTag: tag,
        color: Colors.green);
    super.onResponse(response, handler);
  }
}
