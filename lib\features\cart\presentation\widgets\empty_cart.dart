import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../../../routes/app_router.dart';
import '../../../../widgets/custom_button.dart';

class EmptyCart extends StatelessWidget {
  const EmptyCart({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Empty cart illustration
            Image.asset(
              'assets/images/empty_cart.png',
              height: 180,
              fit: BoxFit.contain,
              errorBuilder: (context, error, stackTrace) => Container(
                height: 180,
                width: 180,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.shopping_cart_outlined,
                  size: 80,
                  color: Colors.grey[400],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Empty cart message
            const Text(
              'Your Cart is Empty',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),

            // Suggestion text
            Text(
              'Looks like you haven\'t added anything to your cart yet.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),

            // Shop now button
            AppButton(
              text: 'Start Shopping',
              onPressed: () {
                context.go(RouteNames.home);
              },
              backgroundColor: AppColors.primary,
              textColor: Colors.white,
              borderRadius: 8,
              width: 200,
            ),
          ],
        ),
      ),
    );
  }
}
