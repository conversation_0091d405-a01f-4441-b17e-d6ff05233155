import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:rozana/widgets/viewall_category_title.dart';

import '../../../../domain/entities/category_entity.dart';
import '../../../../data/mappers/category_mapper.dart';
import '../../../../data/models/category_model.dart';
import '../../../../data/services/data_loading_manager.dart';
import '../../../../core/dependency_injection/di_container.dart';

import 'categoy_card.dart';
import 'category_skeleton_loader.dart';
import '../../../../widgets/lazy_loading_widget.dart';

class SubCategoriesSection extends StatefulWidget {
  final CategoryEntity? parentCategory;
  final VoidCallback? onSeeAllTap;
  final Function(CategoryEntity?)? onSubcategorySelected;
  final bool showTitleBar;

  final bool preloadData;
  final bool useGridView;
  final int gridCrossAxisCount;
  final double gridChildAspectRatio;
  final bool showAsRow;

  const SubCategoriesSection({
    super.key,
    this.onSeeAllTap,
    this.onSubcategorySelected,

    // DataLoadingManager is now accessed directly
    this.preloadData = true,
    this.useGridView = false,
    this.gridCrossAxisCount = 4,
    this.gridChildAspectRatio = 0.72,
    this.showAsRow = false,
    this.parentCategory,
    this.showTitleBar = true,
  });

  @override
  State<SubCategoriesSection> createState() => _SubCategoriesSectionState();
}

class _SubCategoriesSectionState extends State<SubCategoriesSection> {
  final DataLoadingManager _dataManager = getIt<DataLoadingManager>();
  final List<CategoryEntity> _subCategories = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.preloadData) {
      _loadCategories();
    }
  }

  Future<void> _loadCategories() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final allCategory = const CategoryEntity(
          id: 'All',
          name: 'All',
          count: 0,
          imageUrl: 'assets/categories/shopping-bag.png');

      final categories = widget.parentCategory == null
          ? await _dataManager.loadCategories(
              pageSize: 20) // Load all categories at once
          : await _dataManager.loadSubCategories(
              categoryID: widget.parentCategory?.id ?? '',
              pageSize: 20); // Load subcategories for specific category

      setState(() {
        _subCategories.clear();
        if (widget.parentCategory != null) {
          _subCategories.add(allCategory);
        }
        _subCategories.addAll(categories
            .map(
                (json) => CategoryMapper.toEntity(CategoryModel.fromJson(json)))
            .toList());
        _isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showTitleBar)
          Padding(
            padding: const EdgeInsets.only(left: 16, right: 8),
            child: ViewAllCategoryTitle(
              title: widget.parentCategory?.name ?? '',
              onTap: () {
                HapticFeedback.lightImpact();
                widget.onSeeAllTap?.call();
              },
            ),
          ),
        const SizedBox(height: 8),
        _isLoading
            ? Center(
                child: CategorySkeletonLoader(
                  useGridView: widget.useGridView,
                  showAsRow: widget.showAsRow,
                  gridCrossAxisCount: widget.gridCrossAxisCount,
                  gridChildAspectRatio: widget.gridChildAspectRatio,
                ),
              )
            : widget.showAsRow
                ? _buildCategoryRow()
                : widget.useGridView
                    ? _buildCategoryGrid()
                    : _buildDefaultCategoryGrid(),
      ],
    );
  }

  Widget _buildDefaultCategoryGrid() {
    return GridView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      padding: const EdgeInsets.symmetric(horizontal: 12),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.gridCrossAxisCount,
        childAspectRatio: widget.gridChildAspectRatio,
        crossAxisSpacing: 8,
        mainAxisSpacing: 12,
      ),
      itemCount: _subCategories.length,
      itemBuilder: (context, index) {
        CategoryEntity subCategory = _subCategories[index];
        return CategoryCard(
          category: widget.parentCategory,
          subCategory: subCategory,
          imagePadding: EdgeInsets.all(10),
          subCategoryTheme: true,
          radius: 10,
          height: 100,
          fontSize: 10,
        );
      },
    );
  }

  Widget _buildCategoryGrid() {
    return GridLazyLoadingWidget<CategoryEntity>(
      items: _subCategories,
      isLoading: _isLoading,
      hasMoreData: false,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.gridCrossAxisCount,
        childAspectRatio: widget.gridChildAspectRatio,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemBuilder: (context, category, index) {
        CategoryEntity subCategory = _subCategories[index];

        return CategoryCard(
          category: widget.parentCategory,
          subCategory: subCategory,
          imagePadding: EdgeInsets.all(10),
        );
      },
    );
  }

  Widget _buildCategoryRow() {
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _subCategories.length,
      itemBuilder: (context, index) {
        CategoryEntity subCategory = _subCategories[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: CategoryCard(
            category: widget.parentCategory,
            subCategory: subCategory,
            imagePadding: EdgeInsets.all(12),
            height: 70,
            fontSize: 10,
            onTap: () {
              widget.onSubcategorySelected?.call(subCategory);
            },
          ),
        );
      },
    );
  }
}
