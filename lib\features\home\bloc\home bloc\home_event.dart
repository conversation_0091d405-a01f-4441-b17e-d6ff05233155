// import 'package:freezed_annotation/freezed_annotation.dart';

// part 'home_event.freezed.dart';

// @freezed
// class HomeEvent with _$HomeEvent {
//   const factory HomeEvent.loadBanners() = _LoadBanners;
// }

import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../domain/entities/banner_entity.dart';
import '../../../../domain/entities/category_entity.dart';
import '../../../../domain/entities/product_entity.dart';

part 'home_event.freezed.dart'; // This file will be generated

@freezed
abstract class HomeEvent with _$HomeEvent {
  const factory HomeEvent.init() = InitHome;
  const factory HomeEvent.updateScroll(bool scroll) = UpdateScroll;
  const factory HomeEvent.updateLoadedList({
    List<CategoryEntity>? categories,
    List<ProductEntity>? previouslyBought,
    List<ProductEntity>? mostPopular,
    List<ProductEntity>? mostBought,
    List<BannerEntity>? banners,
  }) = UpdateHomeList;
  const factory HomeEvent.loadHomeData() = LoadHomeData;
  const factory HomeEvent.deepLinkFound(
      String route, Map<String, dynamic> args) = LoadDeepLink;
  // You can add more events here if needed, e.g.,
  // const factory HomeEvent.refreshHomeData() = RefreshHomeData;
}
