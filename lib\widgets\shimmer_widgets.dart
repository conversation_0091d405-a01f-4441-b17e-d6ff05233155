import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class CustomShimmer extends StatelessWidget {
  const CustomShimmer({
    super.key,
    this.height,
    this.width,
    this.radius,
    this.margin = const EdgeInsets.all(0.5),
    this.padding,
    this.boxShadow,
    this.child,
    this.baseColor,
    this.highlightColor,
    this.mainColor,
    this.borderRadius,
    this.border,
    this.direction = ShimmerDirection.ltr,
  });

  final double? height;
  final double? width;
  final double? radius;
  final BorderRadius? borderRadius;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final List<BoxShadow>? boxShadow;
  final Widget? child;
  final Color? baseColor;
  final Color? highlightColor;
  final Color? mainColor;
  final Border? border;
  final ShimmerDirection direction;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          border: border,
          borderRadius: borderRadius ?? BorderRadius.circular(radius ?? 0)),
      child: Shimmer.fromColors(
        period: const Duration(milliseconds: 1500),
        direction: direction,
        baseColor: baseColor ?? Colors.grey.shade200.withValues(alpha: 0.8),
        highlightColor: highlightColor ?? Colors.white.withValues(alpha: 0.2),
        child: Container(
          height: height,
          width: width,
          margin: margin,
          padding: padding,
          decoration: BoxDecoration(
              color: mainColor ?? Colors.grey.shade100,
              boxShadow: boxShadow,
              borderRadius: borderRadius ?? BorderRadius.circular(radius ?? 6)),
          child: child,
        ),
      ),
    );
  }
}

class ShimmerTextField extends CustomShimmer {
  const ShimmerTextField({
    super.key,
    super.height = 50,
    super.width = double.infinity,
    super.margin,
  });
}

class ShimmerText extends CustomShimmer {
  const ShimmerText({
    super.key,
    super.height = 14,
    super.width = double.infinity,
    super.radius = 6,
    super.margin = const EdgeInsets.symmetric(vertical: 5),
  });
}

class ShimmerBox extends CustomShimmer {
  const ShimmerBox({
    super.key,
    super.height = 100,
    super.width = double.infinity,
    super.radius = 6,
    super.margin,
    super.direction,
  });
}
