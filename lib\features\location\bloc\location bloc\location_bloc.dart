export 'location_event.dart';
export 'location_state.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import '../../../../data/models/adress_model.dart';
import '../../services/adress_services.dart';
import '../../services/locaion_services.dart';
import 'location_event.dart';
import 'location_state.dart';

class LocationBloc extends Bloc<LocationEvent, LocationState> {
  final AddressService addressService;
  final LocationService locationService;

  LocationBloc({
    required this.addressService,
    required this.locationService,
  }) : super(const LocationState.initial()) {
    on<LocationEvent>(
      (event, emit) => event.map(
        started: (_) => _onStarted(emit),
        refreshLocation: (e) => _onRefreshLocation(emit),
        requestPermissionAndDetect: (_) => _onRequestPermissionAndDetect(emit),
      ),
    );

    // Listen for manual address changes
    addressService.setOnAddressChanged((address) {
      add(const LocationEvent.refreshLocation());
    });
  }

  Future<void> _onStarted(Emitter<LocationState> emit) async {
    emit(const LocationState.loading());
    try {
      final selectedAddress = await addressService.getSelectedAddress();
      if (selectedAddress != null) {
        emit(LocationState.loaded(selectedAddress));
      } else {
        // If no selected address, try to request permission and detect location
        add(const LocationEvent.requestPermissionAndDetect());
      }
    } catch (e) {
      emit(LocationState.error('Failed to load location.'));
    }
  }

  Future<void> _onRequestPermissionAndDetect(
      Emitter<LocationState> emit) async {
    emit(const LocationState.loading());

    try {
      // Check if we already have permission
      final hasPermission = await locationService.checkLocationPermission();

      if (!hasPermission) {
        // Request permission on first launch
        await locationService.requestLocationPermission();
      }

      // Just detect location without saving (for permission and positioning)
      await locationService.getCurrentPosition();

      // Always fall back to refresh location to handle existing addresses
      add(const LocationEvent.refreshLocation());
    } catch (e) {
      // If anything fails, fall back to refresh location
      add(const LocationEvent.refreshLocation());
    }
  }

  Future<void> _onRefreshLocation(Emitter<LocationState> emit) async {
    emit(const LocationState.loading());

    try {
      // Always use cached selected address (respects manual selection)
      final selectedAddress = addressService.getCurrentSelectedAddressSync();
      if (selectedAddress != null) {
        emit(LocationState.loaded(selectedAddress));
        return;
      }

      // If no cached address, do fresh detection
      final detectedAddress = await addressService.getSelectedAddress();
      if (detectedAddress != null) {
        emit(LocationState.loaded(detectedAddress));
        return;
      }

      // No saved address within 500m - show current location details
      final currentPosition = await locationService.getCurrentPosition();
      if (currentPosition != null) {
        final placemarks = await locationService.getAddressFromCoordinates(
          currentPosition.latitude,
          currentPosition.longitude,
        );
        if (placemarks != null && placemarks.isNotEmpty) {
          final address = _createTempAddress(currentPosition, placemarks.first);
          emit(LocationState.loaded(address));
          return;
        }
      }

      emit(const LocationState.error('No address available.'));
    } catch (e) {
      emit(LocationState.error('Error while refreshing location.'));
    }
  }

  AddressModel _createTempAddress(Position pos, Placemark pm) {
    final fullAddress = [
      pm.street,
      pm.subLocality,
      pm.locality,
      pm.administrativeArea,
      pm.postalCode
    ].where((e) => (e ?? '').isNotEmpty).join(', ');

    final addressLine1 = [
      pm.street,
      pm.subLocality,
    ].where((e) => (e ?? '').isNotEmpty).join(', ');

    return AddressModel(
      id: 'temp_${DateTime.now().millisecondsSinceEpoch}',
      fullAddress: fullAddress,
      addressLine1: addressLine1,
      city: pm.locality ?? '',
      state: pm.administrativeArea ?? '',
      pincode: pm.postalCode ?? '',
      latitude: pos.latitude,
      longitude: pos.longitude,
      addressType: 'current',
      isDefault: true,
    );
  }

  /// Reset location state - useful for user logout scenarios
  void resetLocation() {
    add(const LocationEvent.started());
  }

  /// Reload default address - useful when user adds/updates addresses
  void reloadDefaultAddress() {
    add(const LocationEvent.started());
  }

  /// Refresh UI with current selected address
  void refreshUI() {
    add(const LocationEvent.refreshLocation()); // Will use cached data first
  }

  @override
  Future<void> close() {
    // Clean up any location listeners or resources
    // LocationService and AddressService cleanup handled by DI container
    return super.close();
  }
}
