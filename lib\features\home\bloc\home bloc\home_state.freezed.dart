// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'home_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$HomeState {
  bool get isScrolled => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool isScrolled) initial,
    required TResult Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners,
            bool isScrolled)
        loaded,
    required TResult Function(String message, bool isScrolled) error,
    required TResult Function(
            bool isScrolled, String route, Map<String, dynamic> args)
        deepLink,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool isScrolled)? initial,
    TResult? Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners,
            bool isScrolled)?
        loaded,
    TResult? Function(String message, bool isScrolled)? error,
    TResult? Function(bool isScrolled, String route, Map<String, dynamic> args)?
        deepLink,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool isScrolled)? initial,
    TResult Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners,
            bool isScrolled)?
        loaded,
    TResult Function(String message, bool isScrolled)? error,
    TResult Function(bool isScrolled, String route, Map<String, dynamic> args)?
        deepLink,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(HomeInitial value) initial,
    required TResult Function(HomeLoaded value) loaded,
    required TResult Function(HomeError value) error,
    required TResult Function(HomeDeepLink value) deepLink,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(HomeInitial value)? initial,
    TResult? Function(HomeLoaded value)? loaded,
    TResult? Function(HomeError value)? error,
    TResult? Function(HomeDeepLink value)? deepLink,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(HomeInitial value)? initial,
    TResult Function(HomeLoaded value)? loaded,
    TResult Function(HomeError value)? error,
    TResult Function(HomeDeepLink value)? deepLink,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $HomeStateCopyWith<HomeState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HomeStateCopyWith<$Res> {
  factory $HomeStateCopyWith(HomeState value, $Res Function(HomeState) then) =
      _$HomeStateCopyWithImpl<$Res, HomeState>;
  @useResult
  $Res call({bool isScrolled});
}

/// @nodoc
class _$HomeStateCopyWithImpl<$Res, $Val extends HomeState>
    implements $HomeStateCopyWith<$Res> {
  _$HomeStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isScrolled = null,
  }) {
    return _then(_value.copyWith(
      isScrolled: null == isScrolled
          ? _value.isScrolled
          : isScrolled // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$HomeInitialImplCopyWith<$Res>
    implements $HomeStateCopyWith<$Res> {
  factory _$$HomeInitialImplCopyWith(
          _$HomeInitialImpl value, $Res Function(_$HomeInitialImpl) then) =
      __$$HomeInitialImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isScrolled});
}

/// @nodoc
class __$$HomeInitialImplCopyWithImpl<$Res>
    extends _$HomeStateCopyWithImpl<$Res, _$HomeInitialImpl>
    implements _$$HomeInitialImplCopyWith<$Res> {
  __$$HomeInitialImplCopyWithImpl(
      _$HomeInitialImpl _value, $Res Function(_$HomeInitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isScrolled = null,
  }) {
    return _then(_$HomeInitialImpl(
      isScrolled: null == isScrolled
          ? _value.isScrolled
          : isScrolled // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$HomeInitialImpl implements HomeInitial {
  const _$HomeInitialImpl({this.isScrolled = false});

  @override
  @JsonKey()
  final bool isScrolled;

  @override
  String toString() {
    return 'HomeState.initial(isScrolled: $isScrolled)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HomeInitialImpl &&
            (identical(other.isScrolled, isScrolled) ||
                other.isScrolled == isScrolled));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isScrolled);

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HomeInitialImplCopyWith<_$HomeInitialImpl> get copyWith =>
      __$$HomeInitialImplCopyWithImpl<_$HomeInitialImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool isScrolled) initial,
    required TResult Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners,
            bool isScrolled)
        loaded,
    required TResult Function(String message, bool isScrolled) error,
    required TResult Function(
            bool isScrolled, String route, Map<String, dynamic> args)
        deepLink,
  }) {
    return initial(isScrolled);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool isScrolled)? initial,
    TResult? Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners,
            bool isScrolled)?
        loaded,
    TResult? Function(String message, bool isScrolled)? error,
    TResult? Function(bool isScrolled, String route, Map<String, dynamic> args)?
        deepLink,
  }) {
    return initial?.call(isScrolled);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool isScrolled)? initial,
    TResult Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners,
            bool isScrolled)?
        loaded,
    TResult Function(String message, bool isScrolled)? error,
    TResult Function(bool isScrolled, String route, Map<String, dynamic> args)?
        deepLink,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(isScrolled);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(HomeInitial value) initial,
    required TResult Function(HomeLoaded value) loaded,
    required TResult Function(HomeError value) error,
    required TResult Function(HomeDeepLink value) deepLink,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(HomeInitial value)? initial,
    TResult? Function(HomeLoaded value)? loaded,
    TResult? Function(HomeError value)? error,
    TResult? Function(HomeDeepLink value)? deepLink,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(HomeInitial value)? initial,
    TResult Function(HomeLoaded value)? loaded,
    TResult Function(HomeError value)? error,
    TResult Function(HomeDeepLink value)? deepLink,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class HomeInitial implements HomeState {
  const factory HomeInitial({final bool isScrolled}) = _$HomeInitialImpl;

  @override
  bool get isScrolled;

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HomeInitialImplCopyWith<_$HomeInitialImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$HomeLoadedImplCopyWith<$Res>
    implements $HomeStateCopyWith<$Res> {
  factory _$$HomeLoadedImplCopyWith(
          _$HomeLoadedImpl value, $Res Function(_$HomeLoadedImpl) then) =
      __$$HomeLoadedImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<CategoryEntity>? categories,
      List<ProductEntity>? previouslyBought,
      List<ProductEntity>? mostPopular,
      List<ProductEntity>? mostBought,
      List<BannerEntity>? banners,
      bool isScrolled});
}

/// @nodoc
class __$$HomeLoadedImplCopyWithImpl<$Res>
    extends _$HomeStateCopyWithImpl<$Res, _$HomeLoadedImpl>
    implements _$$HomeLoadedImplCopyWith<$Res> {
  __$$HomeLoadedImplCopyWithImpl(
      _$HomeLoadedImpl _value, $Res Function(_$HomeLoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? categories = freezed,
    Object? previouslyBought = freezed,
    Object? mostPopular = freezed,
    Object? mostBought = freezed,
    Object? banners = freezed,
    Object? isScrolled = null,
  }) {
    return _then(_$HomeLoadedImpl(
      categories: freezed == categories
          ? _value._categories
          : categories // ignore: cast_nullable_to_non_nullable
              as List<CategoryEntity>?,
      previouslyBought: freezed == previouslyBought
          ? _value._previouslyBought
          : previouslyBought // ignore: cast_nullable_to_non_nullable
              as List<ProductEntity>?,
      mostPopular: freezed == mostPopular
          ? _value._mostPopular
          : mostPopular // ignore: cast_nullable_to_non_nullable
              as List<ProductEntity>?,
      mostBought: freezed == mostBought
          ? _value._mostBought
          : mostBought // ignore: cast_nullable_to_non_nullable
              as List<ProductEntity>?,
      banners: freezed == banners
          ? _value._banners
          : banners // ignore: cast_nullable_to_non_nullable
              as List<BannerEntity>?,
      isScrolled: null == isScrolled
          ? _value.isScrolled
          : isScrolled // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$HomeLoadedImpl implements HomeLoaded {
  const _$HomeLoadedImpl(
      {required final List<CategoryEntity>? categories,
      required final List<ProductEntity>? previouslyBought,
      required final List<ProductEntity>? mostPopular,
      required final List<ProductEntity>? mostBought,
      required final List<BannerEntity>? banners,
      required this.isScrolled})
      : _categories = categories,
        _previouslyBought = previouslyBought,
        _mostPopular = mostPopular,
        _mostBought = mostBought,
        _banners = banners;

  final List<CategoryEntity>? _categories;
  @override
  List<CategoryEntity>? get categories {
    final value = _categories;
    if (value == null) return null;
    if (_categories is EqualUnmodifiableListView) return _categories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ProductEntity>? _previouslyBought;
  @override
  List<ProductEntity>? get previouslyBought {
    final value = _previouslyBought;
    if (value == null) return null;
    if (_previouslyBought is EqualUnmodifiableListView)
      return _previouslyBought;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ProductEntity>? _mostPopular;
  @override
  List<ProductEntity>? get mostPopular {
    final value = _mostPopular;
    if (value == null) return null;
    if (_mostPopular is EqualUnmodifiableListView) return _mostPopular;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ProductEntity>? _mostBought;
  @override
  List<ProductEntity>? get mostBought {
    final value = _mostBought;
    if (value == null) return null;
    if (_mostBought is EqualUnmodifiableListView) return _mostBought;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<BannerEntity>? _banners;
  @override
  List<BannerEntity>? get banners {
    final value = _banners;
    if (value == null) return null;
    if (_banners is EqualUnmodifiableListView) return _banners;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool isScrolled;

  @override
  String toString() {
    return 'HomeState.loaded(categories: $categories, previouslyBought: $previouslyBought, mostPopular: $mostPopular, mostBought: $mostBought, banners: $banners, isScrolled: $isScrolled)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HomeLoadedImpl &&
            const DeepCollectionEquality()
                .equals(other._categories, _categories) &&
            const DeepCollectionEquality()
                .equals(other._previouslyBought, _previouslyBought) &&
            const DeepCollectionEquality()
                .equals(other._mostPopular, _mostPopular) &&
            const DeepCollectionEquality()
                .equals(other._mostBought, _mostBought) &&
            const DeepCollectionEquality().equals(other._banners, _banners) &&
            (identical(other.isScrolled, isScrolled) ||
                other.isScrolled == isScrolled));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_categories),
      const DeepCollectionEquality().hash(_previouslyBought),
      const DeepCollectionEquality().hash(_mostPopular),
      const DeepCollectionEquality().hash(_mostBought),
      const DeepCollectionEquality().hash(_banners),
      isScrolled);

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HomeLoadedImplCopyWith<_$HomeLoadedImpl> get copyWith =>
      __$$HomeLoadedImplCopyWithImpl<_$HomeLoadedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool isScrolled) initial,
    required TResult Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners,
            bool isScrolled)
        loaded,
    required TResult Function(String message, bool isScrolled) error,
    required TResult Function(
            bool isScrolled, String route, Map<String, dynamic> args)
        deepLink,
  }) {
    return loaded(categories, previouslyBought, mostPopular, mostBought,
        banners, isScrolled);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool isScrolled)? initial,
    TResult? Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners,
            bool isScrolled)?
        loaded,
    TResult? Function(String message, bool isScrolled)? error,
    TResult? Function(bool isScrolled, String route, Map<String, dynamic> args)?
        deepLink,
  }) {
    return loaded?.call(categories, previouslyBought, mostPopular, mostBought,
        banners, isScrolled);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool isScrolled)? initial,
    TResult Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners,
            bool isScrolled)?
        loaded,
    TResult Function(String message, bool isScrolled)? error,
    TResult Function(bool isScrolled, String route, Map<String, dynamic> args)?
        deepLink,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(categories, previouslyBought, mostPopular, mostBought,
          banners, isScrolled);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(HomeInitial value) initial,
    required TResult Function(HomeLoaded value) loaded,
    required TResult Function(HomeError value) error,
    required TResult Function(HomeDeepLink value) deepLink,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(HomeInitial value)? initial,
    TResult? Function(HomeLoaded value)? loaded,
    TResult? Function(HomeError value)? error,
    TResult? Function(HomeDeepLink value)? deepLink,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(HomeInitial value)? initial,
    TResult Function(HomeLoaded value)? loaded,
    TResult Function(HomeError value)? error,
    TResult Function(HomeDeepLink value)? deepLink,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class HomeLoaded implements HomeState {
  const factory HomeLoaded(
      {required final List<CategoryEntity>? categories,
      required final List<ProductEntity>? previouslyBought,
      required final List<ProductEntity>? mostPopular,
      required final List<ProductEntity>? mostBought,
      required final List<BannerEntity>? banners,
      required final bool isScrolled}) = _$HomeLoadedImpl;

  List<CategoryEntity>? get categories;
  List<ProductEntity>? get previouslyBought;
  List<ProductEntity>? get mostPopular;
  List<ProductEntity>? get mostBought;
  List<BannerEntity>? get banners;
  @override
  bool get isScrolled;

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HomeLoadedImplCopyWith<_$HomeLoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$HomeErrorImplCopyWith<$Res>
    implements $HomeStateCopyWith<$Res> {
  factory _$$HomeErrorImplCopyWith(
          _$HomeErrorImpl value, $Res Function(_$HomeErrorImpl) then) =
      __$$HomeErrorImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, bool isScrolled});
}

/// @nodoc
class __$$HomeErrorImplCopyWithImpl<$Res>
    extends _$HomeStateCopyWithImpl<$Res, _$HomeErrorImpl>
    implements _$$HomeErrorImplCopyWith<$Res> {
  __$$HomeErrorImplCopyWithImpl(
      _$HomeErrorImpl _value, $Res Function(_$HomeErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? isScrolled = null,
  }) {
    return _then(_$HomeErrorImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      isScrolled: null == isScrolled
          ? _value.isScrolled
          : isScrolled // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$HomeErrorImpl implements HomeError {
  const _$HomeErrorImpl({required this.message, required this.isScrolled});

  @override
  final String message;
  @override
  final bool isScrolled;

  @override
  String toString() {
    return 'HomeState.error(message: $message, isScrolled: $isScrolled)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HomeErrorImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.isScrolled, isScrolled) ||
                other.isScrolled == isScrolled));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, isScrolled);

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HomeErrorImplCopyWith<_$HomeErrorImpl> get copyWith =>
      __$$HomeErrorImplCopyWithImpl<_$HomeErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool isScrolled) initial,
    required TResult Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners,
            bool isScrolled)
        loaded,
    required TResult Function(String message, bool isScrolled) error,
    required TResult Function(
            bool isScrolled, String route, Map<String, dynamic> args)
        deepLink,
  }) {
    return error(message, isScrolled);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool isScrolled)? initial,
    TResult? Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners,
            bool isScrolled)?
        loaded,
    TResult? Function(String message, bool isScrolled)? error,
    TResult? Function(bool isScrolled, String route, Map<String, dynamic> args)?
        deepLink,
  }) {
    return error?.call(message, isScrolled);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool isScrolled)? initial,
    TResult Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners,
            bool isScrolled)?
        loaded,
    TResult Function(String message, bool isScrolled)? error,
    TResult Function(bool isScrolled, String route, Map<String, dynamic> args)?
        deepLink,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message, isScrolled);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(HomeInitial value) initial,
    required TResult Function(HomeLoaded value) loaded,
    required TResult Function(HomeError value) error,
    required TResult Function(HomeDeepLink value) deepLink,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(HomeInitial value)? initial,
    TResult? Function(HomeLoaded value)? loaded,
    TResult? Function(HomeError value)? error,
    TResult? Function(HomeDeepLink value)? deepLink,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(HomeInitial value)? initial,
    TResult Function(HomeLoaded value)? loaded,
    TResult Function(HomeError value)? error,
    TResult Function(HomeDeepLink value)? deepLink,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class HomeError implements HomeState {
  const factory HomeError(
      {required final String message,
      required final bool isScrolled}) = _$HomeErrorImpl;

  String get message;
  @override
  bool get isScrolled;

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HomeErrorImplCopyWith<_$HomeErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$HomeDeepLinkImplCopyWith<$Res>
    implements $HomeStateCopyWith<$Res> {
  factory _$$HomeDeepLinkImplCopyWith(
          _$HomeDeepLinkImpl value, $Res Function(_$HomeDeepLinkImpl) then) =
      __$$HomeDeepLinkImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isScrolled, String route, Map<String, dynamic> args});
}

/// @nodoc
class __$$HomeDeepLinkImplCopyWithImpl<$Res>
    extends _$HomeStateCopyWithImpl<$Res, _$HomeDeepLinkImpl>
    implements _$$HomeDeepLinkImplCopyWith<$Res> {
  __$$HomeDeepLinkImplCopyWithImpl(
      _$HomeDeepLinkImpl _value, $Res Function(_$HomeDeepLinkImpl) _then)
      : super(_value, _then);

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isScrolled = null,
    Object? route = null,
    Object? args = null,
  }) {
    return _then(_$HomeDeepLinkImpl(
      isScrolled: null == isScrolled
          ? _value.isScrolled
          : isScrolled // ignore: cast_nullable_to_non_nullable
              as bool,
      route: null == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String,
      args: null == args
          ? _value._args
          : args // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc

class _$HomeDeepLinkImpl implements HomeDeepLink {
  const _$HomeDeepLinkImpl(
      {required this.isScrolled,
      required this.route,
      final Map<String, dynamic> args = const {}})
      : _args = args;

  @override
  final bool isScrolled;
  @override
  final String route;
  final Map<String, dynamic> _args;
  @override
  @JsonKey()
  Map<String, dynamic> get args {
    if (_args is EqualUnmodifiableMapView) return _args;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_args);
  }

  @override
  String toString() {
    return 'HomeState.deepLink(isScrolled: $isScrolled, route: $route, args: $args)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HomeDeepLinkImpl &&
            (identical(other.isScrolled, isScrolled) ||
                other.isScrolled == isScrolled) &&
            (identical(other.route, route) || other.route == route) &&
            const DeepCollectionEquality().equals(other._args, _args));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isScrolled, route,
      const DeepCollectionEquality().hash(_args));

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HomeDeepLinkImplCopyWith<_$HomeDeepLinkImpl> get copyWith =>
      __$$HomeDeepLinkImplCopyWithImpl<_$HomeDeepLinkImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool isScrolled) initial,
    required TResult Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners,
            bool isScrolled)
        loaded,
    required TResult Function(String message, bool isScrolled) error,
    required TResult Function(
            bool isScrolled, String route, Map<String, dynamic> args)
        deepLink,
  }) {
    return deepLink(isScrolled, route, args);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool isScrolled)? initial,
    TResult? Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners,
            bool isScrolled)?
        loaded,
    TResult? Function(String message, bool isScrolled)? error,
    TResult? Function(bool isScrolled, String route, Map<String, dynamic> args)?
        deepLink,
  }) {
    return deepLink?.call(isScrolled, route, args);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool isScrolled)? initial,
    TResult Function(
            List<CategoryEntity>? categories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners,
            bool isScrolled)?
        loaded,
    TResult Function(String message, bool isScrolled)? error,
    TResult Function(bool isScrolled, String route, Map<String, dynamic> args)?
        deepLink,
    required TResult orElse(),
  }) {
    if (deepLink != null) {
      return deepLink(isScrolled, route, args);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(HomeInitial value) initial,
    required TResult Function(HomeLoaded value) loaded,
    required TResult Function(HomeError value) error,
    required TResult Function(HomeDeepLink value) deepLink,
  }) {
    return deepLink(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(HomeInitial value)? initial,
    TResult? Function(HomeLoaded value)? loaded,
    TResult? Function(HomeError value)? error,
    TResult? Function(HomeDeepLink value)? deepLink,
  }) {
    return deepLink?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(HomeInitial value)? initial,
    TResult Function(HomeLoaded value)? loaded,
    TResult Function(HomeError value)? error,
    TResult Function(HomeDeepLink value)? deepLink,
    required TResult orElse(),
  }) {
    if (deepLink != null) {
      return deepLink(this);
    }
    return orElse();
  }
}

abstract class HomeDeepLink implements HomeState {
  const factory HomeDeepLink(
      {required final bool isScrolled,
      required final String route,
      final Map<String, dynamic> args}) = _$HomeDeepLinkImpl;

  @override
  bool get isScrolled;
  String get route;
  Map<String, dynamic> get args;

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HomeDeepLinkImplCopyWith<_$HomeDeepLinkImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
