import 'package:rozana/data/models/adress_model.dart';
import 'package:rozana/data/models/order_item_model.dart';
import 'package:rozana/data/models/order_timeline_model.dart';

class OrderModel {
  String? id;
  String? customerId;
  String? customerName;
  String? facilityId;
  String? facilityName;
  String? status;
  String? orderDate;
  String? deliveryDate;
  String? estimatedDeliveryTime;
  num? totalAmount;
  num? subtotal;
  num? tax;
  num? deliveryFee;
  num? discount;
  String? paymentMethod;
  AddressModel? deliveryAddress;
  List<OrderItemModel>? items;
  List<OrderTimelineModel>? orderTimeline;
  bool? canCancel;
  bool? canReorder;
  String? objectType;

  OrderModel({
    this.id,
    this.customerId,
    this.customerName,
    this.facilityId,
    this.facilityName,
    this.status,
    this.orderDate,
    this.deliveryDate,
    this.estimatedDeliveryTime,
    this.totalAmount,
    this.subtotal,
    this.tax,
    this.deliveryFee,
    this.discount,
    this.paymentMethod,
    this.deliveryAddress,
    this.items,
    this.orderTimeline,
    this.canCancel,
    this.canReorder,
    this.objectType,
  });

  OrderModel.fromJson(Map<String, dynamic> json) {
    // Map API response fields to model fields
    id = json['order_id'] ?? json['id'];
    customerId = json['customer_id'] ?? json['customerId'];
    customerName = json['customer_name'] ?? json['customerName'];
    facilityId = json['facility_id'] ?? json['facilityId'];
    facilityName = json['facility_name'] ?? json['facilityName'];
    status = json['status'];
    orderDate = json['created_at'] ?? json['orderDate'];
    deliveryDate = json['deliveryDate'];
    estimatedDeliveryTime = json['eta'] ?? json['estimatedDeliveryTime'];

    // Handle total_amount from API or totalAmount from existing data
    final totalAmountValue = json['total_amount'] ?? json['totalAmount'];
    if (totalAmountValue is num) {
      totalAmount = totalAmountValue;
    } else if (totalAmountValue != null) {
      var numb = num.tryParse(totalAmountValue.toString());
      if (numb is num) {
        totalAmount = numb;
      }
    }

    if (json['subtotal'] is num) {
      subtotal = json['subtotal'];
    } else if (json['subtotal'] != null) {
      var numb = num.tryParse(json['subtotal']!.toString());
      if (numb is num) {
        subtotal = numb;
      }
    } else {
      // Default subtotal to total amount if not provided
      subtotal = totalAmount;
    }

    if (json['tax'] is num) {
      tax = json['tax'];
    } else if (json['tax'] != null) {
      var numb = num.tryParse(json['tax']!.toString());
      if (numb is num) {
        tax = numb;
      }
    } else {
      // Default tax to 0 if not provided
      tax = 0.0;
    }

    if (json['deliveryFee'] is num) {
      deliveryFee = json['deliveryFee'];
    } else if (json['deliveryFee'] != null) {
      var numb = num.tryParse(json['deliveryFee']!.toString());
      if (numb is num) {
        deliveryFee = numb;
      }
    } else {
      // Default delivery fee to 0 if not provided
      deliveryFee = 0.0;
    }

    if (json['discount'] is num) {
      discount = json['discount'];
    } else if (json['discount'] != null) {
      var numb = num.tryParse(json['discount']!.toString());
      if (numb is num) {
        discount = numb;
      }
    } else {
      // Default discount to 0 if not provided
      discount = 0.0;
    }

    paymentMethod = json['paymentMethod'] ?? 'Cash on Delivery';

    if (json['deliveryAddress'] != null) {
      deliveryAddress = AddressModel.fromJson(json['deliveryAddress']);
    }

    if (json['items'] != null) {
      items = <OrderItemModel>[];
      json['items'].forEach((v) {
        items!.add(OrderItemModel.fromJson(v));
      });
    }

    if (json['orderTimeline'] != null) {
      orderTimeline = <OrderTimelineModel>[];
      json['orderTimeline'].forEach((v) {
        orderTimeline!.add(OrderTimelineModel.fromJson(v));
      });
    } else {
      // Create default timeline based on current status
      orderTimeline = _createDefaultTimeline(status ?? 'pending');
    }

    // Set default values for canCancel and canReorder based on status
    canCancel =
        json['canCancel'] ?? (status == 'pending' || status == 'confirmed');
    canReorder =
        json['canReorder'] ?? (status == 'delivered' || status == 'cancelled');
    objectType = json['objectType'];
  }

  /// Create default timeline based on order status
  List<OrderTimelineModel> _createDefaultTimeline(String status) {
    final timeline = <OrderTimelineModel>[];
    final now = DateTime.now();

    // Always add order placed
    timeline.add(OrderTimelineModel(
      status: 'placed',
      timestamp: orderDate?.toString() ?? now.toString(),
      title: 'Order Placed',
      description: 'Your order has been placed successfully',
    ));

    // Add additional timeline entries based on current status
    if (status == 'confirmed' ||
        status == 'preparing' ||
        status == 'out_for_delivery' ||
        status == 'delivered') {
      timeline.add(OrderTimelineModel(
        status: 'confirmed',
        timestamp: now.toString(),
        title: 'Order Confirmed',
        description: 'Your order has been confirmed',
      ));
    }

    if (status == 'preparing' ||
        status == 'out_for_delivery' ||
        status == 'delivered') {
      timeline.add(OrderTimelineModel(
        status: 'preparing',
        timestamp: now.toString(),
        title: 'Preparing',
        description: 'Your order is being prepared',
      ));
    }

    if (status == 'out_for_delivery' || status == 'delivered') {
      timeline.add(OrderTimelineModel(
        status: 'out_for_delivery',
        timestamp: now.toString(),
        title: 'Out for Delivery',
        description: 'Your order is out for delivery',
      ));
    }

    if (status == 'delivered') {
      timeline.add(OrderTimelineModel(
        status: 'delivered',
        timestamp: now.toString(),
        title: 'Delivered',
        description: 'Your order has been delivered',
      ));
    }

    if (status == 'cancelled') {
      timeline.add(OrderTimelineModel(
        status: 'cancelled',
        timestamp: now.toString(),
        title: 'Cancelled',
        description: 'Your order has been cancelled',
      ));
    }

    return timeline;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['customerId'] = customerId;
    data['customerName'] = customerName;
    data['facilityId'] = facilityId;
    data['facilityName'] = facilityName;
    data['status'] = status;
    data['orderDate'] = orderDate;
    data['deliveryDate'] = deliveryDate;
    data['estimatedDeliveryTime'] = estimatedDeliveryTime;
    data['totalAmount'] = totalAmount;
    data['subtotal'] = subtotal;
    data['tax'] = tax;
    data['deliveryFee'] = deliveryFee;
    data['discount'] = discount;
    data['paymentMethod'] = paymentMethod;

    if (deliveryAddress != null) {
      data['deliveryAddress'] = deliveryAddress!.toJson();
    }

    if (items != null) {
      data['items'] = items!.map((v) => v.toJson()).toList();
    }

    if (orderTimeline != null) {
      data['orderTimeline'] = orderTimeline!.map((v) => v.toJson()).toList();
    }

    data['canCancel'] = canCancel;
    data['canReorder'] = canReorder;
    data['objectType'] = objectType;
    return data;
  }

  /// Get DateTime from orderDate string
  DateTime? get orderDateTime {
    if (orderDate == null) return null;
    try {
      return DateTime.parse(orderDate!);
    } catch (e) {
      return null;
    }
  }

  /// Get DateTime from deliveryDate string
  DateTime? get deliveryDateTime {
    if (deliveryDate == null) return null;
    try {
      return DateTime.parse(deliveryDate!);
    } catch (e) {
      return null;
    }
  }

  /// Get total number of items in the order
  int get totalItems =>
      items?.fold<int>(0, (sum, item) => sum + (item.quantity?.toInt() ?? 0)) ??
      0;

  /// Check if order is in progress (can be tracked)
  bool get isInProgress {
    return status == 'pending' ||
        status == 'confirmed' ||
        status == 'preparing' ||
        status == 'out_for_delivery';
  }

  /// Check if order is completed
  bool get isCompleted => status == 'delivered';

  /// Check if order is cancelled
  bool get isCancelled => status == 'cancelled';
}
