import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/widgets/custom_text.dart';
import 'package:rozana/widgets/custom_button.dart';
import 'package:rozana/widgets/lazy_loading_widget.dart';
import 'package:rozana/domain/entities/order_entity.dart';
import '../../bloc/order_bloc.dart';
import '../widgets/order_card.dart';

class OrderHistoryScreen extends StatefulWidget {
  const OrderHistoryScreen({super.key});

  @override
  State<OrderHistoryScreen> createState() => _OrderHistoryScreenState();
}

class _OrderHistoryScreenState extends State<OrderHistoryScreen> {
  final ScrollController _scrollController = ScrollController();
  String _selectedFilter = '';

  @override
  void initState() {
    super.initState();
    // Initialize order history
    context.read<OrderBloc>().add(const OrderEvent.init());

    // Setup scroll listener for pagination
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent * 0.8) {
      final state = context.read<OrderBloc>().state;
      if (state.hasMoreData && !state.isLoadingMore) {
        context.read<OrderBloc>().add(const OrderEvent.loadMoreOrders());
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const CustomText(
          'Order History',
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary,
        ),
        backgroundColor: AppColors.surface,
        elevation: 1,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.textPrimary),
          onPressed: () => context.pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search, color: AppColors.textPrimary),
            onPressed: () => _showSearchDialog(),
          ),
        ],
      ),
      body: Column(
        children: [
          // Filter tabs
          _buildFilterTabs(),

          // Order list
          Expanded(
            child: BlocConsumer<OrderBloc, OrderState>(
              listener: (context, state) {
                state.maybeWhen(
                  error: (message, orderId) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(message),
                        backgroundColor: AppColors.error,
                      ),
                    );
                  },
                  orderCancelled: (orderId, message) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(message),
                        backgroundColor: AppColors.success,
                      ),
                    );
                  },
                  orElse: () {},
                );
              },
              builder: (context, state) {
                return state.when(
                  initial: () => const Center(
                    child: CircularProgressIndicator(),
                  ),
                  loading: () => const Center(
                    child: CircularProgressIndicator(),
                  ),
                  orderHistoryLoaded: (orders, isLoadingMore, hasMoreData,
                      currentPage, currentFilter, searchQuery) {
                    return RefreshIndicator(
                      onRefresh: () async {
                        context.read<OrderBloc>().add(
                              OrderEvent.refreshOrderHistory(
                                  status: _selectedFilter),
                            );
                      },
                      child: LazyLoadingWidget<OrderEntity>(
                        items: orders,
                        isLoading: false,
                        hasMoreData: hasMoreData,
                        onLoadMore: hasMoreData && !isLoadingMore
                            ? () => context.read<OrderBloc>().add(
                                  const OrderEvent.loadMoreOrders(),
                                )
                            : null,
                        scrollController: _scrollController,
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        itemBuilder: (context, order, index) {
                          return OrderCard(
                            order: order,
                            onTap: () => _navigateToOrderDetails(order.id),
                            onReorder: order.canReorder
                                ? () => _handleReorder(order.id)
                                : null,
                            onCancel: order.canCancel
                                ? () => _handleCancelOrder(order.id)
                                : null,
                          );
                        },
                        loadingBuilder: (context) => const Center(
                          child: Padding(
                            padding: EdgeInsets.all(16),
                            child: CircularProgressIndicator(),
                          ),
                        ),
                        emptyBuilder: (context) => _buildEmptyState(),
                      ),
                    );
                  },
                  empty: (filter, searchQuery) => _buildEmptyState(),
                  error: (message, orderId) => _buildErrorState(message),
                  orderDetailsLoaded: (_) => const SizedBox.shrink(),
                  orderCancelled: (_, __) => const SizedBox.shrink(),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterTabs() {
    final filters = [
      {'key': '', 'label': 'All'},
      {'key': 'pending', 'label': 'Pending'},
      {'key': 'out_for_delivery', 'label': 'Active'},
      {'key': 'delivered', 'label': 'Delivered'},
      {'key': 'cancelled', 'label': 'Cancelled'},
    ];

    return Container(
      height: 60,
      color: AppColors.surface,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        itemCount: filters.length,
        itemBuilder: (context, index) {
          final filter = filters[index];
          final isSelected = _selectedFilter == filter['key'];

          return Padding(
            padding: const EdgeInsets.only(right: 12),
            child: FilterChip(
              label: CustomText(
                filter['label']!,
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: isSelected ? AppColors.surface : AppColors.textPrimary,
              ),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedFilter = selected ? filter['key']! : '';
                });
                context.read<OrderBloc>().add(
                      OrderEvent.filterOrdersByStatus(_selectedFilter),
                    );
              },
              backgroundColor: AppColors.background,
              selectedColor: AppColors.primary,
              checkmarkColor: AppColors.surface,
              side: BorderSide(
                color: isSelected ? AppColors.primary : AppColors.textHint,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.shopping_bag_outlined,
              size: 80,
              color: AppColors.textHint,
            ),
            const SizedBox(height: 16),
            const CustomText(
              'No Orders Found',
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
            const SizedBox(height: 8),
            CustomText(
              _selectedFilter.isEmpty
                  ? 'You haven\'t placed any orders yet'
                  : 'No orders found for the selected filter',
              fontSize: 14,
              color: AppColors.textSecondary,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            AppButton(
              text: 'Start Shopping',
              onPressed: () => context.go('/'),
              backgroundColor: AppColors.primary,
              textColor: AppColors.surface,
              borderRadius: 8,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80,
              color: AppColors.error,
            ),
            const SizedBox(height: 16),
            const CustomText(
              'Something went wrong',
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
            const SizedBox(height: 8),
            CustomText(
              message,
              fontSize: 14,
              color: AppColors.textSecondary,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            AppButton(
              text: 'Try Again',
              onPressed: () => context.read<OrderBloc>().add(
                    const OrderEvent.refreshOrderHistory(),
                  ),
              backgroundColor: AppColors.primary,
              textColor: AppColors.surface,
              borderRadius: 8,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToOrderDetails(String orderId) {
    context.push('/orders/$orderId');
  }

  void _handleReorder(String orderId) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Reorder functionality will be implemented'),
      ),
    );
  }

  void _handleCancelOrder(String orderId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const CustomText(
          'Cancel Order',
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
        content: const CustomText(
          'Are you sure you want to cancel this order?',
          fontSize: 14,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: CustomText(
              'No',
              color: AppColors.textSecondary,
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<OrderBloc>().add(OrderEvent.cancelOrder(orderId));
            },
            child: CustomText(
              'Yes, Cancel',
              color: AppColors.error,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) {
        String searchQuery = '';
        return AlertDialog(
          title: const CustomText('Search Orders'),
          content: TextField(
            onChanged: (value) => searchQuery = value,
            decoration: const InputDecoration(
              hintText: 'Enter order ID or product name',
              border: OutlineInputBorder(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const CustomText('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                if (searchQuery.isNotEmpty) {
                  context.read<OrderBloc>().add(
                        OrderEvent.searchOrders(searchQuery),
                      );
                }
              },
              child: const CustomText('Search'),
            ),
          ],
        );
      },
    );
  }
}
