# Environment Configuration Example
# Copy this file to .env.local and fill in your actual values
# NEVER commit actual secrets to version control

# Environment Type (development, staging, production)
ENVIRONMENT=development

# API Configuration - Dual Service Support
# OMS (Order Management System) URLs
OMS_API_BASE_URL=https://your-oms-api-domain.com
DEV_OMS_API_URL=https://oms-dev.your-domain.com
STAGING_OMS_API_URL=https://oms-staging.your-domain.com
PROD_OMS_API_URL=https://oms.your-domain.com

# IMS (Inventory Management System) URLs
IMS_API_BASE_URL=https://your-ims-api-domain.com
DEV_IMS_API_URL=https://ims-dev.your-domain.com
STAGING_IMS_API_URL=https://ims-staging.your-domain.com
PROD_IMS_API_URL=https://ims.your-domain.com

# Legacy API Configuration (deprecated - use OMS/IMS specific URLs above)
API_BASE_URL=https://your-api-domain.com
DEV_API_URL=https://dev-api.your-domain.com
STAGING_API_URL=https://staging-api.your-domain.com
PROD_API_URL=https://api.your-domain.com

# Firebase Configuration
FIREBASE_API_KEY=your-firebase-api-key
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
FIREBASE_STORAGE_BUCKET=your-project.firebasestorage.app
FIREBASE_MESSAGING_SENDER_ID=your-sender-id
FIREBASE_APP_ID=your-app-id
FIREBASE_MEASUREMENT_ID=your-measurement-id

# Typesense Configuration
TYPESENSE_API_KEY=your-typesense-api-key
TYPESENSE_HOST=your-typesense-host.com
TYPESENSE_PORT=8443
TYPESENSE_PROTOCOL=https

# Analytics Configuration
AMPLITUDE_API_KEY=your-amplitude-api-key

# Branch SDK Configuration
BRANCH_LOGGING_ENABLED=false

# App Configuration
APP_NAME=Your App Name
APP_VERSION=1.0.0

# Debug Configuration
ENABLE_LOGGING=false
