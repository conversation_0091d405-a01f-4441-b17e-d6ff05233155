import 'package:dio/dio.dart';
import '../entities/order_entity.dart';

abstract class OrderRepositoryInterface {
  /// Creates a new order with the provided data
  ///
  /// Returns the API response if successful, or throws an exception if failed
  Future<Response?> createOrder({
    required String customerId,
    required String customerName,
    required String facilityId,
    required String facilityName,
    required num totalAmount,
    required List<Map<String, dynamic>> items,
  });

  /// Get order history for a customer with pagination and filtering
  Future<List<OrderEntity>> getOrderHistory({
    String customerId = 'customer_123',
    String status = '',
    int page = 0,
    int pageSize = 10,
    bool refresh = false,
  });

  /// Get detailed information for a specific order
  Future<OrderEntity?> getOrderDetails(String orderId);

  /// Cancel an order
  Future<bool> cancelOrder(String orderId);
}
