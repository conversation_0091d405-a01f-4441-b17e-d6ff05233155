import 'package:flutter/foundation.dart';

/// Environment types
enum Environment {
  development,
  staging,
  production,
}

/// Secure environment configuration using compile-time constants
class EnvironmentConfig {
  /// Current environment - determined at compile time
  static Environment get environment {
    const envString =
        String.fromEnvironment('ENVIRONMENT', defaultValue: 'development');
    switch (envString.toLowerCase()) {
      case 'production':
      case 'prod':
        return Environment.production;
      case 'staging':
      case 'stage':
        return Environment.staging;
      default:
        return Environment.development;
    }
  }

  /// API Configuration - Support for both OMS and IMS services
  /// OMS (Order Management System) Base URL
  static String get omsBaseUrl {
    const url = String.fromEnvironment('OMS_API_BASE_URL');
    if (url.isNotEmpty) return url;

    // OMS URLs for different environments
    switch (environment) {
      case Environment.production:
        return const String.fromEnvironment('PROD_OMS_API_URL',
            defaultValue: 'https://oms-dev.rozana.tech/');
      case Environment.staging:
        return const String.fromEnvironment('STAGING_OMS_API_URL',
            defaultValue: 'https://oms-dev.rozana.tech/');
      case Environment.development:
        return const String.fromEnvironment('DEV_OMS_API_URL',
            defaultValue: 'https://oms-dev.rozana.tech/');
    }
  }

  /// IMS (Inventory Management System) Base URL
  static String get imsBaseUrl {
    const url = String.fromEnvironment('IMS_API_BASE_URL');
    if (url.isNotEmpty) return url;

    // IMS URLs for different environments
    switch (environment) {
      case Environment.production:
        return const String.fromEnvironment('PROD_IMS_API_URL',
            defaultValue: 'https://ims-dev.rozana.tech/');
      case Environment.staging:
        return const String.fromEnvironment('STAGING_IMS_API_URL',
            defaultValue: 'https://ims-dev.rozana.tech/');
      case Environment.development:
        return const String.fromEnvironment('DEV_IMS_API_URL',
            defaultValue: 'https://ims-dev.rozana.tech/');
    }
  }

  /// Legacy API Base URL - defaults to OMS for backward compatibility
  @Deprecated('Use omsBaseUrl or imsBaseUrl instead for clarity')
  static String get apiBaseUrl => omsBaseUrl;

  /// Get base URL for specific service
  /// [isIms] - true for IMS service, false for OMS service
  static String getServiceBaseUrl({required bool isIms}) {
    return isIms ? imsBaseUrl : omsBaseUrl;
  }

  /// Firebase Configuration
  static String get firebaseApiKey {
    const key = String.fromEnvironment('FIREBASE_API_KEY');
    return key.isNotEmpty ? key : _getDefaultFirebaseApiKey();
  }

  static String get firebaseProjectId {
    const projectId = String.fromEnvironment('FIREBASE_PROJECT_ID');
    return projectId.isNotEmpty ? projectId : _getDefaultFirebaseProjectId();
  }

  static String get firebaseAuthDomain {
    const domain = String.fromEnvironment('FIREBASE_AUTH_DOMAIN');
    return domain.isNotEmpty ? domain : '$firebaseProjectId.firebaseapp.com';
  }

  static String get firebaseStorageBucket {
    const bucket = String.fromEnvironment('FIREBASE_STORAGE_BUCKET');
    return bucket.isNotEmpty
        ? bucket
        : '$firebaseProjectId.firebasestorage.app';
  }

  static String get firebaseMessagingSenderId {
    const senderId = String.fromEnvironment('FIREBASE_MESSAGING_SENDER_ID');
    return senderId.isNotEmpty ? senderId : _getDefaultMessagingSenderId();
  }

  static String get firebaseAppId {
    const appId = String.fromEnvironment('FIREBASE_APP_ID');
    return appId.isNotEmpty ? appId : _getDefaultFirebaseAppId();
  }

  static String get firebaseMeasurementId {
    const measurementId = String.fromEnvironment('FIREBASE_MEASUREMENT_ID');
    return measurementId.isNotEmpty
        ? measurementId
        : _getDefaultMeasurementId();
  }

  /// Typesense Configuration
  static String get typesenseApiKey {
    const key = String.fromEnvironment('TYPESENSE_API_KEY');
    return key.isNotEmpty ? key : _getDefaultTypesenseApiKey();
  }

  static String get typesenseHost {
    const host = String.fromEnvironment('TYPESENSE_HOST');
    return host.isNotEmpty ? host : _getDefaultTypesenseHost();
  }

  static String get typesensePort {
    const port = String.fromEnvironment('TYPESENSE_PORT', defaultValue: '8443');
    return port;
  }

  static String get typesenseProtocol {
    const protocol =
        String.fromEnvironment('TYPESENSE_PROTOCOL', defaultValue: 'https');
    return protocol;
  }

  /// Analytics Configuration
  static String get amplitudeApiKey {
    const key = String.fromEnvironment('AMPLITUDE_API_KEY');
    return key.isNotEmpty ? key : _getDefaultAmplitudeApiKey();
  }

  /// Branch SDK Configuration
  static bool get branchLoggingEnabled {
    const enabled =
        String.fromEnvironment('BRANCH_LOGGING_ENABLED', defaultValue: 'false');
    return enabled.toLowerCase() == 'true' ||
        environment != Environment.production;
  }

  /// App Configuration
  static String get appName {
    const name = String.fromEnvironment('APP_NAME');
    return name.isNotEmpty ? name : _getDefaultAppName();
  }

  static String get appVersion {
    const version =
        String.fromEnvironment('APP_VERSION', defaultValue: '1.0.0');
    return version;
  }

  /// Debug Configuration
  static bool get isDebugMode {
    return kDebugMode || environment == Environment.development;
  }

  static bool get enableLogging {
    const enabled = String.fromEnvironment('ENABLE_LOGGING');
    return enabled.isNotEmpty
        ? enabled.toLowerCase() == 'true'
        : environment != Environment.production;
  }

  // Google Maps Configuration
  static String get googlePlacesApiKey {
    const key = String.fromEnvironment('GOOGLE_PLACES_API_KEY');
    return key.isNotEmpty ? key : _getDefaultGooglePlacesApiKey();
  }

  static String get googleMapsBaseUrl {
    return _getDefaultGoogleMapsBaseUrl();
  }

  // Private helper methods for default values (only for development/staging)
  static String _getDefaultFirebaseApiKey() {
    switch (environment) {
      case Environment.production:
        return 'AIzaSyBEEWROSUbSLVl9T3HHSp8VdgsU6oTrdFI';
      case Environment.staging:
        return 'AIzaSyBEEWROSUbSLVl9T3HHSp8VdgsU6oTrdFI';
      case Environment.development:
        return 'AIzaSyBEEWROSUbSLVl9T3HHSp8VdgsU6oTrdFI';
    }
  }

  static String _getDefaultFirebaseProjectId() {
    switch (environment) {
      case Environment.production:
        return 'rozana-app-customer-b2c';
      case Environment.staging:
        return 'rozana-app-customer-b2c';
      case Environment.development:
        return 'rozana-app-customer-b2c';
    }
  }

  static String _getDefaultMessagingSenderId() {
    switch (environment) {
      case Environment.production:
        return '211080965257';
      case Environment.staging:
        return '211080965257';
      case Environment.development:
        return '211080965257';
    }
  }

  static String _getDefaultFirebaseAppId() {
    switch (environment) {
      case Environment.production:
        return '1:211080965257:web:3b917158271e9aa422aa58';
      case Environment.staging:
        return '1:211080965257:web:3b917158271e9aa422aa58';
      case Environment.development:
        return '1:211080965257:web:3b917158271e9aa422aa58';
    }
  }

  static String _getDefaultMeasurementId() {
    switch (environment) {
      case Environment.production:
        return 'G-QQS02K1LXM';
      case Environment.staging:
        return 'G-QQS02K1LXM';
      case Environment.development:
        return 'G-QQS02K1LXM';
    }
  }

  static String _getDefaultTypesenseApiKey() {
    switch (environment) {
      case Environment.production:
        return 'WzQSB1f8gpXWmsQzRjvEO149v04bfaJa';
      case Environment.staging:
        return 'WzQSB1f8gpXWmsQzRjvEO149v04bfaJa';
      case Environment.development:
        return 'WzQSB1f8gpXWmsQzRjvEO149v04bfaJa';
    }
  }

  static String _getDefaultTypesenseHost() {
    switch (environment) {
      case Environment.production:
        return 'rozanats.headrun.com';
      case Environment.staging:
        return 'rozanats.headrun.com';
      case Environment.development:
        return 'rozanats.headrun.com';
    }
  }

  static String _getDefaultAmplitudeApiKey() {
    switch (environment) {
      case Environment.production:
        return 'fa4004be40bfd5c03e30e6725351c87e';
      case Environment.staging:
        return 'fa4004be40bfd5c03e30e6725351c87e';
      case Environment.development:
        return 'fa4004be40bfd5c03e30e6725351c87e';
    }
  }

  static String _getDefaultAppName() {
    switch (environment) {
      case Environment.production:
        return 'Rozana';
      case Environment.staging:
        return 'Rozana';
      case Environment.development:
        return 'Rozana';
    }
  }

  static String _getDefaultGooglePlacesApiKey() {
    switch (environment) {
      case Environment.production:
        return 'AIzaSyBjTYiNcd5RJTvDqWBP6WpW2OuLyjBYSro';
      case Environment.staging:
        return 'AIzaSyBjTYiNcd5RJTvDqWBP6WpW2OuLyjBYSro';
      case Environment.development:
        return 'AIzaSyBjTYiNcd5RJTvDqWBP6WpW2OuLyjBYSro';
    }
  }

  static String _getDefaultGoogleMapsBaseUrl() {
    return 'https://maps.googleapis.com/maps/api/place/autocomplete/json';
  }

  /// Validation method to ensure all required configs are present
  /// Note: Currently using same configuration for all environments
  static void validateConfiguration() {
    // Configuration validation - currently all environments use same values
    // This method is kept for future when different environment configs are needed

    final requiredConfigs = [
      ('FIREBASE_API_KEY', firebaseApiKey),
      ('TYPESENSE_API_KEY', typesenseApiKey),
      ('AMPLITUDE_API_KEY', amplitudeApiKey),
      ('OMS_API_BASE_URL', omsBaseUrl),
      ('IMS_API_BASE_URL', imsBaseUrl),
      ('GOOGLE_PLACES_API_KEY', googlePlacesApiKey),
    ];

    for (final (name, value) in requiredConfigs) {
      if (value.isEmpty) {
        throw Exception('$name configuration is missing');
      }
    }
  }
}
