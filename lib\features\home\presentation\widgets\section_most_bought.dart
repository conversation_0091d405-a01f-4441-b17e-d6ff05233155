import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/core/utils/app_dimensions.dart';
import 'package:rozana/widgets/viewall_category_title.dart';

import '../../../../domain/entities/product_entity.dart';
import '../../bloc/home bloc/home_bloc.dart';
import 'product_grid.dart';

class MostBoughtSection extends StatelessWidget {
  const MostBoughtSection({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      buildWhen: (previous, current) {
        if (previous is! HomeLoaded) {
          return true;
        }
        if (current is HomeLoaded) {
          return previous.mostBought != current.mostBought;
        }
        return false; // Don’t rebuild for other transitions
      },
      builder: (context, state) {
        List<ProductEntity>? mostBought =
            state.mapOrNull(loaded: (value) => value.mostBought);
        return Visibility(
          visible: mostBought != null,
          replacement: Column(
            children: [
              ProductGrid(productList: null),
            ],
          ),
          child: Visibility(
              visible: mostBought?.isNotEmpty ?? false,
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.only(
                        left: AppDimensions.screenHzPadding,
                        right: AppDimensions.screenHzPadding - 15),
                    child: ViewAllCategoryTitle(
                      title: 'Most Bought Items',
                      onTap: () {
                        HapticFeedback.lightImpact();
                      },
                    ),
                  ),
                  SizedBox(height: 8),
                  ProductGrid(productList: mostBought ?? []),
                ],
              )),
        );
      },
    );
  }
}
