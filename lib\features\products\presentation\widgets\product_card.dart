import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/widgets/custom_image.dart';
import 'package:rozana/widgets/custom_text.dart';
import 'package:rozana/widgets/shimmer_widgets.dart';

import '../../../../data/models/cart_item_model.dart';
import '../../../../domain/entities/product_entity.dart';
import '../../../../data/mappers/product_mapper.dart';
import '../../../../routes/app_router.dart';
import '../../../cart/bloc/cart_bloc.dart';
import '../../../cart/bloc/cart_event.dart';
import '../../../cart/bloc/cart_state.dart';
import '../../../cart/utils/cart_utils.dart';

class DiscountedProductCard extends StatelessWidget {
  final ProductEntity? product;
  final bool isLoading;

  const DiscountedProductCard(
      {super.key, required this.product, required this.isLoading});

  @override
  Widget build(BuildContext context) {
    final num saveAmount =
        (product?.originalPrice ?? product?.price ?? 0) - (product?.price ?? 0);

    return isLoading
        ? ShimmerBox()
        : GestureDetector(
            onTap: () {
              context.push(RouteNames.productDetail, extra: {
                'product': product != null
                    ? ProductMapper.toModel(product!).toJson()
                    : null,
              });
            },
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(5.0),
                border: Border.all(color: Color(0xFFF5F5F5), width: 1),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Top Price Section
                  Row(
                    mainAxisSize: MainAxisSize.min, // Essential to wrap content
                    children: [
                      Expanded(
                        child: Container(
                          padding:
                              EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                          decoration: BoxDecoration(
                              color: Color(0xFF388E3C), // Darker green
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(5.0),
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Color(0xFFC6C6C6),
                                  offset: Offset(0, 2),
                                  blurRadius: 8,
                                  spreadRadius: 0,
                                )
                              ]),
                          alignment: Alignment.center,
                          child: FittedBox(
                            child: RichText(
                                text: TextSpan(
                                    text: '₹ ',
                                    children: [
                                      TextSpan(
                                          text:
                                              '${(product?.price ?? 0).toStringAsFixed(0)}.',
                                          style: TextStyle(fontSize: 16)),
                                      TextSpan(
                                        text: (product?.price ?? 0)
                                            .toStringAsFixed(2)
                                            .split('.')
                                            .last,
                                      )
                                    ],
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w800,
                                    ))),
                          ),
                        ),
                      ),
                      Expanded(
                        child: Stack(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 10, vertical: 8),
                              decoration: const BoxDecoration(
                                color: Color(0xFFFFD700), // Yellow
                                borderRadius: BorderRadius.only(
                                  topRight: Radius.circular(
                                      5.0), // Creates the diagonal cut visually
                                ),
                              ),
                              child: FittedBox(
                                child: RichText(
                                    text: TextSpan(
                                        text: '₹ ',
                                        children: [
                                          TextSpan(
                                              text:
                                                  '${(product?.originalPrice ?? product?.price ?? 0).toStringAsFixed(0)}.',
                                              style: TextStyle(fontSize: 16)),
                                          TextSpan(
                                            text: (product?.originalPrice ??
                                                    product?.price ??
                                                    0)
                                                .toStringAsFixed(2)
                                                .split('.')
                                                .last,
                                          )
                                        ],
                                        style: TextStyle(
                                          color: AppColors.textPrimary,
                                          fontSize: 12,
                                          fontWeight: FontWeight.w800,
                                        ))),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(
                                  right: 6, top: 5, left: 6),
                              child:
                                  Image.asset('assets/icons/discount_mark.png'),
                            )
                          ],
                        ),
                      ),
                    ],
                  ),

                  // Product Image and Add Button (using Stack for overlay)
                  Padding(
                    padding: const EdgeInsets.fromLTRB(10, 10, 10, 0),
                    child: Stack(
                      alignment: Alignment.bottomRight,
                      children: [
                        Container(
                          height: 70,
                          decoration: BoxDecoration(
                              // color: Color(0xFFFCF1FF),
                              borderRadius: BorderRadius.circular(5)),
                          alignment: Alignment.center,
                          child: CustomImage(
                            imageUrl: product?.imageUrl,
                            height: 80,
                            width: double.infinity,
                            fit: BoxFit.contain,
                          ),
                        ),
                        BlocBuilder<CartBloc, CartState>(
                          builder: (context, state) {
                            String productId = product?.id ?? '';
                            final quantity = CartUtils.getItemQuantity(
                                productId, state.cart);
                            return quantity > 0
                                ? Container(
                                    decoration: BoxDecoration(
                                      color: Color(
                                          0xFF1D9B24), // Green for Add button
                                      borderRadius: BorderRadius.circular(3.0),
                                    ),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        // Minus button
                                        InkWell(
                                          splashColor: Colors.transparent,
                                          onTap: () async {
                                            HapticFeedback.lightImpact();
                                            final cartItemId =
                                                CartUtils.getCartItemId(
                                                    productId, state.cart);

                                            if (cartItemId != null) {
                                              if (quantity > 1) {
                                                context.read<CartBloc>().add(
                                                    CartEvent.updateQuantity(
                                                        cartItemId,
                                                        (quantity - 1)
                                                            .toInt()));
                                              } else if (quantity == 1) {
                                                context.read<CartBloc>().add(
                                                    CartEvent.removeItem(
                                                        cartItemId));
                                              }
                                            }
                                          },
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 8, vertical: 6),
                                            child: const Icon(
                                              Icons.remove,
                                              color: AppColors.surface,
                                              size: 12,
                                            ),
                                          ),
                                        ),

                                        // Quantity display
                                        CustomText(
                                          '$quantity',
                                          color: AppColors.surface,
                                          fontWeight: FontWeight.w600,
                                          fontSize: 12,
                                        ),

                                        // Plus button
                                        InkWell(
                                          splashColor: Colors.transparent,
                                          onTap: () async {
                                            HapticFeedback.lightImpact();
                                            final cartItemId =
                                                CartUtils.getCartItemId(
                                                    productId, state.cart);
                                            if (cartItemId != null) {
                                              context.read<CartBloc>().add(
                                                  CartEvent.updateQuantity(
                                                      cartItemId,
                                                      (quantity + 1).toInt()));
                                            }

                                            // widget.onAddToCart?.call();
                                          },
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 8, vertical: 6),
                                            child: const Icon(
                                              Icons.add,
                                              color: AppColors.surface,
                                              size: 16,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ))
                                : InkWell(
                                    onTap: () {
                                      HapticFeedback.lightImpact();
                                      context
                                          .read<CartBloc>()
                                          .add(CartEvent.addItem(
                                              item: CartItemModel(
                                            productId: productId,
                                            name: product?.name,
                                            price: product?.price,
                                            imageUrl: product?.imageUrl,
                                            quantity: 1,
                                            facilityId: product?.facilityId,
                                            facilityName: product?.facilityName,
                                            unit: 'item',
                                            discountedPrice: saveAmount,
                                            skuID: product?.skuID,
                                          )));
                                    },
                                    child: Container(
                                      margin:
                                          EdgeInsets.only(top: 20, left: 20),
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 8, vertical: 4),
                                      decoration: BoxDecoration(
                                        color: const Color(0xFFE4FBE5),
                                        borderRadius:
                                            BorderRadius.circular(3.0),
                                        border: Border.all(
                                            color: Color(0xFF1D9B24)),
                                      ),
                                      child: const CustomText(
                                        'Add',
                                        color: Color(0xFF1D9B24),
                                        fontWeight: FontWeight.w600,
                                        fontSize: 12,
                                      ),
                                    ),
                                  );
                          },
                        ),
                      ],
                    ),
                  ),

                  // Save Information
                  Padding(
                    padding: const EdgeInsets.fromLTRB(10, 4, 10, 4),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 2, vertical: 2),
                      decoration: BoxDecoration(
                        color: const Color(0xFFE4FBE5),
                        borderRadius: BorderRadius.circular(2.0),
                      ),
                      child: RichText(
                          text: TextSpan(
                              text:
                                  'Save ₹ ${(saveAmount).toStringAsFixed(0)}.',
                              children: [
                                TextSpan(
                                    text: (product?.price ?? 0)
                                        .toStringAsFixed(2)
                                        .split('.')
                                        .last,
                                    style: TextStyle(fontSize: 6))
                              ],
                              style: TextStyle(
                                color: Color(0xFF1D9B24),
                                fontSize: 8,
                                fontWeight: FontWeight.w800,
                              ))),
                    ),
                  ),

                  // Product Details (Name, Quantity, Rating)
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomText(
                          product?.name ?? '--',
                          fontSize: 12,
                          fontWeight: FontWeight.w800,
                        ),
                        SizedBox(height: 5),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            CustomText(
                              '100g',
                              fontSize: 10,
                              color: Color(0xFF9CA3AF),
                              fontWeight: FontWeight.w600,
                            ),
                            Expanded(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  CustomText(
                                    (product?.rating ?? 0).toStringAsFixed(1),
                                    fontSize: 10,
                                    fontWeight: FontWeight.w600,
                                  ),
                                  const SizedBox(width: 2),
                                  Padding(
                                    padding: const EdgeInsets.only(bottom: 2),
                                    child: Image.asset(
                                      'assets/icons/star.png',
                                      width: 10,
                                      height: 10,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
  }
}
